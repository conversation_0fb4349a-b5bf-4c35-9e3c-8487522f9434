## @file
# IntelSilicon Package
#
# This package provides common open source Intel silicon modules.
#
# Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
# This program and the accompanying materials are licensed and made available under
# the terms and conditions of the BSD License that accompanies this distribution.
# The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php.
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = IntelSiliconPkg
  PACKAGE_GUID                   = F7A58914-FA0E-4F71-BD6A-220FDF824A49
  PACKAGE_VERSION                = 0.1

[Includes]
  Include

[Guids]
  ## GUID for Package token space
  # {A9F8D54E-1107-4F0A-ADD0-4587E7A4A735}
  gIntelSiliconPkgTokenSpaceGuid  = { 0xa9f8d54e, 0x1107, 0x4f0a, { 0xad, 0xd0, 0x45, 0x87, 0xe7, 0xa4, 0xa7, 0x35 } }

  ## HOB GUID to publish SMBIOS data records from PEI phase
  # HOB data format is same as SMBIOS records defined in SMBIOS spec or OEM defined types
  # Generic DXE Library / Driver can locate HOB(s) and add SMBIOS records into SMBIOS table
  gIntelSmbiosDataHobGuid         = { 0x798e722e, 0x15b2, 0x4e13, { 0x8a, 0xe9, 0x6b, 0xa3, 0x0f, 0xf7, 0xf1, 0x67 }}

[PcdsFixedAtBuild, PcdsPatchableInModule, PcdsDynamic, PcdsDynamicEx]
  ## This is the GUID of the FFS which contains the Graphics Video BIOS Table (VBT)
  # The VBT content is stored as a RAW section which is consumed by GOP PEI/UEFI driver.
  # The default GUID can be updated by patching or runtime if platform support multiple VBT configurations.
  # @Prompt GUID of the FFS which contains the Graphics Video BIOS Table (VBT)
  # { 0x56752da9, 0xde6b, 0x4895, 0x88, 0x19, 0x19, 0x45, 0xb6, 0xb7, 0x6c, 0x22 }
  gIntelSiliconPkgTokenSpaceGuid.PcdIntelGraphicsVbtFileGuid|{ 0xa9, 0x2d, 0x75, 0x56, 0x6b, 0xde, 0x95, 0x48, 0x88, 0x19, 0x19, 0x45, 0xb6, 0xb7, 0x6c, 0x22 }|VOID*|0x00000001

