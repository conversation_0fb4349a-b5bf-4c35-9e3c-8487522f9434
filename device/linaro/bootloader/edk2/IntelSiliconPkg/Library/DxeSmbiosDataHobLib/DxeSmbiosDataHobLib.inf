## @file
# Component INF file for the DxeSmbiosDataHob library.
#
# Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials are licensed and made available under
# the terms and conditions of the BSD License which accompanies this distribution.
# The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
INF_VERSION   = 0x00010017
BASE_NAME     = DxeSmbiosDataHobLib
FILE_GUID     = AF55A9B6-2AE6-4B08-8CF7-750B7CBF49D7
MODULE_TYPE   = DXE_DRIVER
LIBRARY_CLASS = NULL|DXE_DRIVER
CONSTRUCTOR   = DxeSmbiosDataHobLibConstructor

[Packages]
MdePkg/MdePkg.dec
IntelSiliconPkg/IntelSiliconPkg.dec

[Sources]
DxeSmbiosDataHobLib.c

[LibraryClasses]
DebugLib
BaseMemoryLib
MemoryAllocationLib
BaseLib
HobLib
UefiLib

[Guids]
gIntelSmbiosDataHobGuid ## CONSUMES   ## GUID

[Depex]
gEfiSmbiosProtocolGuid

