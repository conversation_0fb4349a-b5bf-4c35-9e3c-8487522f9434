;------------------------------------------------------------------------------
;
; Copyright (c) 2014, Intel Corporation. All rights reserved.<BR>
; This program and the accompanying materials
; are licensed and made available under the terms and conditions of the BSD License
; which accompanies this distribution.  The full text of the license may be found at
; http://opensource.org/licenses/bsd-license.php.
;
; THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
; WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
;
; Abstract:
;
;  Reset Vector Data structure
;  This structure is located at 0xFFFFFFC0
;
;------------------------------------------------------------------------------

    .model  tiny
    .686p
    .stack  0h
    .code

;
; The layout of this file is fixed. The build tool makes assumption of the layout.
;

    ORG     0h
;
; Reserved
;
ReservedData         DD 0eeeeeeeeh, 0eeeeeeeeh

    ORG     10h
;
; This is located at 0xFFFFFFD0h
;
    mov     di, "AP"
    jmp     ApStartup

    ORG     20h
;
; Pointer to the entry point of the PEI core
; It is located at 0xFFFFFFE0, and is fixed up by some build tool
; So if the value 8..1 appears in the final FD image, tool failure occurs.
;
PeiCoreEntryPoint       DD      12345678h

;
; This is the handler for all kinds of exceptions. Since it's for debugging
; purpose only, nothing except a deadloop would be done here. Developers could
; analyze the cause of the exception if a debugger had been attached.
;
InterruptHandler    PROC
    jmp     $
    iret
InterruptHandler    ENDP

    ORG     30h
;
; For IA32, the reset vector must be at 0xFFFFFFF0, i.e., 4G-16 byte
; Execution starts here upon power-on/platform-reset.
;
ResetHandler:
    nop
    nop

ApStartup:
    ;
    ; Jmp Rel16 instruction
    ; Use machine code directly in case of the assembler optimization
    ; SEC entry point relatvie address will be fixed up by some build tool.
    ;
    ; Typically, SEC entry point is the function _ModuleEntryPoint() defined in
    ; SecEntry.asm
    ;
    DB      0e9h
    DW      -3


    ORG     38h
;
; Ap reset vector segment address is at 0xFFFFFFF8
; This will be fixed up by some build tool,
; so if the value 1..8 appears in the final FD image,
; tool failure occurs
;
ApSegAddress    dd      12345678h

    ORG     3ch
;
; BFV Base is at 0xFFFFFFFC
; This will be fixed up by some build tool,
; so if the value 1..8 appears in the final FD image,
; tool failure occurs.
;
BfvBase     DD      12345678h

;
; Nothing can go here, otherwise the layout of this file would change.
;

    END
