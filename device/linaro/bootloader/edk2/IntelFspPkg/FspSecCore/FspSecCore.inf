## @file
#  Sec Core for FSP
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspSecCore
  FILE_GUID                      = 1BA0062E-C779-4582-8566-336AE8F78F09
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  SecMain.c
  SecMain.h
  SecFsp.c
  SecFsp.h

[Sources.IA32]
  Ia32/ResetVec.asm16 | MSFT
  Ia32/Stack.asm  | MSFT
  Ia32/InitializeFpu.asm  | MSFT
  Ia32/FspApiEntry.asm  | MSFT
  Ia32/FspHelper.asm  | MSFT

  Ia32/Stacks.s | GCC
  Ia32/InitializeFpu.s | GCC
  Ia32/FspApiEntry.s | GCC
  Ia32/FspHelper.s | GCC

[Binaries.Ia32]
  RAW|Vtf0/Bin/ResetVec.ia32.raw |GCC

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  BaseLib
  PciCf8Lib
  SerialPortLib
  FspSwitchStackLib
  FspCommonLib
  FspSecPlatformLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress           ## UNDEFINED
  gIntelFspPkgTokenSpaceGuid.PcdGlobalDataPointerAddress      ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdTemporaryRamBase              ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdTemporaryRamSize              ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspTemporaryRamSize           ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspBootFirmwareVolumeBase     ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspAreaBaseAddress            ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspAreaSize                   ## CONSUMES

[FixedPcd]
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPatchEntry              ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPerfEntry               ## CONSUMES

[Ppis]
  gEfiTemporaryRamSupportPpiGuid                ## PRODUCES 

