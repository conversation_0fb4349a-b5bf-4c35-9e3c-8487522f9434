## @file
#  NULL instance of Platform Sec Lib.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspSecPlatformLibNull
  FILE_GUID                      = 03DA99B3-DDF4-4c7e-8CCA-216FC3F1F311
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspSecPlatformLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  PlatformSecLibNull.c

[Sources.IA32]
  Ia32/Flat32.asm
  Ia32/Flat32.s
  Ia32/SecCarInit.asm
  Ia32/SecCarInit.s

################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec
