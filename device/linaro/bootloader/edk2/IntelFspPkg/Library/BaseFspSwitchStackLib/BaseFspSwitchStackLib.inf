## @file
#  Instance of BaseFspSwitchStackLib
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspSwitchStackLib
  FILE_GUID                      = 8A5EA987-27F9-4ad0-B07C-D61882BFF4FF
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspSwitchStackLib

[Sources.IA32]
  FspSwitchStackLib.c

[Sources.IA32]
  Ia32/Stack.asm | MSFT
  Ia32/Stack.s | GCC

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]
  BaseLib
  IoLib

[FixedPcd]
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPatchEntry      ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPerfEntry       ## CONSUMES



