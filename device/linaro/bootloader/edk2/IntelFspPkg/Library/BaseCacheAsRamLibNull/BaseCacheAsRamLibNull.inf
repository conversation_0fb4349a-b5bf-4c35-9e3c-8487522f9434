## @file
#  NULL instance of Base cache as RAM.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseCacheAsRamLibNull
  FILE_GUID                      = FBB4A01B-947E-4d82-B27D-1E207C070053
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CacheAsRamLib

[sources.common]
  DisableCacheAsRamNull.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]

