## @file
#  Instance of BaseFspDebugLib
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspDebugLibSerialPort
  FILE_GUID                      = 9D52E46E-F07E-44e8-9A90-F8576C91C211
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DebugLib

#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  DebugLib.c

[Sources.Ia32]
  Ia32/FspDebug.asm | MSFT
  Ia32/FspDebug.s | GCC

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]
  SerialPortLib
  BaseMemoryLib
  PcdLib
  PrintLib
  BaseLib
  DebugDeviceLib
  DebugPrintErrorLevelLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugClearMemoryValue       ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask           ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdFixedDebugPrintErrorLevel   ## CONSUMES

