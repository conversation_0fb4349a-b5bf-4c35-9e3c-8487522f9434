## @file
#  Instance of FspCommonLib 
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspCommonLib
  FILE_GUID                      = 54607F66-D728-448e-A282-49E0404A557F
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspCommonLib

[Sources]
  FspCommonLib.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]
  BaseMemoryLib

[Pcd]
  gIntelFspPkgTokenSpaceGuid.PcdGlobalDataPointerAddress      ## CONSUMES

[FixedPcd]
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPatchEntry              ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPerfEntry               ## CONSUMES
