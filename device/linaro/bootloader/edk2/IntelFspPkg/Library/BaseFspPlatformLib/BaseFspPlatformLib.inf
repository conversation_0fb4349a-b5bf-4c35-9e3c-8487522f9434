## @file
# Instance of FspPlatformLib
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspPlatformLib
  FILE_GUID                      = 7DECCDAF-361F-4ec1-9714-260BAAF6F384
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspPlatformLib

[Sources]
  FspPlatformMemory.c
  FspPlatformNotify.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]
  BaseMemoryLib
  MemoryAllocationLib

[Pcd]
  gIntelFspPkgTokenSpaceGuid.PcdGlobalDataPointerAddress    ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdTemporaryRamBase            ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdTemporaryRamSize            ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspTemporaryRamSize         ## CONSUMES

[Guids]
  gFspBootLoaderTemporaryMemoryGuid                         ## PRODUCES ## HOB

[FixedPcd]
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPatchEntry        ## CONSUMES
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPerfEntry         ## CONSUMES
