
======================
= Code Contributions =
======================

To make a contribution to a TianoCore project, follow these steps.
1. Create a change description in the format specified below to
   use in the source control commit log.
2. Your commit message must include your "Signed-off-by" signature,
   and "Contributed-under" message.
3. Your "Contributed-under" message explicitly states that the
   contribution is made under the terms of the specified
   contribution agreement.  Your "Contributed-under" message
   must include the name of contribution agreement and version.
   For example: Contributed-under: TianoCore Contribution Agreement 1.0
   The "TianoCore Contribution Agreement" is included below in
   this document.
4. Submit your code to the TianoCore project using the process
   that the project documents on its web page.  If the process is
   not documented, then submit the code on development email list
   for the project.
5. It is preferred that contributions are submitted using the same
   copyright license as the base project. When that is not possible,
   then contributions using the following licenses can be accepted:
   * BSD (2-clause): http://opensource.org/licenses/BSD-2-Clause
   * BSD (3-clause): http://opensource.org/licenses/BSD-3-Clause
   * MIT: http://opensource.org/licenses/MIT
   * Python-2.0: http://opensource.org/licenses/Python-2.0
   * Zlib: http://opensource.org/licenses/Zlib

   Contributions of code put into the public domain can also be
   accepted.

   Contributions using other licenses might be accepted, but further
   review will be required.

=====================================================
= Change Description / Commit Message / Patch Email =
=====================================================

Your change description should use the standard format for a
commit message, and must include your "Signed-off-by" signature
and the "Contributed-under" message.

== Sample Change Description / Commit Message =

=== Start of sample patch email message ===

From: Contributor Name <<EMAIL>>
Subject: [PATCH] CodeModule: Brief-single-line-summary

Full-commit-message

Contributed-under: TianoCore Contribution Agreement 1.0
Signed-off-by: Contributor Name <<EMAIL>>
---

An extra message for the patch email which will not be considered part
of the commit message can be added here.

Patch content inline or attached

=== End of sample patch email message ===

=== Notes for sample patch email ===

* The first line of commit message is taken from the email's subject
  line following [PATCH]. The remaining portion of the commit message
  is the email's content until the '---' line.
* git format-patch is one way to create this format

=== Definitions for sample patch email ===

* "CodeModule" is a short idenfier for the affected code.  For
  example MdePkg, or MdeModulePkg UsbBusDxe.
* "Brief-single-line-summary" is a short summary of the change.
* The entire first line should be less than ~70 characters.
* "Full-commit-message" a verbose multiple line comment describing
  the change.  Each line should be less than ~70 characters.
* "Contributed-under" explicitely states that the contribution is
  made under the terms of the contribtion agreement.  This
  agreement is included below in this document.
* "Signed-off-by" is the contributor's signature identifying them
  by their real/legal name and their email address.

========================================
= TianoCore Contribution Agreement 1.0 =
========================================

INTEL CORPORATION ("INTEL") MAKES AVAILABLE SOFTWARE, DOCUMENTATION,
INFORMATION AND/OR OTHER MATERIALS FOR USE IN THE TIANOCORE OPEN SOURCE
PROJECT (COLLECTIVELY "CONTENT"). USE OF THE CONTENT IS GOVERNED BY THE
TERMS AND CONDITIONS OF THIS AGREEMENT BETWEEN YOU AND INTEL AND/OR THE
TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR NOTICES INDICATED OR
REFERENCED BELOW. BY USING THE CONTENT, YOU AGREE THAT YOUR USE OF THE
CONTENT IS GOVERNED BY THIS AGREEMENT AND/OR THE TERMS AND CONDITIONS
OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED
BELOW. IF YOU DO NOT AGREE TO THE TERMS AND CONDITIONS OF THIS
AGREEMENT AND THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE
AGREEMENTS OR NOTICES INDICATED OR REFERENCED BELOW, THEN YOU MAY NOT
USE THE CONTENT.

Unless otherwise indicated, all Content made available on the TianoCore
site is provided to you under the terms and conditions of the BSD
License ("BSD"). A copy of the BSD License is available at
http://opensource.org/licenses/bsd-license.php
or when applicable, in the associated License.txt file.

Certain other content may be made available under other licenses as
indicated in or with such Content. (For example, in a License.txt file.)

You accept and agree to the following terms and conditions for Your
present and future Contributions submitted to TianoCore site. Except
for the license granted to Intel hereunder, You reserve all right,
title, and interest in and to Your Contributions.

== SECTION 1: Definitions ==
* "You" or "Contributor" shall mean the copyright owner or legal
  entity authorized by the copyright owner that is making a
  Contribution hereunder. All other entities that control, are
  controlled by, or are under common control with that entity are
  considered to be a single Contributor. For the purposes of this
  definition, "control" means (i) the power, direct or indirect, to
  cause the direction or management of such entity, whether by
  contract or otherwise, or (ii) ownership of fifty percent (50%)
  or more of the outstanding shares, or (iii) beneficial ownership
  of such entity.
* "Contribution" shall mean any original work of authorship,
  including any modifications or additions to an existing work,
  that is intentionally submitted by You to the TinaoCore site for
  inclusion in, or documentation of, any of the Content. For the
  purposes of this definition, "submitted" means any form of
  electronic, verbal, or written communication sent to the
  TianoCore site or its representatives, including but not limited
  to communication on electronic mailing lists, source code
  control systems, and issue tracking systems that are managed by,
  or on behalf of, the TianoCore site for the purpose of
  discussing and improving the Content, but excluding
  communication that is conspicuously marked or otherwise
  designated in writing by You as "Not a Contribution."

== SECTION 2: License for Contributions ==
* Contributor hereby agrees that redistribution and use of the
  Contribution in source and binary forms, with or without
  modification, are permitted provided that the following
  conditions are met:
** Redistributions of source code must retain the Contributor's
   copyright notice, this list of conditions and the following
   disclaimer.
** Redistributions in binary form must reproduce the Contributor's
   copyright notice, this list of conditions and the following
   disclaimer in the documentation and/or other materials provided
   with the distribution.
* Disclaimer. None of the names of Contributor, Intel, or the names
  of their respective contributors may be used to endorse or
  promote products derived from this software without specific
  prior written permission.
* Contributor grants a license (with the right to sublicense) under
  claims of Contributor's patents that Contributor can license that
  are infringed by the Contribution (as delivered by Contributor) to
  make, use, distribute, sell, offer for sale, and import the
  Contribution and derivative works thereof solely to the minimum
  extent necessary for licensee to exercise the granted copyright
  license; this patent license applies solely to those portions of
  the Contribution that are unmodified. No hardware per se is
  licensed.
* EXCEPT AS EXPRESSLY SET FORTH IN SECTION 3 BELOW, THE
  CONTRIBUTION IS PROVIDED BY THE CONTRIBUTOR "AS IS" AND ANY
  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  CONTRIBUTOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
  OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THE
  CONTRIBUTION, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
  DAMAGE.

== SECTION 3: Representations ==
* You represent that You are legally entitled to grant the above
  license. If your employer(s) has rights to intellectual property
  that You create that includes Your Contributions, You represent
  that You have received permission to make Contributions on behalf
  of that employer, that Your employer has waived such rights for
  Your Contributions.
* You represent that each of Your Contributions is Your original
  creation (see Section 4 for submissions on behalf of others).
  You represent that Your Contribution submissions include complete
  details of any third-party license or other restriction
  (including, but not limited to, related patents and trademarks)
  of which You are personally aware and which are associated with
  any part of Your Contributions.

== SECTION 4: Third Party Contributions ==
* Should You wish to submit work that is not Your original creation,
  You may submit it to TianoCore site separately from any
  Contribution, identifying the complete details of its source
  and of any license or other restriction (including, but not
  limited to, related patents, trademarks, and license agreements)
  of which You are personally aware, and conspicuously marking the
  work as "Submitted on behalf of a third-party: [named here]".

== SECTION 5: Miscellaneous ==
* Applicable Laws. Any claims arising under or relating to this
  Agreement shall be governed by the internal substantive laws of
  the State of Delaware or federal courts located in Delaware,
  without regard to principles of conflict of laws.
* Language. This Agreement is in the English language only, which
  language shall be controlling in all respects, and all versions
  of this Agreement in any other language shall be for accommodation
  only and shall not be binding. All communications and notices made
  or given pursuant to this Agreement, and all documentation and
  support to be provided, unless otherwise noted, shall be in the
  English language.

