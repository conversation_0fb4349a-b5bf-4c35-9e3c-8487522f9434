## @file
#  PEIM for DXE IPL
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspDxeIpl
  FILE_GUID                      = 98C8588C-640A-4bb4-AEA0-3F81CDE17524
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = PeimInitializeDxeIpl

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  DxeIpl.h
  DxeIpl.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec

[LibraryClasses]
  PcdLib
  MemoryAllocationLib
  BaseMemoryLib
  ExtractGuidedSectionLib
  UefiDecompressLib
  ReportStatusCodeLib
  PeiServicesLib
  HobLib
  BaseLib
  PeimEntryPoint
  DebugLib
  FspSwitchStackLib
  UefiDecompressLib
  FspCommonLib
  FspPlatformLib

[Ppis]
  gEfiDxeIplPpiGuid                       ## PRODUCES
  gEfiEndOfPeiSignalPpiGuid               ## SOMETIMES_PRODUCES(Not produced on S3 boot path)
  gEfiPeiDecompressPpiGuid                ## CONSUMES

[Protocols]
  gEfiPciEnumerationCompleteProtocolGuid  ## PRODUCES

[Guids]
  gEfiEventReadyToBootGuid                ## PRODUCES ## Event

[FixedPcd]
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPatchEntry    ## CONSUMES 
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPerfEntry     ## CONSUMES  

[Depex]
  gEfiPeiMemoryDiscoveredPpiGuid AND gEfiPeiLoadFilePpiGuid
