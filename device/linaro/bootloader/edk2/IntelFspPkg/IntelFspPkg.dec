## @file
# Provides driver and definitions to build fsp in EDKII bios.
#
# Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
# This program and the accompanying materials are licensed and made available under
# the terms and conditions of the BSD License that accompanies this distribution.
# The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php.
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = IntelFspPkg
  PACKAGE_GUID                   = 444C6CDF-55BD-4744-8F74-AE98B003B955
  PACKAGE_VERSION                = 0.1

[Includes]
  Include
  Include/Private
  
[LibraryClasses]
  ##  @libraryclass  Provides cache-as-ram support.
  CacheAsRamLib|Include/Library/CacheAsRamLib.h

  ##  @libraryclass  Provides cache setting on MTRR.
  CacheLib|Include/Library/CacheLib.h

  ##  @libraryclass  Provides debug device abstraction.
  DebugDeviceLib|Include/Library/DebugDeviceLib.h

  ##  @libraryclass  Provides FSP related services.
  FspCommonLib|Include/Library/FspCommonLib.h

  ##  @libraryclass  Provides FSP platform related actions.
  FspPlatformLib|Include/Library/FspPlatformLib.h

  ##  @libraryclass  Provides FSP switch stack function.
  FspSwitchStackLib|Include/Library/FspSwitchStackLib.h
  
  ##  @libraryclass  Provides FSP platform sec related actions.
  FspSecPlatformLib|Include/Library/FspSecPlatformLib.h

[Guids]
  #
  # GUID defined in package
  #
  gIntelFspPkgTokenSpaceGuid           = { 0x834c0c5f, 0xadb3, 0x4372, { 0xae, 0xeb, 0x03, 0xe4, 0xe9, 0xe6, 0xc5, 0x91 } }

  # Guid define in FSP EAS
  gFspHeaderFileGuid                    = { 0x912740BE, 0x2284, 0x4734, { 0xB9, 0x71, 0x84, 0xB0, 0x27, 0x35, 0x3F, 0x0C } }
  gFspBootLoaderTemporaryMemoryGuid     = { 0xbbcff46c, 0xc8d3, 0x4113, { 0x89, 0x85, 0xb9, 0xd4, 0xf3, 0xb3, 0xf6, 0x4e } }
  gFspReservedMemoryResourceHobGuid     = { 0x69a79759, 0x1373, 0x4367, { 0xa6, 0xc4, 0xc7, 0xf5, 0x9e, 0xfd, 0x98, 0x6e } }
  gFspNonVolatileStorageHobGuid         = { 0x721acf02, 0x4d77, 0x4c2a, { 0xb3, 0xdc, 0x27, 0x0b, 0x7b, 0xa9, 0xe4, 0xb0 } }
  gFspBootLoaderTolumHobGuid            = { 0x73ff4f56, 0xaa8e, 0x4451, { 0xb3, 0x16, 0x36, 0x35, 0x36, 0x67, 0xad, 0x44 } } # FSP EAS v1.1

  # Guid defined by platform
  gFspReservedMemoryResourceHobTsegGuid = { 0xd038747c, 0xd00c, 0x4980, { 0xb3, 0x19, 0x49, 0x01, 0x99, 0xa4, 0x7d, 0x55 } }
  gFspReservedMemoryResourceHobGfxGuid  = { 0x9c7c3aa7, 0x5332, 0x4917, { 0x82, 0xb9, 0x56, 0xa5, 0xf3, 0xe6, 0x2a, 0x07 } }
  gFspReservedMemoryResourceHobMiscGuid = { 0x00d6b14b, 0x7dd0, 0x4062, { 0x88, 0x21, 0xe5, 0xf9, 0x6a, 0x2a, 0x1b, 0x00 } }

[PcdsFixedAtBuild]
  gIntelFspPkgTokenSpaceGuid.PcdGlobalDataPointerAddress |0xFED00108|UINT32|0x00000001
  gIntelFspPkgTokenSpaceGuid.PcdTemporaryRamBase         |0xFEF00000|UINT32|0x10001001
  gIntelFspPkgTokenSpaceGuid.PcdTemporaryRamSize         |    0x2000|UINT32|0x10001002
  gIntelFspPkgTokenSpaceGuid.PcdFspTemporaryRamSize      |    0x1000|UINT32|0x10001003
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPerfEntry          |        32|UINT32|0x00002001
  gIntelFspPkgTokenSpaceGuid.PcdFspMaxPatchEntry         |         6|UINT32|0x00002002
  gIntelFspPkgTokenSpaceGuid.PcdFspAreaBaseAddress       |0xFFF80000|UINT32|0x10000001
  gIntelFspPkgTokenSpaceGuid.PcdFspAreaSize              |0x00040000|UINT32|0x10000002
  gIntelFspPkgTokenSpaceGuid.PcdFspBootFirmwareVolumeBase|0xFFF80000|UINT32|0x10000003

[PcdsFixedAtBuild,PcdsDynamic,PcdsDynamicEx]
  gIntelFspPkgTokenSpaceGuid.PcdFspReservedMemoryLength |0x00100000|UINT32|0x46530000
  gIntelFspPkgTokenSpaceGuid.PcdBootLoaderEntry         |0xFFFFFFE4|UINT32|0x46530100
