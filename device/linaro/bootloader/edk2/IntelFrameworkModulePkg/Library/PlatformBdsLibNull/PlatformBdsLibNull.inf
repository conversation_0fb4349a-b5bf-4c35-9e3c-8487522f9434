## @file
#  NULL implementation for PlatformBdsLib library class interfaces.
#  
#  Copyright (c) 2007 - 2014, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#  
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformBdsLib
  MODULE_UNI_FILE                = PlatformBdsLib.uni
  FILE_GUID                      = 143B5044-7C1B-4904-9778-EA16F1F3D554
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformBdsLib|DXE_DRIVER   

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  BdsPlatform.c
  PlatformData.c
  BdsPlatform.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec
  
[LibraryClasses]
  BaseLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  BaseMemoryLib
  DebugLib
  PcdLib
  GenericBdsLib