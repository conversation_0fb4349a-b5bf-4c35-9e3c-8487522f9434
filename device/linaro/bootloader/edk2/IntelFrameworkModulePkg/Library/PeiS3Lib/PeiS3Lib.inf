## @file
#  This library provides API to invoke the S3 resume vector in the ACPI Table in S3 resume mode.
#
#  This library instance is no longer used and module using this library
#  class should update to directly locate EFI_PEI_S3_RESUME_PPI defined
#  in PI 1.2 specification. 
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PeiS3Lib
  MODULE_UNI_FILE                = PeiS3Lib.uni
  FILE_GUID                      = EFB7D3A8-DEB9-4bed-B6D6-3B09BEEB835A
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = S3Lib|PEIM

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  PeiS3Lib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec

[LibraryClasses]
  PeiServicesTablePointerLib
  DebugLib

[Ppis]
  gEfiPeiS3ResumePpiGuid                ## CONSUMES

