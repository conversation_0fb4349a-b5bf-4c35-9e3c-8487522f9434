// /** @file
// Framework Report status code library instance which supports logging message in SMM, as well as DXE & runtime phase.
//
// This library instance supports status code report in SMM, as well as DXE & runtime phase.
// In SMM, it logs message via SMM Status Code Protocol.
// Otherwise, it logs message to ReportStatusCode() in framework runtime services table or runtime report status code protocol.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Supports logging message in SMM, as well as DXE & runtime phase"

#string STR_MODULE_DESCRIPTION          #language en-US "This library instance supports the status code report in SMM, as well as DXE & runtime phase. In SMM, it logs message via the SMM Status Code Protocol. Otherwise, it logs message to ReportStatusCode() in the framework runtime services table or via the runtime report status code protocol."

