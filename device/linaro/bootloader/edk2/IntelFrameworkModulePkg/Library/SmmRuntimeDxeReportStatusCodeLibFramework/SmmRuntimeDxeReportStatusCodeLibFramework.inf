## @file
#  Framework Report status code library instance which supports logging message in SMM, as well as DXE & runtime phase.
#
#  This library instance supports status code report in SMM, as well as DXE & runtime phase.
#  In SMM, it logs message via SMM Status Code Protocol.
#  Otherwise, it logs message to ReportStatusCode() in framework runtime services table or runtime report status code protocol.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmRuntimeDxeReportStatusCodeLibFramework
  MODULE_UNI_FILE                = SmmRuntimeDxeReportStatusCodeLibFramework.uni
  FILE_GUID                      = D65D9F72-7BCE-4f73-A673-47AF446A1A31
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ReportStatusCodeLib|DXE_RUNTIME_DRIVER DXE_SMM_DRIVER
  
  CONSTRUCTOR                    = ReportStatusCodeLibConstruct
  DESTRUCTOR                     = ReportStatusCodeLibDestruct
#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  ReportStatusCodeLib.c
  SmmRuntimeDxeSupport.c
  ReportStatusCodeLibInternal.h


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  PcdLib
  BaseMemoryLib
  BaseLib
  DebugLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  DevicePathLib
  MemoryAllocationLib

[Guids]
  gEfiStatusCodeSpecificDataGuid                ## SOMETIMES_CONSUMES ## UNDEFINED
  gEfiStatusCodeDataTypeDebugGuid               ## SOMETIMES_CONSUMES ## UNDEFINED
  gEfiEventExitBootServicesGuid                 ## CONSUMES ## Event
  gEfiEventVirtualAddressChangeGuid             ## CONSUMES ## Event


[Protocols]
  gEfiStatusCodeRuntimeProtocolGuid             ## SOMETIMES_CONSUMES
  gEfiSmmBaseProtocolGuid                       ## SOMETIMES_CONSUMES
  gEfiSmmStatusCodeProtocolGuid                 ## SOMETIMES_CONSUMES

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdReportStatusCodePropertyMask ## CONSUMES

