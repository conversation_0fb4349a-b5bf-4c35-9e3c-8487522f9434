// /** @file
// PEIM Recovery Library supports system recovery boot.
//
// This library instance is no longer used and module using this library
// class should update to directly locate EFI_PEI_RECOVERY_MODULE_PPI defined
// in PI 1.2 specification.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "PEIM Recovery Library supports system recovery boot"

#string STR_MODULE_DESCRIPTION          #language en-US "This library instance is no longer used and any module using this library class should update to directly locate EFI_PEI_RECOVERY_MODULE_PPI defined in the PI 1.2 Specification."

