## @file
#  LzmaCustomDecompressLib produces LZMA custom decompression algorithm.
#
#  It is based on the LZMA SDK 16.04.
#  LZMA SDK 16.04 was placed in the public domain on 2016-10-04.
#  It was released on the http://www.7-zip.org/sdk.html website.
#
#  Copyright (c) 2009 - 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = LzmaDecompressLib
  MODULE_UNI_FILE                = LzmaDecompressLib.uni
  FILE_GUID                      = *************-44ad-9636-e44885f092d1
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = NULL
  CONSTRUCTOR                    = LzmaDecompressLibConstructor

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  LzmaDecompress.c
  Sdk/C/LzFind.c
  Sdk/C/LzmaDec.c
  Sdk/C/7zVersion.h
  Sdk/C/CpuArch.h
  Sdk/C/LzFind.h
  Sdk/C/LzHash.h
  Sdk/C/LzmaDec.h
  Sdk/C/7zTypes.h
  Sdk/C/Precomp.h
  Sdk/C/Compiler.h
  GuidedSectionExtraction.c
  UefiLzma.h
  LzmaDecompressLibInternal.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[Guids]
  gLzmaCustomDecompressGuid  ## PRODUCES  ## UNDEFINED # specifies LZMA custom decompress algorithm.

[LibraryClasses]
  BaseLib
  DebugLib
  BaseMemoryLib
  ExtractGuidedSectionLib

