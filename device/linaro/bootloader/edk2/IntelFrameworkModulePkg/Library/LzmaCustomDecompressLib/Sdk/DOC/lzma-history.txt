HISTORY of the LZMA SDK
-----------------------

16.04          2016-10-04
-------------------------
- The bug was fixed in DllSecur.c.


16.03          2016-09-28
-------------------------
- SFX modules now use some protection against DLL preloading attack.
- Some bugs in 7z code were fixed.


16.02          2016-05-21
-------------------------
- The BUG in 16.00 - 16.01 was fixed:
  Split Handler (SplitHandler.cpp) returned incorrect 
  total size value (kpidSize) for split archives.


16.01          2016-05-19
-------------------------	
- Some internal changes to reduce the number of compiler warnings.


16.00          2016-05-10
-------------------------	
- Some bugs were fixed.


15.12          2015-11-19
-------------------------	
- The BUG in C version of 7z decoder was fixed:
  7zDec.c : SzDecodeLzma2()
  7z decoder could mistakenly report about decoding error for some 7z archives
  that use LZMA2 compression method.
  The probability to get that mistaken decoding error report was about 
  one error per 16384 solid blocks for solid blocks larger than 16 KB (compressed size). 
- The BUG (in 9.26-15.11) in C version of 7z decoder was fixed:
  7zArcIn.c : SzReadHeader2()
  7z decoder worked incorrectly for 7z archives that contain 
  empty solid blocks, that can be placed to 7z archive, if some file is 
  unavailable for reading during archive creation.


15.09 beta     2015-10-16
-------------------------	
- The BUG in LZMA / LZMA2 encoding code was fixed.
  The BUG in LzFind.c::MatchFinder_ReadBlock() function.
  If input data size is larger than (4 GiB - dictionary_size),
  the following code worked incorrectly:
  -  LZMA : LzmaEnc_MemEncode(), LzmaEncode() : LZMA encoding functions 
     for compressing from memory to memory. 
     That BUG is not related to LZMA encoder version that works via streams.
  -  LZMA2 : multi-threaded version of LZMA2 encoder worked incorrectly, if 
     default value of chunk size (CLzma2EncProps::blockSize) is changed 
     to value larger than (4 GiB - dictionary_size).


9.38 beta      2015-01-03
-------------------------	
- The BUG in 9.31-9.37 was fixed:
  IArchiveGetRawProps interface was disabled for 7z archives.
- The BUG in 9.26-9.36 was fixed:
  Some code in CPP\7zip\Archive\7z\ worked correctly only under Windows.


9.36 beta      2014-12-26
-------------------------	
- The BUG in command line version was fixed:
  7-Zip created temporary archive in current folder during update archive
  operation, if -w{Path} switch was not specified. 
  The fixed 7-Zip creates temporary archive in folder that contains updated archive.
- The BUG in 9.33-9.35 was fixed:
  7-Zip silently ignored file reading errors during 7z or gz archive creation,
  and the created archive contained only part of file that was read before error.
  The fixed 7-Zip stops archive creation and it reports about error.


9.35 beta      2014-12-07
-------------------------	
- 7zr.exe now support AES encryption.
- SFX mudules were added to LZMA SDK
- Some bugs were fixed.


9.21 beta      2011-04-11
-------------------------	
- New class FString for file names at file systems.
- Speed optimization in CRC code for big-endian CPUs.
- The BUG in Lzma2Dec.c was fixed:
    Lzma2Decode function didn't work.


9.18 beta      2010-11-02
-------------------------	
- New small SFX module for installers (SfxSetup).


9.12 beta      2010-03-24
-------------------------
- The BUG in LZMA SDK 9.* was fixed: LZMA2 codec didn't work,
  if more than 10 threads were used (or more than 20 threads in some modes).


9.11 beta      2010-03-15
-------------------------
- PPMd compression method support
   

9.09           2009-12-12
-------------------------
- The bug was fixed:
   Utf16_To_Utf8 funstions in UTFConvert.cpp and 7zMain.c
   incorrectly converted surrogate characters (the code >= 0x10000) to UTF-8.
- Some bugs were fixed


9.06           2009-08-17
-------------------------
- Some changes in ANSI-C 7z Decoder interfaces.


9.04           2009-05-30
-------------------------
- LZMA2 compression method support
- xz format support


4.65           2009-02-03
-------------------------
- Some minor fixes


4.63           2008-12-31
-------------------------
- Some minor fixes


4.61 beta      2008-11-23
-------------------------
- The bug in ANSI-C LZMA Decoder was fixed:
    If encoded stream was corrupted, decoder could access memory 
    outside of allocated range.
- Some changes in ANSI-C 7z Decoder interfaces.
- LZMA SDK is placed in the public domain.


4.60 beta      2008-08-19
-------------------------
- Some minor fixes.


4.59 beta      2008-08-13
-------------------------
- The bug was fixed:
    LZMA Encoder in fast compression mode could access memory outside of 
    allocated range in some rare cases.


4.58 beta      2008-05-05
-------------------------
- ANSI-C LZMA Decoder was rewritten for speed optimizations.
- ANSI-C LZMA Encoder was included to LZMA SDK.
- C++ LZMA code now is just wrapper over ANSI-C code.


4.57           2007-12-12
-------------------------
- Speed optimizations in C++ LZMA Decoder. 
- Small changes for more compatibility with some C/C++ compilers.


4.49 beta      2007-07-05
-------------------------
- .7z ANSI-C Decoder:
     - now it supports BCJ and BCJ2 filters
     - now it supports files larger than 4 GB.
     - now it supports "Last Write Time" field for files.
- C++ code for .7z archives compressing/decompressing from 7-zip 
  was included to LZMA SDK.
  

4.43           2006-06-04
-------------------------
- Small changes for more compatibility with some C/C++ compilers.
  

4.42           2006-05-15
-------------------------
- Small changes in .h files in ANSI-C version.
  

4.39 beta      2006-04-14
-------------------------
- The bug in versions 4.33b:4.38b was fixed:
  C++ version of LZMA encoder could not correctly compress 
  files larger than 2 GB with HC4 match finder (-mfhc4).
  

4.37 beta      2005-04-06
-------------------------
- Fixes in C++ code: code could no be compiled if _NO_EXCEPTIONS was defined. 


4.35 beta      2005-03-02
-------------------------
- The bug was fixed in C++ version of LZMA Decoder:
    If encoded stream was corrupted, decoder could access memory 
    outside of allocated range.


4.34 beta      2006-02-27
-------------------------
- Compressing speed and memory requirements for compressing were increased
- LZMA now can use only these match finders: HC4, BT2, BT3, BT4


4.32           2005-12-09
-------------------------
- Java version of LZMA SDK was included


4.30           2005-11-20
-------------------------
- Compression ratio was improved in -a2 mode
- Speed optimizations for compressing in -a2 mode
- -fb switch now supports values up to 273
- The bug in 7z_C (7zIn.c) was fixed:
  It used Alloc/Free functions from different memory pools.
  So if program used two memory pools, it worked incorrectly.
- 7z_C: .7z format supporting was improved
- LZMA# SDK (C#.NET version) was included


4.27 (Updated) 2005-09-21
-------------------------
- Some GUIDs/interfaces in C++ were changed.
 IStream.h:
   ISequentialInStream::Read now works as old ReadPart
   ISequentialOutStream::Write now works as old WritePart


4.27           2005-08-07
-------------------------
- The bug in LzmaDecodeSize.c was fixed:
   if _LZMA_IN_CB and _LZMA_OUT_READ were defined,
   decompressing worked incorrectly.


4.26           2005-08-05
-------------------------
- Fixes in 7z_C code and LzmaTest.c:
  previous versions could work incorrectly,
  if malloc(0) returns 0


4.23           2005-06-29
-------------------------
- Small fixes in C++ code


4.22           2005-06-10
-------------------------
- Small fixes


4.21           2005-06-08
-------------------------
- Interfaces for ANSI-C LZMA Decoder (LzmaDecode.c) were changed
- New additional version of ANSI-C LZMA Decoder with zlib-like interface:
    - LzmaStateDecode.h
    - LzmaStateDecode.c
    - LzmaStateTest.c
- ANSI-C LZMA Decoder now can decompress files larger than 4 GB


4.17           2005-04-18
-------------------------
- New example for RAM->RAM compressing/decompressing: 
  LZMA + BCJ (filter for x86 code):
    - LzmaRam.h
    - LzmaRam.cpp
    - LzmaRamDecode.h
    - LzmaRamDecode.c
    - -f86 switch for lzma.exe


4.16           2005-03-29
-------------------------
- The bug was fixed in LzmaDecode.c (ANSI-C LZMA Decoder): 
   If _LZMA_OUT_READ was defined, and if encoded stream was corrupted,
   decoder could access memory outside of allocated range.
- Speed optimization of ANSI-C LZMA Decoder (now it's about 20% faster).
  Old version of LZMA Decoder now is in file LzmaDecodeSize.c. 
  LzmaDecodeSize.c can provide slightly smaller code than LzmaDecode.c
- Small speed optimization in LZMA C++ code
- filter for SPARC's code was added
- Simplified version of .7z ANSI-C Decoder was included


4.06           2004-09-05
-------------------------
- The bug in v4.05 was fixed:
    LZMA-Encoder didn't release output stream in some cases.


4.05           2004-08-25
-------------------------
- Source code of filters for x86, IA-64, ARM, ARM-Thumb 
  and PowerPC code was included to SDK
- Some internal minor changes


4.04           2004-07-28
-------------------------
- More compatibility with some C++ compilers


4.03           2004-06-18
-------------------------
- "Benchmark" command was added. It measures compressing 
  and decompressing speed and shows rating values. 
  Also it checks hardware errors.


4.02           2004-06-10
-------------------------
- C++ LZMA Encoder/Decoder code now is more portable
  and it can be compiled by GCC on Linux.


4.01           2004-02-15
-------------------------
- Some detection of data corruption was enabled.
    LzmaDecode.c / RangeDecoderReadByte
    .....
    {
      rd->ExtraBytes = 1;
      return 0xFF;
    }


4.00           2004-02-13
-------------------------
- Original version of LZMA SDK



HISTORY of the LZMA
-------------------
  2001-2008:  Improvements to LZMA compressing/decompressing code, 
              keeping compatibility with original LZMA format
  1996-2001:  Development of LZMA compression format

  Some milestones:

  2001-08-30: LZMA compression was added to 7-Zip
  1999-01-02: First version of 7-Zip was released
  

End of document
