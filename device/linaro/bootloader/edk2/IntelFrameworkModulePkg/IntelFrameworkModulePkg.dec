## @file
# Intel Framework Module Package.
#
# This package contains the definitions and module implementation
# which follows Intel EFI Framework Specification.
#
# Copyright (c) 2007 - 2016, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution. The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = IntelFrameworkModulePkg
  PACKAGE_UNI_FILE               = IntelFrameworkModulePkg.uni
  PACKAGE_GUID                   = *************-4822-B484-624E24B6DECF
  PACKAGE_VERSION                = 0.96

[Includes]
  Include                        # Root include for the package

[LibraryClasses]
  ##  @libraryclass  Platform BDS library definition about platform specific behavior.
  PlatformBdsLib|Include/Library/PlatformBdsLib.h

  ##  @libraryclass  Generic BDS library definition, include the data structure and function.
  GenericBdsLib|Include/Library/GenericBdsLib.h

[Guids]
  ## IntelFrameworkModule package token space guid
  #  Include/Guid/IntelFrameworkModulePkgTokenSpace.h
  gEfiIntelFrameworkModulePkgTokenSpaceGuid = { 0xD3705011, 0xBC19, 0x4af7, { 0xBE, 0x16, 0xF6, 0x80, 0x30, 0x37, 0x8C, 0x15 }}

  ## GUID identifies Data Hub records logged by Status Code Runtime Protocol.
  #  Include/Guid/DataHubStatusCodeRecord.h
  gEfiDataHubStatusCodeRecordGuid = { 0xD083E94C, 0x6560, 0x42E4, { 0xB6, 0xD4, 0x2D, 0xF7, 0x5A, 0xDF, 0x6A, 0x2A }}

  ## GUID indicates the tiano custom compress/decompress algorithm.
  #  Include/Guid/TianoDecompress.h
  gTianoCustomDecompressGuid     = { 0xA31280AD, 0x481E, 0x41B6, { 0x95, 0xE8, 0x12, 0x7F, 0x4C, 0x98, 0x47, 0x79 }}

  ## Include/Guid/AcpiVariable.h
  gEfiAcpiVariableCompatiblityGuid   = { 0xc020489e, 0x6db2, 0x4ef2, { 0x9a, 0xa5, 0xca, 0x6,  0xfc, 0x11, 0xd3, 0x6a }}

  ## Include/Guid/LegacyBios.h
  gEfiLegacyBiosGuid                 = { 0x2E3044AC, 0x879F, 0x490F, { 0x97, 0x60, 0xBB, 0xDF, 0xAF, 0x69, 0x5F, 0x50 }}
  
  ## Include/Guid/LegacyDevOrder.h
  gEfiLegacyDevOrderVariableGuid     = { 0xa56074db, 0x65fe, 0x45f7, {0xbd, 0x21, 0x2d, 0x2b, 0xdd, 0x8e, 0x96, 0x52 }}

  ## Include/Guid/CapsuleDataFile.h
  gEfiUpdateDataFileGuid             = { 0x283fa2ee, 0x532c, 0x484d, { 0x93, 0x83, 0x9f, 0x93, 0xb3, 0x6f, 0xb, 0x7e }}
  
  ## Include/Guid/BlockIoVendor.h
  gBlockIoVendorGuid                 = { 0xcf31fac5, 0xc24e, 0x11d2,  {0x85, 0xf3, 0x0, 0xa0, 0xc9, 0x3e, 0xc9, 0x3b }}
  
  ## Include/Guid/BdsHii.h
  gFrontPageFormSetGuid              = { 0x9e0c30bc, 0x3f06, 0x4ba6, {0x82, 0x88, 0x9, 0x17, 0x9b, 0x85, 0x5d, 0xbe }}
  gBootManagerFormSetGuid            = { 0x847bc3fe, 0xb974, 0x446d, {0x94, 0x49, 0x5a, 0xd5, 0x41, 0x2e, 0x99, 0x3b }}
  gDeviceManagerFormSetGuid          = { 0x3ebfa8e6, 0x511d, 0x4b5b, {0xa9, 0x5f, 0xfb, 0x38, 0x26, 0xf, 0x1c, 0x27 }}
  gDriverHealthFormSetGuid           = { 0xf76e0a70, 0xb5ed, 0x4c38, {0xac, 0x9a, 0xe5, 0xf5, 0x4b, 0xf1, 0x6e, 0x34 }}
  gBootMaintFormSetGuid              = { 0x642237c7, 0x35d4, 0x472d, {0x83, 0x65, 0x12, 0xe0, 0xcc, 0xf2, 0x7a, 0x22 }}
  gFileExploreFormSetGuid            = { 0x1f2d63e1, 0xfebd, 0x4dc7, {0x9c, 0xc5, 0xba, 0x2b, 0x1c, 0xef, 0x9c, 0x5b }}
  
  ## Include/Guid/BdsLibHii.h
  gBdsLibStringPackageGuid           = { 0x3b4d9b23, 0x95ac, 0x44f6, {0x9f, 0xcd, 0xe, 0x95, 0x94, 0x58, 0x6c, 0x72 }}
  
  ## Include/Guid/LastEnumLang.h
  gLastEnumLangGuid                  = { 0xe8c545b, 0xa2ee, 0x470d, {0x8e, 0x26, 0xbd, 0xa1, 0xa1, 0x3c, 0xa, 0xa3 }}

  ## Include/Guid/HdBootVariable.h
  gHdBootDevicePathVariablGuid       = { 0xfab7e9e1, 0x39dd, 0x4f2b, {0x84, 0x8, 0xe2, 0xe, 0x90, 0x6c, 0xb6, 0xde }}

[Protocols]
  ## Vga Mini port binding for a VGA controller
  #  Include/Protocol/VgaMiniPort.h
  gEfiVgaMiniPortProtocolGuid    = { 0xc7735a2f, 0x88f5, 0x4882, { 0xae, 0x63, 0xfa, 0xac, 0x8c, 0x8b, 0x86, 0xb3 }}

  ## ISA I/O Protocol is used to perform ISA device Io/Mem operations.
  #  Include/Protocol/IsaIo.h
  gEfiIsaIoProtocolGuid          = { 0x7ee2bd44, 0x3da0, 0x11d4, { 0x9a, 0x38, 0x0, 0x90, 0x27, 0x3f, 0xc1, 0x4d }}

  ## ISA Acpi Protocol is used to operate and communicate with ISA device.
  #  Include/Protocol/IsaAcpi.h
  gEfiIsaAcpiProtocolGuid        = { 0x64a892dc, 0x5561, 0x4536, { 0x92, 0xc7, 0x79, 0x9b, 0xfc, 0x18, 0x33, 0x55 }}

  ## OEM Badging Protocol defines the interface to get the OEM badging image with the dispaly attribute.
  #  Include/Protocol/OEMBadging.h
  gEfiOEMBadgingProtocolGuid     = { 0x170E13C0, 0xBF1B, 0x4218, { 0x87, 0x1D, 0x2A, 0xBD, 0xC6, 0xF8, 0x87, 0xBC }}

  ## Include/Protocol/ExitPmAuth.h
  gExitPmAuthProtocolGuid        = { 0xd088a413, 0xa70, 0x4217, { 0xba, 0x55, 0x9a, 0x3c, 0xb6, 0x5c, 0x41, 0xb3 }}

#
# [Error.gEfiIntelFrameworkModulePkgTokenSpaceGuid]
#   0x80000001 | Invalid value provided.
#   0x80000002 | Reserved bits must be set to zero.
#

[PcdsFeatureFlag]
  ## Indicates if OEM device is enabled as StatusCode report device.
  #  It is only used in Framework StatusCode implementation. <BR><BR>
  #   TRUE  - Enable OEM device.<BR>
  #   FALSE - Disable OEM device.<BR>
  # @Prompt Report StatusCode via OEM Device
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdStatusCodeUseOEM|FALSE|BOOLEAN|0x00010024

  ## Indicates if StatusCode report is loged into DataHub.<BR><BR>
  #   TRUE  - Log StatusCode report into DataHub.<BR>
  #   FALSE - Does not log StatusCode report into DataHub.<BR>
  # @Prompt Log StatusCode into DataHub
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdStatusCodeUseDataHub|FALSE|BOOLEAN|0x00010029

  ## Indicates if Serial device uses half hand shake.<BR><BR>
  #   TRUE  - Serial device uses half hand shake.<BR>
  #   FALSE - Serial device doesn't use half hand shake.<BR>
  # @Prompt Enable Serial device Half Hand Shake
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdIsaBusSerialUseHalfHandshake|FALSE|BOOLEAN|0x00010043

  ## Indicates if Legacy support is needed for ACPI S3 Save.<BR><BR>
  #   TRUE  - Support Legacy OS with S3 boot.<BR>
  #   FALSE - Does not support Legacy OS with S3 boot.<BR>
  # @Prompt Turn on Legacy Support in S3 Boot
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdPlatformCsmSupport|TRUE|BOOLEAN|0x00010044

  ## Indicates if Framework Acpi Support protocol is installed.<BR><BR>  
  #   TRUE  - Install Framework Acpi Support protocol.<BR>
  #   FALSE - Doesn't install Framework Acpi Support protocol.<BR>
  # @Prompt Enable Framework Acpi Support
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdInstallAcpiSupportProtocol|TRUE|BOOLEAN|0x00010046

  ## Indicates if only Boot logo is showed and all message output is disabled in BDS.<BR><BR>
  #   TRUE  - Only Boot Logo is showed in boot.<BR>
  #   FALSE - All messages and Boot Logo are showed in boot.<BR>
  # @Prompt Enable Boot Logo only
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBootlogoOnlyEnable|FALSE|BOOLEAN|0x00010048

[PcdsFixedAtBuild, PcdsPatchableInModule]
  ## FFS filename to find the default BMP Logo file.
  # @Prompt FFS Name of Boot Logo File
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLogoFile |{ 0x99, 0x8b, 0xB2, 0x7B, 0xBB, 0x61, 0xD5, 0x11, 0x9A, 0x5D, 0x00, 0x90, 0x27, 0x3F, 0xC1, 0x4D }|VOID*|0x40000003

  ## FFS filename to find the shell application.
  # @Prompt FFS Name of Shell Application
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdShellFile|{ 0xB7, 0xD6, 0x7A, 0xC5, 0x15, 0x05, 0xA8, 0x40, 0x9D, 0x21, 0x55, 0x16, 0x52, 0x85, 0x4E, 0x37 }|VOID*|0x40000004

  ## ISA Bus features to support DMA, SlaveDMA and ISA Memory. <BR><BR>
  #  BIT0 indicates if DMA is supported<BR>
  #  BIT1 indicates if only slave DMA is supported<BR>
  #  BIT2 indicates if ISA memory is supported<BR>
  #  Other BITs are reseved and must be zero.
  #  If more than one features are supported, the different BIT will be enabled at the same time.
  # @Prompt ISA Bus Features
  # @Expression 0x80000002 | (gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdIsaBusSupportedFeatures & 0xF8) == 0
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdIsaBusSupportedFeatures|0x05|UINT8|0x00010040

[PcdsDynamic, PcdsDynamicEx]
  ## Indicates if the machine has completed one boot cycle before.
  #  After the complete boot, BootState will be set to FALSE.<BR><BR>
  #   TRUE  - The complete boot cycle has not happened before.<BR>
  #   FALSE - The complete boot cycle has happened before.<BR>
  # @Prompt Boot State Flag
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBootState|TRUE|BOOLEAN|0x0001002f

[PcdsFixedAtBuild, PcdsDynamic, PcdsDynamicEx, PcdsPatchableInModule]
  ## I/O Base address of floppy device controller.
  # @Prompt I/O Base Address of Floppy Device Controller
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdFdcBaseAddress|0x3f0|UINT16|0x30000000

  ## Indicates if BiosVideo driver will switch to 80x25 Text VGA Mode when exiting boot service.<BR><BR>
  #   TRUE  - Switch to Text VGA Mode.<BR>
  #   FALSE - Does not switch to Text VGA Mode.<BR>
  # @Prompt Switch to Text VGA Mode on UEFI Boot
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBiosVideoSetTextVgaModeEnable|FALSE|BOOLEAN|0x30000001

  ## Indicates if BiosVideo driver will check for VESA BIOS Extension service support.<BR><BR>
  #   TRUE  - Check for VESA BIOS Extension service.<BR>
  #   FALSE - Does not check for VESA BIOS Extension service.<BR>
  # @Prompt Enable Check for VESA BIOS Extension Service
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBiosVideoCheckVbeEnable|TRUE|BOOLEAN|0x30000002

  ## Indicates if BiosVideo driver will check for VGA service support.
  #  NOTE: If both PcdBiosVideoCheckVbeEnable and PcdBiosVideoCheckVgaEnable are set to FALSE,
  #  that means Graphics Output protocol will not be installed, the VGA miniport protocol will be installed instead.<BR><BR>
  #   TRUE  - Check for VGA service.<BR>
  #   FALSE - Does not check for VGA service.<BR>
  # @Prompt Enable Check for VGA Service
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBiosVideoCheckVgaEnable|TRUE|BOOLEAN|0x30000003

  ## Indicates if memory space for legacy region will be set as cacheable.<BR><BR>
  #   TRUE  - Set cachebility for legacy region.<BR>
  #   FALSE - Does not set cachebility for legacy region.<BR>
  # @Prompt Enable Cachebility for Legacy Region
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLegacyBiosCacheLegacyRegion|TRUE|BOOLEAN|0x00000004

  ## Specify memory size with bytes to reserve EBDA below 640K for OPROM.
  # The value should be a multiple of 4KB. 
  # @Prompt Reserved EBDA Memory Size
  # @Expression 0x80000001 | (gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdEbdaReservedMemorySize < 0xA0000) AND ((gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdEbdaReservedMemorySize & 0x1000) == 0)
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdEbdaReservedMemorySize|0x8000|UINT32|0x30000005

  ## Specify memory base address for OPROM to find free memory.
  # Some OPROMs do not use EBDA or PMM to allocate memory for its usage, 
  # instead they find the memory filled with zero from 0x20000.
  # The value should be a multiple of 4KB.
  # The range should be below the EBDA reserved range from 
  # (CONVENTIONAL_MEMORY_TOP - Reserved EBDA Memory Size) to CONVENTIONAL_MEMORY_TOP.
  # @Prompt Reserved Memory Base Address for OPROM
  # @Expression 0x80000001 | (gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdOpromReservedMemoryBase >= 0x20000) AND ((gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdOpromReservedMemoryBase & 0x1000) == 0)
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdOpromReservedMemoryBase|0x60000|UINT32|0x3000000c
  
  ## Specify memory size with bytes for OPROM to find free memory.
  #  The value should be a multiple of 4KB. And the range should be below the EBDA reserved range from 
  # (CONVENTIONAL_MEMORY_TOP - Reserved EBDA Memory Size) to CONVENTIONAL_MEMORY_TOP.
  # @Prompt Reserved Memory Size for OPROM
  # @Expression 0x80000001 | (gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdOpromReservedMemorySize < 0x80000) AND ((gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdOpromReservedMemorySize & 0x1000) == 0)
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdOpromReservedMemorySize|0x28000|UINT32|0x3000000d

  ## Specify memory size with page number for a pre-allocated reserved memory to be used
  #  by PEI in S3 phase. The default size 32K. When changing the value make sure the memory size 
  #  is large enough to meet PEI requirement in the S3 phase.
  # @Prompt Reserved S3 Boot ACPI Memory Size
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdS3AcpiReservedMemorySize|0x8000|UINT32|0x30000006

  ## Specify the end of address below 1MB for the OPROM.
  #  The last shadowed OpROM should not exceed this address.
  # @Prompt Top Address of Shadowed Legacy OpROM
  # @Expression 0x80000001 | gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdEndOpromShadowAddress < 0x100000
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdEndOpromShadowAddress|0xdffff|UINT32|0x30000008
  
  ## Specify the low PMM (Post Memory Manager) size with bytes below 1MB.
  #  The value should be a multiple of 4KB.
  # @Prompt Low PMM (Post Memory Manager) Size
  # @Expression 0x80000001 | (gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLowPmmMemorySize < 0x100000) AND ((gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLowPmmMemorySize & 0x1000) == 0)
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLowPmmMemorySize|0x10000|UINT32|0x30000009
  
  ## Specify the high PMM (Post Memory Manager) size with bytes above 1MB.
  #  The value should be a multiple of 4KB.
  # @Prompt High PMM (Post Memory Manager) Size
  # @Expression 0x80000001 | (gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdHighPmmMemorySize & 0x1000) == 0
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdHighPmmMemorySize|0x400000|UINT32|0x3000000a

[UserExtensions.TianoCore."ExtraFiles"]
  IntelFrameworkModulePkgExtra.uni
