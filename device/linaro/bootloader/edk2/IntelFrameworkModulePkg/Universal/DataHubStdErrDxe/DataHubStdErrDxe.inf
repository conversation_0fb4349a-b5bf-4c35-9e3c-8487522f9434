## @file
#  This driver takes DEBUG info from Data Hub and writes it to StdErr if it exists.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DataHubStdErrDxe
  MODULE_UNI_FILE                = DataHubStdErrDxe.uni
  FILE_GUID                      = CA515306-00CE-4032-874E-11B755FF6866
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = DataHubStdErrInitialize

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  DataHubStdErr.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  UefiBootServicesTableLib
  BaseMemoryLib
  UefiDriverEntryPoint
  DebugLib

[Guids]
  gEfiStatusCodeDataTypeDebugGuid               ## SOMETIMES_CONSUMES ## UNDEFINED  # DataRecord Date Type
  gEfiDataHubStatusCodeRecordGuid               ## SOMETIMES_CONSUMES ## UNDEFINED  # DataRecordGuid


[Protocols]
  gEfiDataHubProtocolGuid                       ## CONSUMES

[Depex]
  gEfiDataHubProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  DataHubStdErrDxeExtra.uni
