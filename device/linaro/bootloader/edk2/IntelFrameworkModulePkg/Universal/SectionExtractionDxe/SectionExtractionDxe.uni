// /** @file
// Section Extraction Dxe Driver produces framework section extract protocol.
//
// The section is implemented as a linked list of section streams,
// where each stream contains a linked list of children, which may be leaves or
// encapsulations. Encapsulation section will further generate new section stream entries.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution.  The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Produces the framework section extract protocol"

#string STR_MODULE_DESCRIPTION          #language en-US "The section is implemented as a linked list of section streams, where each stream contains a linked list of children, which may be leaves or encapsulations. An encapsulation section will further generate new section of stream entries."

