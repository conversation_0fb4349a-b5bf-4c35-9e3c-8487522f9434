## @file
#  Section Extraction Dxe Driver produces framework section extract protocol.
#
#  The section is implemented as a linked list of section streams,
#  where each stream contains a linked list of children, which may be leaves or
#  encapsulations. Encapsulation section will further generate new section stream entries.
#  
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#  
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SectionExtractionDxe
  MODULE_UNI_FILE                = SectionExtractionDxe.uni
  FILE_GUID                      = 801ADCA0-815E-46a4-84F7-657F53621A57
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = SectionExtractionEntryPoint  

# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  SectionExtraction.c

[LibraryClasses]
  UefiBootServicesTableLib
  MemoryAllocationLib
  DebugLib
  BaseLib
  BaseMemoryLib
  UefiDriverEntryPoint
  UefiLib

[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec

[Protocols]
  gEfiSectionExtractionProtocolGuid    ## PRODUCES
  gEfiDecompressProtocolGuid           ## SOMETIMES_CONSUMES

[Depex]
  gEfiDecompressProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  SectionExtractionDxeExtra.uni
