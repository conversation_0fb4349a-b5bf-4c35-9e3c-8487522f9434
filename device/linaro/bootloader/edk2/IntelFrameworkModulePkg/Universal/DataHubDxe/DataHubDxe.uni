// /** @file
// This driver initializes and installs the Data Hub protocol.
//
// The data hub is a volatile database that is intended as the major focus for the accumulation of
// manageability data.T he hub is fed by "producers" with chunks of data in a defined format.
// Consumers may then extract the data in temporal "log" order.As an example, progress codes might
// be recorded in the data hub for future processing.Ot her data contributed to the data hub might
// include, for example, statistics on enumerated items such as memory, add-in buses, and add-in
// cards and data on errors encountered during boot (for example, the system did not boot off the
// network because the cable was not plugged in).
// Some classes of data have defined formats.For example, the amount of memory in the system is
// reported in a standard format so that consumers can be written to extract the data.O ther data is
// system specific.For example, additional detail on errors might be specific to the driver that
// discovered the error.The consumer might be a driver that tabularizes data from the data hub,
// providing a mechanism for the raw data to be made available to the OS for post-processing by
// OS-based applications.
// The intent of the data hub is for drivers that enumerate and configure parts of the system to report
// their discoveries to the data hub.This data can then be extracted by other drivers that report those
// discoveries using standard manageability interfaces such as SMBIOS and Intelligent Platform
// Management Interface (IPMI).The alternative to a data-hub-like architecture is to require all
// drivers to be aware of all reporting formats.
// For more information, please ref http://www.intel.com/technology/framework/
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Initializes and installs the Data Hub protocol"

#string STR_MODULE_DESCRIPTION          #language en-US "The data hub is a volatile database that is intended as the major focus for the accumulation of manageability data. The hub is fed by \"producers\" with chunks of data in a defined format. Consumers may then extract the data in a temporal \"log\" order. As an example, progress codes might be recorded in the data hub for future processing. For example, other data contributed to the data hub might include,  statistics on enumerated items such as memory, add-in buses, and add-in cards, and data on errors encountered during boot (such as, the system did not boot off the network because the cable was not plugged in). Some classes of data have defined formats. For example, the amount of memory in the system is reported in a standard format so that consumers can be written to extract the data. Other data is system specific. For example, additional detail on errors might be specific to the driver that discovered the error. The consumer might be a driver that tabularizes data from the data hub, providing a mechanism for the raw data to be made available to the OS for post-processing by OS-based applications. The intent of the data hub is for drivers that enumerate and configure parts of the system to report their discoveries to the data hub. This data can then be extracted by other drivers that report those discoveries using standard manageability interfaces such as SMBIOS and Intelligent Platform Management Interface (IPMI). The alternative to a data-hub-like architecture is to require all drivers to be aware of all reporting formats. For more information, refer to http://www.intel.com/technology/framework/ ."

