## @file
#  FwVolDxe Driver.
#
#  This driver produces Firmware Volume2 protocol with full services
#  (read/write, get/set) based on Firmware Volume Block protocol.
#
# Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials are
# licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FwVolDxe
  MODULE_UNI_FILE                = FwVolDxe.uni
  FILE_GUID                      = 233C2592-1CEC-494a-A097-15DC96379777
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = FwVolDriverInit

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  FwVolDriver.h
  FwPadFile.c
  Ffs.c
  FwVolWrite.c
  FwVolRead.c
  FwVolAttrib.c
  FwVol.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec


[LibraryClasses]
  UefiBootServicesTableLib
  MemoryAllocationLib
  BaseMemoryLib
  BaseLib
  UefiLib
  UefiDriverEntryPoint
  DebugLib


[Guids]
  gEfiFirmwareVolumeTopFileGuid                ## CONSUMES ## File # VTF file
  gEfiFirmwareFileSystem2Guid                  ## CONSUMES ## GUID # File System Guid
  gEfiFirmwareFileSystem3Guid                  ## CONSUMES ## GUID # File System Guid

[Protocols]
  gEfiSectionExtractionProtocolGuid            ## CONSUMES
  gEfiFirmwareVolumeBlockProtocolGuid          ## CONSUMES
  gEfiFirmwareVolume2ProtocolGuid              ## PRODUCES

[Depex]
  gEfiFirmwareVolumeBlockProtocolGuid AND gEfiSectionExtractionProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  FwVolDxeExtra.uni
