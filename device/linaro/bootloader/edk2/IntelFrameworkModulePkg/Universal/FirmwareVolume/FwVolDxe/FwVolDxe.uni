// /** @file
// FwVolDxe Driver.
//
// This driver produces Firmware Volume2 protocol with full services
// (read/write, get/set) based on Firmware Volume Block protocol.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials are
// licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution.  The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "FwVolDxe Driver"

#string STR_MODULE_DESCRIPTION          #language en-US "This driver produces the Firmware Volume2 protocol with full services (read/write, get/set) based on Firmware Volume Block protocol."

