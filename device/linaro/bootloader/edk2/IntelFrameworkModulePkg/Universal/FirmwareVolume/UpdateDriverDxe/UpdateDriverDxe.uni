// /** @file
// Update Driver for Capulse update.
//
// This driver is intended to be put in a capsule (FV). If all goes well,
// then it should be dispatched from the capsule FV, then find the image
// in the same FV and program it in a target Firmware Volume device.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials are
// licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution.  The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Update Driver for Capsule update"

#string STR_MODULE_DESCRIPTION          #language en-US "This driver is intended to be put in a capsule (FV). If all goes correctly, it should be dispatched from the capsule FV, and then it finds the image in the same FV, and programs it in a target Firmware Volume device."

