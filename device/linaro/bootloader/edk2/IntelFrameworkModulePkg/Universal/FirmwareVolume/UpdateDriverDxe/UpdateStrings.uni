/// @file
//  String definitions for UpdateDispatcher formset.
//
//  Copyright (c) 2003 - 2014, Intel Corporation. All rights reserved.<BR>
//
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
///

/=#

#langdef   en-US "English"

#string UPDATE_PROCESS_DATA            #language en-US  "Update driver loaded, processing update image\n\r"
#string UPDATE_DRIVER_DONE             #language en-US  "Done\n\r"
#string UPDATE_FLASH_RANGE             #language en-US  "Updating flash area from %08LX to %08LX ...\n\r"
#string UPDATE_DRIVER_ABORTED          #language en-US  "Aborted\n\r"
#string UPDATE_FIRMWARE_VOLUME_FILE    #language en-US  "Update firmware volume file %g ..\n\r"
#string UPDATE_FIRMWARE_VOLUME         #language en-US  "Updating whole firmware volume from %08LX to %08LX ..\n\r"
