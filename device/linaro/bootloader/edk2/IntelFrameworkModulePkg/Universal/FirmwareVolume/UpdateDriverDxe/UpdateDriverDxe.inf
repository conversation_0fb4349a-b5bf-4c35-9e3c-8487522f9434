## @file
# Update Driver for Capulse update.
#
# This driver is intended to be put in a capsule (FV). If all goes well, 
# then it should be dispatched from the capsule FV, then find the image 
# in the same FV and program it in a target Firmware Volume device.
#
# Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials are
# licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UpdateDriverDxe
  MODULE_UNI_FILE                = UpdateDriverDxe.uni
  FILE_GUID                      = 0E84FC69-29CC-4C6D-92AC-6D476921850F
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeUpdateDriver

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  UpdateDriver.h
  UpdateStrings.uni
  UpdateDispatcher.c
  ParseUpdateProfile.c
  FlashUpdate.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  BaseLib
  PrintLib
  HiiLib
  DxeServicesTableLib
  MemoryAllocationLib
  UefiLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiRuntimeServicesTableLib
  BaseMemoryLib
  DebugLib
  DevicePathLib

[Guids]
  gEfiConfigFileNameGuid                        ## CONSUMES ## File # FileName to store ConfigFile
  gEfiUpdateDataFileGuid                        ## CONSUMES ## File # FileName to store Capsule Data.

[Protocols]
  gEfiFaultTolerantWriteProtocolGuid            ## CONSUMES
  gEfiFirmwareVolume2ProtocolGuid               ## CONSUMES
  gEfiFirmwareVolumeBlockProtocolGuid           ## CONSUMES
  gEfiLoadedImageProtocolGuid                   ## CONSUMES

[Depex]
  gEfiFirmwareVolumeBlockProtocolGuid AND gEfiFaultTolerantWriteProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  UpdateDriverDxeExtra.uni
