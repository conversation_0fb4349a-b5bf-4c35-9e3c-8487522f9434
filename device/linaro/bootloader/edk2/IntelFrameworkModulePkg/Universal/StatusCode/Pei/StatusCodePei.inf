## @file
#  Status code PEIM which produces Status Code PPI.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = StatusCodePei
  MODULE_UNI_FILE                = StatusCodePei.uni
  FILE_GUID                      = 1EC0F53A-FDE0-4576-8F25-7A1A410F58EB
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = PeiStatusCodeDriverEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  StatusCodePei.c
  StatusCodePei.h
  MemoryStausCodeWorker.c
  SerialStatusCodeWorker.c


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  PeimEntryPoint
  OemHookStatusCodeLib
  PeiServicesLib
  PcdLib
  HobLib
  SerialPortLib
  ReportStatusCodeLib
  PrintLib
  DebugLib
  BaseLib


[Guids]
  gMemoryStatusCodeRecordGuid                   ## SOMETIMES_CONSUMES ## HOB
  gEfiStatusCodeDataTypeStringGuid              ## SOMETIMES_CONSUMES ## UNDEFINED # String Data Type

[Ppis]
  gEfiPeiStatusCodePpiGuid                      ## PRODUCES


[FeaturePcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdStatusCodeUseOEM ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeUseMemory ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeUseSerial ## CONSUMES


[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeMemorySize|1|gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeUseMemory  ## SOMETIMES_CONSUMES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  StatusCodePeiExtra.uni
