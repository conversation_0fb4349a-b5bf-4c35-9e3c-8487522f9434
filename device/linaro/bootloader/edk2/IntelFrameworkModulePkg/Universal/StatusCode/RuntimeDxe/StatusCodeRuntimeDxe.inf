## @file
#  Status Code Runtime Dxe driver produces Status Code Runtime Protocol.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = StatusCodeRuntimeDxe
  MODULE_UNI_FILE                = StatusCodeRuntimeDxe.uni
  FILE_GUID                      = FEDE0A1B-BCA2-4A9F-BB2B-D9FD7DEC2E9F
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = StatusCodeRuntimeDxeEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#
#  VIRTUAL_ADDRESS_MAP_CALLBACK  =  VirtualAddressChangeCallBack
#

[Sources]
  SerialStatusCodeWorker.c
  RtMemoryStatusCodeWorker.c
  DataHubStatusCodeWorker.c
  StatusCodeRuntimeDxe.h
  StatusCodeRuntimeDxe.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  OemHookStatusCodeLib
  SerialPortLib
  UefiRuntimeLib
  MemoryAllocationLib
  UefiLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  HobLib
  PcdLib
  PrintLib
  ReportStatusCodeLib
  DebugLib
  BaseMemoryLib
  BaseLib
  SynchronizationLib


[Guids]
  gEfiDataHubStatusCodeRecordGuid               ## SOMETIMES_PRODUCES ## UNDEFINED # DataRecord Guid
  gEfiStatusCodeDataTypeDebugGuid               ## SOMETIMES_PRODUCES ## UNDEFINED # Record data type
  gMemoryStatusCodeRecordGuid                   ## SOMETIMES_CONSUMES ## HOB
  gEfiEventVirtualAddressChangeGuid             ## CONSUMES ## Event
  gEfiStatusCodeDataTypeStringGuid              ## SOMETIMES_CONSUMES ## UNDEFINED

[Protocols]
  gEfiStatusCodeRuntimeProtocolGuid             ## PRODUCES
  gEfiDataHubProtocolGuid                       ## SOMETIMES_CONSUMES # Needed if Data Hub is supported for status code

[FeaturePcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeReplayIn              ## CONSUMES
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdStatusCodeUseOEM     ## CONSUMES
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdStatusCodeUseDataHub ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeUseMemory             ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeUseSerial             ## CONSUMES

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeMemorySize |128| gEfiMdeModulePkgTokenSpaceGuid.PcdStatusCodeUseMemory ## SOMETIMES_CONSUMES

[Depex]
  TRUE
[UserExtensions.TianoCore."ExtraFiles"]
  StatusCodeRuntimeDxeExtra.uni
