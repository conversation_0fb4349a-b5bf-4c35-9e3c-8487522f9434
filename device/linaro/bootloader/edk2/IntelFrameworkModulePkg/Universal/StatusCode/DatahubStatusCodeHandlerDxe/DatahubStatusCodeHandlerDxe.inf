## @file
#  Status Code Handler Driver which produces datahub handler.
#
#  Copyright (c) 2011 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DatahubStatusCodeHandlerDxe
  MODULE_UNI_FILE                = DatahubStatusCodeHandlerDxe.uni
  FILE_GUID                      = 863D214F-0920-437B-8CAD-88EA83A24E97
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0  
  ENTRY_POINT                    = DatahubStatusCodeHandlerDxeEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  DatahubStatusCodeHandlerDxe.h
  DatahubStatusCodeHandlerDxe.c
  DataHubStatusCodeWorker.c
  
[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec
  
[LibraryClasses]
  BaseLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  PcdLib
  PrintLib
  ReportStatusCodeLib
  DebugLib
  SynchronizationLib
  BaseMemoryLib
  
[Guids]
  gEfiEventExitBootServicesGuid                 ## CONSUMES ## Event
  gEfiDataHubStatusCodeRecordGuid               ## PRODUCES ## UNDEFINED # DataRecord Guid
  gEfiStatusCodeDataTypeDebugGuid               ## SOMETIMES_PRODUCES ## UNDEFINED # Record data type
  
[Protocols]
  gEfiRscHandlerProtocolGuid                    ## CONSUMES
  gEfiDataHubProtocolGuid                       ## CONSUMES
  gEfiStatusCodeRuntimeProtocolGuid             ## UNDEFINED

[FeaturePcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdStatusCodeUseDataHub ## CONSUMES

[Depex]
  gEfiRscHandlerProtocolGuid AND
  gEfiDataHubProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  DatahubStatusCodeHandlerDxeExtra.uni
