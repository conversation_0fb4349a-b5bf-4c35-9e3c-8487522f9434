## @file
#  VGA Class Driver that managers VGA devices and produces Simple Text Output Protocol.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials                          
#  are licensed and made available under the terms and conditions of the BSD License         
#  which accompanies this distribution.  The full text of the license may be found at        
#  http://opensource.org/licenses/bsd-license.php                                            
#                                                                                          
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,                     
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.   
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = VgaClassDxe
  MODULE_UNI_FILE                = VgaClassDxe.uni
  FILE_GUID                      = BF89F10D-B205-474f-96E3-7A7BB1B4A407
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeVgaClass

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#
#  DRIVER_BINDING                =  gVgaClassDriverBinding                       
#  COMPONENT_NAME                =  gVgaClassComponentName
#  COMPONENT_NAME2               =  gVgaClassComponentName2                    
#

[Sources]
  ComponentName.c
  VgaClass.h
  VgaClass.c


[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec


[LibraryClasses]
  ReportStatusCodeLib
  UefiBootServicesTableLib
  MemoryAllocationLib
  UefiLib
  UefiDriverEntryPoint
  DebugLib


[Protocols]
  gEfiSimpleTextOutProtocolGuid                 ## BY_START
  gEfiVgaMiniPortProtocolGuid                   ## TO_START
  gEfiPciIoProtocolGuid                         ## TO_START
  gEfiDevicePathProtocolGuid                    ## TO_START

[UserExtensions.TianoCore."ExtraFiles"]
  VgaClassDxeExtra.uni
