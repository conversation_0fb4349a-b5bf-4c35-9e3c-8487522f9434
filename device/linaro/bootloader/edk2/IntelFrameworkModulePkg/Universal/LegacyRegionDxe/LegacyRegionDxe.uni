// /** @file
// Produces Framework Legacy Region Protocol.
//
// This generic implementation of the Legacy Region Protocol does not actually
// perform any lock/unlock operations.  This module may be used on platforms
// that do not provide HW locking of the legacy memory regions.  It can also
// be used as a template driver for implementing the Legacy Region Protocol on
// a platform that does support HW locking of the legacy memory regions.
//
// Copyright (c) 2009 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution.  The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Produces Framework Legacy Region Protocol"

#string STR_MODULE_DESCRIPTION          #language en-US "This generic implementation of the Legacy Region Protocol does not actually perform any lock/unlock operations.  This module may be used on platforms that do not provide HW locking of the legacy memory regions.  It can also be used as a template driver for implementing the Legacy Region Protocol on a platform that does support HW locking of the legacy memory regions."

