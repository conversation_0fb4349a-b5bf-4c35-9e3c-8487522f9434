## @file
#  Produces Framework Legacy Region Protocol.
#
#  This generic implementation of the Legacy Region Protocol does not actually 
#  perform any lock/unlock operations.  This module may be used on platforms 
#  that do not provide HW locking of the legacy memory regions.  It can also 
#  be used as a template driver for implementing the Legacy Region Protocol on
#  a platform that does support HW locking of the legacy memory regions.
#
# Copyright (c) 2009 - 2014, Intel Corporation. All rights reserved.<BR>
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#                                                                                           
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = LegacyRegionDxe
  MODULE_UNI_FILE                = LegacyRegionDxe.uni
  FILE_GUID                      = 8C439043-85CA-467a-96F1-CB14F4D0DCDA
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = LegacyRegionInstall

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  LegacyRegion.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  DebugLib
  UefiBootServicesTableLib

[Protocols]
  gEfiLegacyRegionProtocolGuid                 ## PRODUCES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  LegacyRegionDxeExtra.uni
