// /** @file
// Module that produces the Framework CPU I/O Protocol using the services of the I/O Library
//
// The module that produces the Framework CPU I/O Protocol using the services of the I/O Library.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Produces the Framework CPU I/O Protocol using the services of the I/O Library"

#string STR_MODULE_DESCRIPTION          #language en-US "The module that produces the Framework CPU I/O Protocol using the services of the I/O Library."

