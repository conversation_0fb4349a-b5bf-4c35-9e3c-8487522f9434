## @file
#  Module that produces the Framework CPU I/O Protocol using the services of the I/O Library
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CpuIoDxe
  MODULE_UNI_FILE                = CpuIoDxe.uni
  FILE_GUID                      = BAE7599F-3C6B-43b7-BDF0-9CE07AA91AA6
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CpuIoInitialize

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  CpuIo.c
  CpuIo.h

[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  BaseLib
  DebugLib
  IoLib
  UefiBootServicesTableLib

[Protocols]
  gEfiCpuIoProtocolGuid                         ## PRODUCES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  CpuIoDxeExtra.uni
