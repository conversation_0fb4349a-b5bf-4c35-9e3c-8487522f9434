## @file
# AcpiS3Save module installs ACPI S3 Save protocol to prepare S3 boot data.
#
# Copyright (c) 2006 - 2016, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials are
# licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AcpiS3SaveDxe
  MODULE_UNI_FILE                = AcpiS3SaveDxe.uni
  FILE_GUID                      = 2BDED685-F733-455f-A840-43A22B791FB3
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InstallAcpiS3Save

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  AcpiS3Save.h
  AcpiS3Save.c
  AcpiVariableThunkPlatform.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  PcdLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  BaseMemoryLib
  HobLib
  UefiLib
  DebugLib

[Guids]
  ## SOMETIMES_CONSUMES ## Variable:L"AcpiGlobalVariable"
  ## SOMETIMES_PRODUCES ## Variable:L"AcpiGlobalVariable"
  gEfiAcpiVariableCompatiblityGuid

[Protocols]
  gEfiAcpiS3SaveProtocolGuid                    ## PRODUCES
  gFrameworkEfiMpServiceProtocolGuid            ## SOMETIMES_CONSUMES
  ## NOTIFY
  ## SOMETIMES_CONSUMES
  gEdkiiVariableLockProtocolGuid

[FeaturePcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdPlatformCsmSupport          ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdFrameworkCompatibilitySupport          ## CONSUMES

[Pcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdS3AcpiReservedMemorySize    ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiS3Enable                           ## CONSUMES

[Depex]
  #
  # Note: the extra dependency of gEfiMpServiceProtocolGuid is to ensure that ACPI variable is set by MpDxe driver before
  # AcpiS3SaveDxe module is executed. 
  #
  gEfiVariableArchProtocolGuid AND gEfiVariableWriteArchProtocolGuid AND gEfiMpServiceProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  AcpiS3SaveDxeExtra.uni
