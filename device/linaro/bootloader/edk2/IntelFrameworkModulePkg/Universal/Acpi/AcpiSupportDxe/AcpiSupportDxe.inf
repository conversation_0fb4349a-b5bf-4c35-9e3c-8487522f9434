## @file
# Acpi Support Dirver to install Framework Acpi Support Protocol.
#
# This driver initializes ACPI support protocol instance data structure and intstall 
# ACPI support protocol to provide Get, Set and Publish Table services.
#
# Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions
# of the BSD License which accompanies this distribution.  The
# full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AcpiSupportDxe
  MODULE_UNI_FILE                = AcpiSupportDxe.uni
  FILE_GUID                      = 506533a6-e626-4500-b14f-17939c0e5b60
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InstallAcpiSupport

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  AcpiSupportAcpiSupportProtocol.c
  AcpiSupport.h
  AcpiSupport.c


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec

[LibraryClasses]
  UefiBootServicesTableLib
  MemoryAllocationLib
  UefiDriverEntryPoint
  BaseMemoryLib
  UefiLib
  DebugLib
  BaseLib
  PcdLib


[Guids]
  gEfiAcpi10TableGuid                           ## PRODUCES ## SystemTable
  gEfiAcpiTableGuid                             ## PRODUCES ## SystemTable

[FeaturePcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdInstallAcpiSupportProtocol ## CONSUMES

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiDefaultOemId            ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiDefaultOemTableId       ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiDefaultOemRevision      ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiDefaultCreatorId        ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiDefaultCreatorRevision  ## CONSUMES

[Protocols]
  gEfiAcpiTableProtocolGuid                     ## PRODUCES
  gEfiAcpiSupportProtocolGuid                   ## SOMETIMES_PRODUCES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  AcpiSupportDxeExtra.uni
