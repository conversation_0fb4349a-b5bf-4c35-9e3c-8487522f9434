## @file
#  BDSDxe module is core driver for BDS phase.
#
#  When <PERSON>xe<PERSON><PERSON> dispatching all DXE driver, this module will produce architecture protocol 
#  gEfiBdsArchProtocolGuid. After <PERSON><PERSON><PERSON><PERSON> finish dispatching, <PERSON><PERSON><PERSON><PERSON> will invoke Entry
#  interface of protocol gEfiBdsArchProtocolGuid, then BDS phase is entered.
#
#  Generally, this module take reposiblity to connect all necessary devices for platform boot, 
#  these boot device path are hold in PlatformBdsLib library instance produced by platform.
#  For legacy boot, BDS will transfer control to legacy BIOS after legacy boot device is select.
#  For EFI boot, BDS will load boot loader file EFI\BOOT\BOOTIA32.EFI, EFI\BOOT\BOOTX64.EFI, 
#  EFI\BOOT\BOOTIA64.EFI file from selected boot device and transfer control to boot loader.
#
#  BDSDxe also maintain the UI for "Boot Manager, Boot Maintaince Manager, Device Manager" which
#  is used for user to configure boot option or maintain hardware device.
#  
#  Copyright (c) 2008 - 2015, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#  
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BdsDxe
  MODULE_UNI_FILE                = BdsDxe.uni
  FILE_GUID                      = FC5C7020-1A48-4198-9BE2-EAD5ABC8CF2F
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0 
  ENTRY_POINT                    = BdsInitialize

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  FrontPage.h
  Language.h
  Bds.h
  Hotkey.h
  BootMaint/BBSsupport.h
  BootMngr/BootManager.h
  BootMaint/BootMaint.h
  String.h
  BootMaint/FormGuid.h
  HwErrRecSupport.c
  HwErrRecSupport.h

  DeviceMngr/DeviceManager.h
  DeviceMngr/DeviceManagerVfr.h
  DeviceMngr/DeviceManagerVfr.Vfr
  DeviceMngr/DriverHealthVfr.Vfr
  DeviceMngr/DeviceManagerStrings.uni
  DeviceMngr/DeviceManager.c
  BootMngr/BootManagerVfr.Vfr
  BootMngr/BootManagerStrings.uni
  BootMngr/BootManager.c
  BootMaint/FE.vfr
  BootMaint/FileExplorer.c
  BootMaint/BootMaint.c
  BootMaint/BBSsupport.c
  BootMaint/UpdatePage.c
  BootMaint/Variable.c
  BootMaint/Data.c
  BootMaint/ConsoleOption.c
  BootMaint/BootOption.c
  BootMaint/BmLib.c
  BootMaint/Bm.vfr
  BootMaint/Bmstring.uni
  Hotkey.c
  MemoryTest.c
  Capsules.c
  Strings.uni
  String.c
  Language.c
  FrontPageVfr.Vfr
  FrontPageStrings.uni
  FrontPage.c
  BdsEntry.c


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec
  
[LibraryClasses]
  DevicePathLib
  BaseLib
  HobLib
  UefiRuntimeServicesTableLib
  GenericBdsLib
  ReportStatusCodeLib
  PerformanceLib
  MemoryAllocationLib
  UefiLib
  UefiBootServicesTableLib
  BaseMemoryLib
  DebugLib
  PrintLib
  HiiLib
  UefiDriverEntryPoint
  PlatformBdsLib
  CapsuleLib
  PcdLib
  UefiHiiServicesLib
  
[Guids]
  ## SOMETIMES_PRODUCES ## Variable:L"BootXXXX"          # Boot option variable
  ## SOMETIMES_PRODUCES ## Variable:L"DriverXXXX"        # Driver load option.
  ## SOMETIMES_PRODUCES ## Variable:L"PlatformLang"      # Platform supported languange in Rfc4646 format
  ## SOMETIMES_PRODUCES ## Variable:L"Lang"              # Platform supported languange in Iso639 format
  ## SOMETIMES_PRODUCES ## Variable:L"LangCodes"         # Value of PcdUefiVariableDefaultLangCodes
  ## PRODUCES           ## Variable:L"PlatformLangCodes" # Value of PcdUefiVariableDefaultPlatformLangCodes
  ## SOMETIMES_PRODUCES ## Variable:L"KeyXXXX"  # Hotkey option variable
  ## PRODUCES           ## Variable:L"HwErrRecSupport"   # The level of platform supported hardware Error Record Persistence
  ## PRODUCES           ## Variable:L"Timeout"     # The time out value in second of showing progress bar
  ## SOMETIMES_PRODUCES ## Variable:L"BootOptionSupport" # The feature supported in boot option menu, value could be: EFI_BOOT_OPTION_SUPPORT_KEY, EFI_BOOT_OPTION_SUPPORT_APP
  ## SOMETIMES_PRODUCES ## Variable:L"BootOrder"   # The boot option array
  ## SOMETIMES_PRODUCES ## Variable:L"DriverOrder" # The driver order list
  ## SOMETIMES_CONSUMES ## Variable:L"ConIn"  # The device path of console in device
  ## SOMETIMES_PRODUCES ## Variable:L"ConIn"  # The device path of console in device
  ## SOMETIMES_CONSUMES ## Variable:L"ConOut" # The device path of console out device
  ## SOMETIMES_PRODUCES ## Variable:L"ConOut" # The device path of console out device
  ## SOMETIMES_CONSUMES ## Variable:L"ErrOut" # The device path of error out device
  ## SOMETIMES_PRODUCES ## Variable:L"ErrOut" # The device path of error out device
  ## SOMETIMES_CONSUMES ## Variable:L"ConInDev"  # The device path of console in device
  ## SOMETIMES_CONSUMES ## Variable:L"ConOutDev" # The device path of console out device
  ## SOMETIMES_CONSUMES ## Variable:L"ErrOutDev" # The device path of error out device
  ## SOMETIMES_PRODUCES ## Variable:L"BootNext"  # The number of next boot option
  gEfiGlobalVariableGuid
  gEfiFileSystemVolumeLabelInfoIdGuid           ## SOMETIMES_CONSUMES ## UNDEFINED # Indicate the information type is volume
  gEfiFileInfoGuid                              ## SOMETIMES_CONSUMES ## UNDEFINED # Indicate the information type is file
  gEfiHiiPlatformSetupFormsetGuid               ## SOMETIMES_CONSUMES ## UNDEFINED # Indicate the formset class guid to be displayed
  gEfiIfrTianoGuid                              ## SOMETIMES_PRODUCES ## UNDEFINED # Extended IFR Guid Opcode
  gEfiHiiDriverHealthFormsetGuid                ## SOMETIMES_CONSUMES ## UNDEFINED # Indicate the Driver Health formset class guid to be displayed
  ## SOMETIMES_PRODUCES ## Variable:L"LegacyDevOrder"
  ## SOMETIMES_CONSUMES ## Variable:L"LegacyDevOrder"
  gEfiLegacyDevOrderVariableGuid
  gFrontPageFormSetGuid                         ## SOMETIMES_CONSUMES ## HII # FrontPage HII Package
  gBootMaintFormSetGuid                         ## SOMETIMES_CONSUMES ## HII # BootMaint HII Package
  gFileExploreFormSetGuid                       ## SOMETIMES_CONSUMES ## HII # FileExplore HII Package
  gBootManagerFormSetGuid                       ## SOMETIMES_CONSUMES ## HII # BootManager HII Package
  gDeviceManagerFormSetGuid                     ## SOMETIMES_CONSUMES ## HII # DeviceManager HII Package
  gDriverHealthFormSetGuid                      ## SOMETIMES_CONSUMES ## HII # DriverHealth HII Package
  ## SOMETIMES_PRODUCES ## Event
  ## SOMETIMES_CONSUMES ## Event
  gConnectConInEventGuid
  gEfiFmpCapsuleGuid                            ## SOMETIMES_CONSUMES ## GUID # FMP Capsule
  gEdkiiStatusCodeDataTypeVariableGuid          ## SOMETIMES_CONSUMES ## GUID
  gEfiUartDevicePathGuid                        ## SOMETIMES_CONSUMES ## GUID (Identify the device path for UARD device)
  gPerformanceProtocolGuid                      ## SOMETIMES_PRODUCES ## Variable:L"PerfDataMemAddr" (The ACPI address of performance data)

[Protocols]
  gEfiSimpleFileSystemProtocolGuid              ## SOMETIMES_CONSUMES
  gEfiLoadFileProtocolGuid                      ## SOMETIMES_CONSUMES
  gEfiBdsArchProtocolGuid                       ## PRODUCES
  gEfiSmbiosProtocolGuid                        ## CONSUMES
  gEfiGenericMemTestProtocolGuid                ## SOMETIMES_CONSUMES
  gEfiLegacyBiosProtocolGuid                    ## SOMETIMES_CONSUMES
  gEfiUgaDrawProtocolGuid |gEfiMdePkgTokenSpaceGuid.PcdUgaConsumeSupport ## SOMETIMES_CONSUMES
  gEfiBlockIoProtocolGuid                       ## SOMETIMES_CONSUMES
  gEfiGraphicsOutputProtocolGuid                ## SOMETIMES_CONSUMES
  ## CONSUMES
  ## NOTIFY
  gEfiSimpleTextInputExProtocolGuid             
  gEfiHiiConfigAccessProtocolGuid               ## SOMETIMES_PRODUCES
  gEfiFormBrowser2ProtocolGuid                  ## CONSUMES
  gEfiSerialIoProtocolGuid                      ## SOMETIMES_CONSUMES
  gEfiDevicePathProtocolGuid                    ## CONSUMES
  gEfiDriverHealthProtocolGuid                  ## SOMETIMES_CONSUMES
  gEfiPciIoProtocolGuid                         ## SOMETIMES_CONSUMES
  gEfiBootLogoProtocolGuid                      ## SOMETIMES_CONSUMES
  gEdkiiVariableLockProtocolGuid                ## SOMETIMES_CONSUMES

[FeaturePcd]
  gEfiMdePkgTokenSpaceGuid.PcdUefiVariableDefaultLangDeprecate    ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUgaConsumeSupport                   ## CONSUMES
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBootlogoOnlyEnable ## CONSUMES

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdUefiVariableDefaultLangCodes          ## SOMETIMES_CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUefiVariableDefaultLang               ## SOMETIMES_CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUefiVariableDefaultPlatformLangCodes  ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUefiVariableDefaultPlatformLang       ## SOMETIMES_CONSUMES
  ## CONSUMES
  ## PRODUCES
  gEfiMdePkgTokenSpaceGuid.PcdHardwareErrorRecordLevel
  gEfiMdeModulePkgTokenSpaceGuid.PcdConOutRow     ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdConOutColumn  ## PRODUCES
  ## SOMETIMES_CONSUMES
  ## SOMETIMES_PRODUCES
  gEfiMdePkgTokenSpaceGuid.PcdPlatformBootTimeOut
  ## CONSUMES
  ## PRODUCES
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBootState
  gEfiMdeModulePkgTokenSpaceGuid.PcdFirmwareVendor   ## CONSUMES   
  gEfiMdeModulePkgTokenSpaceGuid.PcdFirmwareRevision ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdVideoHorizontalResolution  ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdVideoVerticalResolution    ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdConInConnectOnDemand       ## SOMETIMES_CONSUMES
  ## CONSUMES
  ## SOMETIMES_PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupConOutColumn
  ## CONSUMES
  ## SOMETIMES_PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupConOutRow
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupVideoHorizontalResolution            ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupVideoVerticalResolution              ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdErrorCodeSetVariable                      ## CONSUMES

[Depex]
  TRUE

#
# [BootMode] 
#   FLASH_UPDATE    ## SOMETIMES_CONSUMES # Update Capsule Image
#

[UserExtensions.TianoCore."ExtraFiles"]
  BdsDxeExtra.uni
