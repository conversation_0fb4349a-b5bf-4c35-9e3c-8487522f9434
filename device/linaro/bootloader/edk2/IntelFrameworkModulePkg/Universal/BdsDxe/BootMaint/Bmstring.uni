///** @file
//  
//    String definitions for Boot Maintenance Utility.
//  
//  Copyright (c) 2004 - 2010, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

/=#

#langdef   en-US "English"
#langdef   fr-FR "Français"

#string STR_NULL_STRING                #language en-US  " "
                                       #language fr-FR  " "
#string STR_NONE                       #language en-US  "NONE"
                                       #language fr-FR  "NONE"
#string STR_FORM_MAIN_TITLE            #language en-US  "Boot Maintenance Manager"
                                       #language fr-FR  "Boot Maintenance Manager"
#string STR_FORM_BOOT_SETUP_TITLE      #language en-US  "Boot Options"
                                       #language fr-FR  "Boot Options"
#string STR_FORM_BOOT_SETUP_HELP       #language en-US  "Modify system boot options"
                                       #language fr-FR  "Modify system boot options"
#string STR_FORM_DRIVER_SETUP_TITLE    #language en-US  "Driver Options"
                                       #language fr-FR  "Driver Options"
#string STR_FORM_DRIVER_SETUP_HELP     #language en-US  "Modify boot driver options"
                                       #language fr-FR  "Modify boot driver options"
#string STR_FORM_BOOT_ADD_TITLE        #language en-US  "Add Boot Option"
                                       #language fr-FR  "Add Boot Option"
#string STR_FORM_BOOT_ADD_HELP         #language en-US  "Add EFI Application or Removable Fs as Boot Option"
                                       #language fr-FR  "Add EFI Application or Removable Fs as Boot Option"
#string STR_FORM_BOOT_DEL_TITLE        #language en-US  "Delete Boot Option"
                                       #language fr-FR  "Delete Boot Option"
#string STR_FORM_BOOT_IMMEDIATE_HELP   #language en-US  "Will be valid immediately"
                                       #language fr-FR  "Will be valid immediately"
#string STR_FORM_BOOT_CHG_TITLE        #language en-US  "Change Boot Order"
                                       #language fr-FR  "Change Boot Order"
#string STR_FORM_DRV_ADD_TITLE         #language en-US  "Add Driver Option"
                                       #language fr-FR  "Add Driver Option"
#string STR_FORM_DRV_ADD_HELP          #language en-US  "Add .EFI Driver as Driver Option"
                                       #language fr-FR  "Add .EFI Driver as Driver Option"
#string STR_FORM_DRV_DEL_TITLE         #language en-US  "Delete Driver Option"
                                       #language fr-FR  "Delete Driver Option"
#string STR_FORM_DRV_CHG_TITLE         #language en-US  "Change Driver Order"
                                       #language fr-FR  "Change Driver Order"
#string STR_FORM_NEXT_BOOT_HELP        #language en-US  "Will be valid on next boot"
                                       #language fr-FR  "Will be valid on next boot"
#string STR_FORM_BOOT_NEXT_TITLE       #language en-US  "Set Boot Next Value"
                                       #language fr-FR  "Set Boot Next Value"
#string STR_FORM_BOOT_NEXT_HELP        #language en-US  "Modify next boot behavior"
                                       #language fr-FR  "Modify next boot behavior"
#string STR_FORM_TIME_OUT_TITLE        #language en-US  "Set Time Out Value"
                                       #language fr-FR  "Set Time Out Value"
#string STR_FORM_TIME_OUT_HELP         #language en-US  "Modify automatic boot time-out value"
                                       #language fr-FR  "Modify automatic boot time-out value"
#string STR_FORM_CON_MAIN_TITLE        #language en-US  "Console Options"
                                       #language fr-FR  "Console Options"
#string STR_FORM_CON_MAIN_HELP         #language en-US  "Modify system console options"
                                       #language fr-FR  "Modify system console options"
#string STR_FORM_CON_IN_TITLE          #language en-US  "Console Input Device Select"
                                       #language fr-FR  "Console Input Device Select"
#string STR_FORM_CON_IN_HELP           #language en-US  "Enable console device as ConIn"
                                       #language fr-FR  "Enable console device as ConIn"
#string STR_FORM_SET_FD_ORDER_TITLE    #language en-US  "Set Legacy Floppy Drive Order"
                                       #language fr-FR  "Set Legacy Floppy Drive Order"
#string STR_FORM_SET_HD_ORDER_TITLE    #language en-US  "Set Legacy HardDisk Drive Order"
                                       #language fr-FR  "Set Legacy HardDisk Drive Order"
#string STR_FORM_SET_CD_ORDER_TITLE    #language en-US  "Set Legacy CD-ROM Drive Order"
                                       #language fr-FR  "Set Legacy CD-ROM Drive Order"
#string STR_FORM_SET_NET_ORDER_TITLE   #language en-US  "Set Legacy NET Drive Order"
                                       #language fr-FR  "Set Legacy NET Drive Order"
#string STR_FORM_SET_BEV_ORDER_TITLE   #language en-US  "Set Legacy BEV Drive Order"
                                       #language fr-FR  "Set Legacy BEV Drive Order"
#string STR_FORM_GOTO_SETTING          #language en-US  "Go Back To Setting Page"
                                       #language fr-FR  "Go Back To Setting Page"
#string STR_COM1                       #language en-US  "COM1"
                                       #language fr-FR  "COM1"
#string STR_COM2                       #language en-US  "COM2"
                                       #language fr-FR  "COM2"
#string STR_COM_AS_CONSOLE_OPTION      #language en-US  "Select this COM port as Console"
                                       #language fr-FR  "Select this COM port as Console"
#string STR_FORM_CON_OUT_TITLE         #language en-US  "Console Output Device Select"
                                       #language fr-FR  "Console Output Device Select"
#string STR_FORM_CON_OUT_HELP          #language en-US  "Enable console device as ConOut"
                                       #language fr-FR  "Enable console device as ConOut"
#string STR_FORM_STD_ERR_TITLE         #language en-US  "Console Standard Error Device Select"
                                       #language fr-FR  "Console Standard Error Device Select"
#string STR_FORM_STD_ERR_HELP          #language en-US  "Enable console device as StdErr"
                                       #language fr-FR  "Enable console device as StdErr"
#string STR_FORM_MODE_TITLE            #language en-US  "Console Output Mode Select"
                                       #language fr-FR  "Console Output Mode Select"
#string STR_FORM_MODE_HELP             #language en-US  "Select Console Output Mode: 80x25, 100x31, etc."
                                       #language fr-FR  "Select Console Output Mode: 80x25, 100x31, etc."
#string STR_FORM_COM_TITLE             #language en-US  "COM Attribute Setup Page"
                                       #language fr-FR  "COM Attribute Setup Page"
#string STR_FORM_COM_HELP              #language en-US  "Setup ComPort BaudRate, DataBits, StopBits, Parity and TerminalType"
                                       #language fr-FR  "Setup ComPort BaudRate, DataBits, StopBits, Parity and TerminalType"
#string STR_FORM_DRV_ADD_FILE_TITLE    #language en-US  "Add Driver Option Using File"
                                       #language fr-FR  "Add Driver Option Using File"
#string STR_FORM_DRV_ADD_HANDLE_TITLE  #language en-US  "Add Driver Option Using Handle"
                                       #language fr-FR  "Add Driver Option Using Handle"
#string STR_FORM_BOOT_ADD_DESC_TITLE   #language en-US  "Modify Boot Option Description"
                                       #language fr-FR  "Modify Boot Option Description"
#string STR_FORM_DRV_ADD_DESC_TITLE    #language en-US  "Modify Driver Option Description"
                                       #language fr-FR  "Modify Driver Option Description"
#string STR_NUM_AUTO_BOOT              #language en-US  "Auto Boot Time-out"
                                       #language fr-FR  "Auto Boot Time-out"
#string STR_HLP_AUTO_BOOT              #language en-US  "Range: 0 to 65535 seconds, 0 means no wait, 65535 means waiting for key"
                                       #language fr-FR  "Range: 0 to 65535 seconds, 0 means no wait, 65535 means waiting for key"
#string STR_BOOT_NEXT                  #language en-US  "Boot Next Value"
                                       #language fr-FR  "Boot Next Value"
#string STR_BOOT_NEXT_HELP             #language en-US  "Next boot use this boot option"
                                       #language fr-FR  "Next boot use this boot option"
#string STR_LOAD_OPTION_DEVPATH        #language en-US  "This is the devicepath"
                                       #language fr-FR  "This is the devicepath"
#string STR_LOAD_OPTION_DESC           #language en-US  "Input the description"
                                       #language fr-FR  "Input the description"
#string STR_LOAD_OPTION_ACTIVE         #language en-US  "Load Option Active"
                                       #language fr-FR  "Load Option Active"
#string STR_LOAD_OPTION_FORCE_RECON    #language en-US  "Load Option Reconnect"
                                       #language fr-FR  "Load Option Reconnect"
#string STR_SAVE_AND_EXIT              #language en-US  "Commit Changes and Exit"
                                       #language fr-FR  "Commit Changes and Exit"
#string STR_NO_SAVE_AND_EXIT           #language en-US  "Discard Changes and Exit"
                                       #language fr-FR  "Discard Changes and Exit"
#string STR_CON_IN_SETUP               #language en-US  "Set Console Input Device"
                                       #language fr-FR  "Set Console Input Device"
#string STR_CON_OUT_SETUP              #language en-US  "Set Console Output Device"
                                       #language fr-FR  "Set Console Output Device"
#string STR_CON_ERR_SETUP              #language en-US  "Set Error Output Device"
                                       #language fr-FR  "Set Error Output Device"
#string STR_CON_MODE_SETUP             #language en-US  "Set Console Output Mode"
                                       #language fr-FR  "Set Console Output Mode"
#string STR_CON_COM_SETUP              #language en-US  "Set COM Attributes"
                                       #language fr-FR  "Set COM Attributes"
#string STR_COM_TERMI_TYPE             #language en-US  "Set COM Terminal Type"
                                       #language fr-FR  "Set COM Terminal Type"
#string STR_COM_FLOW_CONTROL           #language en-US  "Set COM Flow Control"
                                       #language fr-FR  "Set COM Flow Control"
#string STR_COM_BAUD_RATE              #language en-US  "Set COM Baud Rate"
                                       #language fr-FR  "Set COM Baud Rate"
#string STR_COM_DATA_BITS              #language en-US  "Set COM Data Bits"
                                       #language fr-FR  "Set COM Data Bits"
#string STR_COM_PARITY                 #language en-US  "Set COM Parity"
                                       #language fr-FR  "Set COM Parity"
#string STR_COM_STOP_BITS              #language en-US  "Set COM Stop Bits"
                                       #language fr-FR  "Set COM Stop Bits"
#string STR_COM_BAUD_RATE_0            #language en-US  "115200"
                                       #language fr-FR  "115200"
#string STR_COM_BAUD_RATE_1            #language en-US  "57600"
                                       #language fr-FR  "57600"
#string STR_COM_BAUD_RATE_2            #language en-US  "38400"
                                       #language fr-FR  "38400"
#string STR_COM_BAUD_RATE_3            #language en-US  "19200"
                                       #language fr-FR  "19200"
#string STR_COM_BAUD_RATE_4            #language en-US  "9600"
                                       #language fr-FR  "9600"
#string STR_COM_BAUD_RATE_5            #language en-US  "7200"
                                       #language fr-FR  "7200"
#string STR_COM_BAUD_RATE_6            #language en-US  "4800"
                                       #language fr-FR  "4800"
#string STR_COM_BAUD_RATE_7            #language en-US  "3600"
                                       #language fr-FR  "3600"
#string STR_COM_BAUD_RATE_8            #language en-US  "2400"
                                       #language fr-FR  "2400"
#string STR_COM_BAUD_RATE_9            #language en-US  "2000"
                                       #language fr-FR  "2000"
#string STR_COM_BAUD_RATE_10           #language en-US  "1800"
                                       #language fr-FR  "1800"
#string STR_COM_BAUD_RATE_11           #language en-US  "1200"
                                       #language fr-FR  "1200"
#string STR_COM_BAUD_RATE_12           #language en-US  "600"
                                       #language fr-FR  "600"
#string STR_COM_BAUD_RATE_13           #language en-US  "300"
                                       #language fr-FR  "300"
#string STR_COM_BAUD_RATE_14           #language en-US  "150"
                                       #language fr-FR  "150"
#string STR_COM_BAUD_RATE_15           #language en-US  "134"
                                       #language fr-FR  "134"
#string STR_COM_BAUD_RATE_16           #language en-US  "110"
                                       #language fr-FR  "110"
#string STR_COM_BAUD_RATE_17           #language en-US  "75"
                                       #language fr-FR  "75"
#string STR_COM_BAUD_RATE_18           #language en-US  "50"
                                       #language fr-FR  "50"
#string STR_COM_DATA_BITS_0            #language en-US  "5"
                                       #language fr-FR  "5"
#string STR_COM_DATA_BITS_1            #language en-US  "6"
                                       #language fr-FR  "6"
#string STR_COM_DATA_BITS_2            #language en-US  "7"
                                       #language fr-FR  "7"
#string STR_COM_DATA_BITS_3            #language en-US  "8"
                                       #language fr-FR  "8"
#string STR_COM_PAR_0                  #language en-US  "None"
                                       #language fr-FR  "None"
#string STR_COM_PAR_1                  #language en-US  "Even"
                                       #language fr-FR  "Even"
#string STR_COM_PAR_2                  #language en-US  "Odd"
                                       #language fr-FR  "Odd"
#string STR_COM_PAR_3                  #language en-US  "Mark"
                                       #language fr-FR  "Mark"
#string STR_COM_PAR_4                  #language en-US  "Space"
                                       #language fr-FR  "Space"
#string STR_COM_STOP_BITS_0            #language en-US  "One"
                                       #language fr-FR  "One"
#string STR_COM_STOP_BITS_1            #language en-US  "One And A Half"
                                       #language fr-FR  "One And A Half"
#string STR_COM_STOP_BITS_2            #language en-US  "Two"
                                       #language fr-FR  "Two"
#string STR_COM_TYPE_0                 #language en-US  "PC_ANSI"
                                       #language fr-FR  "PC_ANSI"
#string STR_COM_TYPE_1                 #language en-US  "VT_100"
                                       #language fr-FR  "VT_100"
#string STR_COM_TYPE_2                 #language en-US  "VT_100_PLUS"
                                       #language fr-FR  "VT_100_PLUS"
#string STR_COM_TYPE_3                 #language en-US  "VT_UTF8"
                                       #language fr-FR  "VT_UTF8"
#string STR_RESET                      #language en-US  "Reset System"
                                       #language fr-FR  "Reset System"
#string STR_FORM_GOTO_MAIN             #language en-US  "Go Back To Main Page"
                                       #language fr-FR  "Go Back To Main Page"
#string STR_BOOT_FROM_FILE             #language en-US  "Boot From File"
                                       #language fr-FR  "Boot From File"
#string STR_BOOT_FROM_FILE_HELP        #language en-US  "Boot system from a file or device"
                                       #language fr-FR  "Boot system from a file or device"
#string STR_OPTIONAL_DATA              #language en-US  "Input Optional Data"
                                       #language fr-FR  "Input Optional Data"
#string STR_CHANGE_ORDER               #language en-US  "Change the order"
                                       #language fr-FR  "Change the order"
#string STR_BOOT_LEGACY                #language en-US  "Boot Legacy System"
                                       #language fr-FR  "Boot Legacy System"
#string STR_BOOT_LEGACY_HELP           #language en-US  "Supports boot from legacy FD, HD, CD, PCMCIA, USB, and Network"
                                       #language fr-FR  "Supports boot from legacy FD, HD, CD, PCMCIA, USB, and Network"
#string STR_BOOT_LEGACY_FLOPPY         #language en-US  "Boot From Floppy"
                                       #language fr-FR  "Boot From Floppy"
#string STR_BOOT_LEGACY_HARDDRIVE      #language en-US  "Boot From Hard Drive"
                                       #language fr-FR  "Boot From Hard Drive"
#string STR_BOOT_LEGACY_CDROM          #language en-US  "Boot From CD Rom"
                                       #language fr-FR  "Boot From CD Rom"
#string STR_BOOT_LEGACY_PCMCIA         #language en-US  "Boot From PCMCIA"
                                       #language fr-FR  "Boot From PCMCIA"
#string STR_BOOT_LEGACY_USB            #language en-US  "Boot From USB Device"
                                       #language fr-FR  "Boot From USB Device"
#string STR_BOOT_LEGACY_NETWORK        #language en-US  "Boot From Network"
                                       #language fr-FR  "Boot From Network"
#string STR_DISABLE_LEGACY_DEVICE      #language en-US  "Disabled"
                                       #language fr-FR  "Disabled"
#string STR_FILE_EXPLORER_TITLE        #language en-US  "File Explorer"
                                       #language fr-FR  "File Explorer"
#string STR_HARDWARE_FLOW_CONTROL      #language fr-FR  "Hardware"
                                       #language en-US  "Hardware"
#string STR_NONE_FLOW_CONTROL          #language fr-FR  "None"
                                       #language en-US  "None"
//
// BugBug : need someone to translate these strings to french
//
