///** @file
//  
//    File Explorer Formset
//  
//  Copyright (c) 2004 - 2014, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

#include "FormGuid.h"

formset
  guid = FILE_EXPLORE_FORMSET_GUID,
  title = STRING_TOKEN(STR_FILE_EXPLORER_TITLE),
  help = STRING_TOKEN(STR_NULL_STRING),
  classguid = FILE_EXPLORE_FORMSET_GUID,

  varstore FILE_EXPLORER_NV_DATA,
    varid = VARSTORE_ID_BOOT_MAINT,
    name = FeData,
    guid = FILE_EXPLORE_FORMSET_GUID;

  form formid = FORM_FILE_EXPLORER_ID,
       title = STRING_TOKEN(STR_FILE_EXPLORER_TITLE);

       label FORM_FILE_EXPLORER_ID;
       label LABEL_END;
  endform;

  form formid = FORM_BOOT_ADD_DESCRIPTION_ID,
       title = STRING_TOKEN(STR_FORM_BOOT_ADD_DESC_TITLE);

       label FORM_BOOT_ADD_DESCRIPTION_ID;
       label LABEL_END;

       subtitle text = STRING_TOKEN(STR_NULL_STRING);

       string    varid    = FeData.BootDescriptionData,
                 questionid = KEY_VALUE_BOOT_DESCRIPTION,
                 prompt   = STRING_TOKEN(STR_LOAD_OPTION_DESC),
                 help     = STRING_TOKEN(STR_NULL_STRING),
                 flags    = INTERACTIVE,
                 minsize  = 6,
                 maxsize  = 75,
       endstring;

       string    varid    = FeData.BootOptionalData,
                 questionid = KEY_VALUE_BOOT_OPTION,
                 prompt   = STRING_TOKEN(STR_OPTIONAL_DATA),
                 help     = STRING_TOKEN(STR_NULL_STRING),
                 flags    = INTERACTIVE,
                 minsize  = 0,
                 maxsize  = 120,
       endstring;

       subtitle text = STRING_TOKEN(STR_NULL_STRING);

       text
         help   = STRING_TOKEN(STR_SAVE_AND_EXIT),
         text   = STRING_TOKEN(STR_SAVE_AND_EXIT),
         flags  = INTERACTIVE,
         key    = KEY_VALUE_SAVE_AND_EXIT_BOOT;

       text
         help   = STRING_TOKEN(STR_NO_SAVE_AND_EXIT),
         text   = STRING_TOKEN(STR_NO_SAVE_AND_EXIT),
         flags  = INTERACTIVE,
         key    = KEY_VALUE_NO_SAVE_AND_EXIT_BOOT;

  endform;

  form formid = FORM_DRIVER_ADD_FILE_DESCRIPTION_ID,
       title = STRING_TOKEN(STR_FORM_DRV_ADD_DESC_TITLE);

       label FORM_DRIVER_ADD_FILE_DESCRIPTION_ID;
       label LABEL_END;

       subtitle text = STRING_TOKEN(STR_NULL_STRING);

       string    varid    = FeData.DriverDescriptionData,
                 questionid = KEY_VALUE_DRIVER_DESCRIPTION,
                 prompt   = STRING_TOKEN(STR_LOAD_OPTION_DESC),
                 help     = STRING_TOKEN(STR_NULL_STRING),
                 flags    = INTERACTIVE,
                 minsize  = 6,
                 maxsize  = 75,
       endstring;

       string    varid    = FeData.DriverOptionalData,
                 questionid = KEY_VALUE_DRIVER_OPTION,
                 prompt   = STRING_TOKEN(STR_OPTIONAL_DATA),
                 help     = STRING_TOKEN(STR_NULL_STRING),
                 flags    = INTERACTIVE,
                 minsize  = 0,
                 maxsize  = 120,
       endstring;

       checkbox varid    = FeData.ForceReconnect,
               prompt   = STRING_TOKEN(STR_LOAD_OPTION_FORCE_RECON),
               help     = STRING_TOKEN(STR_LOAD_OPTION_FORCE_RECON),
               flags    = CHECKBOX_DEFAULT,
               key      = 0,
       endcheckbox;

       subtitle text = STRING_TOKEN(STR_NULL_STRING);

       text
         help   = STRING_TOKEN(STR_SAVE_AND_EXIT),
         text   = STRING_TOKEN(STR_SAVE_AND_EXIT),
         flags  = INTERACTIVE,
         key    = KEY_VALUE_SAVE_AND_EXIT_DRIVER;  //BUGBUB: allow duplicate key in one formset???

       text
         help   = STRING_TOKEN(STR_NO_SAVE_AND_EXIT),
         text   = STRING_TOKEN(STR_NO_SAVE_AND_EXIT),
         flags  = INTERACTIVE,
         key    = KEY_VALUE_NO_SAVE_AND_EXIT_DRIVER;

  endform;

endformset;