///** @file
//  
//    Browser formset.
//  
//  Copyright (c) 2007 - 2015, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

#include <Guid/BdsHii.h>

#define FRONT_PAGE_CLASS               0x0000
#define FRONT_PAGE_SUBCLASS            0x0002

#define FRONT_PAGE_FORM_ID             0x1000

#define FRONT_PAGE_ITEM_ONE            0x0001
#define FRONT_PAGE_ITEM_TWO            0x0002
#define FRONT_PAGE_ITEM_THREE          0x0003
#define FRONT_PAGE_ITEM_FOUR           0x0004
#define FRONT_PAGE_ITEM_FIVE           0x0005

#define FRONT_PAGE_KEY_CONTINUE        0x1000
#define FRONT_PAGE_KEY_LANGUAGE        0x1234
#define FRONT_PAGE_KEY_BOOT_MANAGER    0x1064
#define FRONT_PAGE_KEY_DEVICE_MANAGER  0x8567
#define FRONT_PAGE_KEY_BOOT_MAINTAIN   0x9876

#define LABEL_SELECT_LANGUAGE          0x1000
#define LABEL_TIMEOUT                  0x2000
#define LABEL_END                      0xffff

formset
  guid     = FRONT_PAGE_FORMSET_GUID,
  title    = STRING_TOKEN(STR_FRONT_PAGE_TITLE),
  help     = STRING_TOKEN(STR_NULL_STRING),
  classguid = FRONT_PAGE_FORMSET_GUID,

  form formid = FRONT_PAGE_FORM_ID,
       title  = STRING_TOKEN(STR_FRONT_PAGE_TITLE);

    banner
      title = STRING_TOKEN(STR_FRONT_PAGE_COMPUTER_MODEL),
      line  1,
      align left;

    banner
      title = STRING_TOKEN(STR_FRONT_PAGE_CPU_MODEL),
      line  2,
      align left;

    banner
      title = STRING_TOKEN(STR_FRONT_PAGE_CPU_SPEED),
      line  2,
      align right;

    banner
      title = STRING_TOKEN(STR_FRONT_PAGE_BIOS_VERSION),
      line  3,
      align left;

    banner
      title = STRING_TOKEN(STR_FRONT_PAGE_MEMORY_SIZE),
      line  3,
      align right;

//    banner
//      title = STRING_TOKEN(STR_FRONT_PAGE_BANNER_0_LEFT),
//      line  0,
//      align left;

//    banner
//      title = STRING_TOKEN(STR_FRONT_PAGE_BANNER_0_RIGHT),
//      line  0,
//      align right;

//    banner
//      title = STRING_TOKEN(STR_FRONT_PAGE_BANNER_1_LEFT),
//      line  1,
//      align left;

//    banner
//      title = STRING_TOKEN(STR_FRONT_PAGE_BANNER_1_RIGHT),
//      line  1,
//      align right;

//    banner
//      title = STRING_TOKEN(STR_FRONT_PAGE_BANNER_2_LEFT),
//      line  2,
//      align left;

//    banner
//      title = STRING_TOKEN(STR_FRONT_PAGE_BANNER_3_LEFT),
//      line  3,
//      align left;


    text
      help    = STRING_TOKEN(STR_CONTINUE_HELP),
      text    = STRING_TOKEN(STR_CONTINUE_PROMPT),
      flags   = INTERACTIVE,
      key     = FRONT_PAGE_KEY_CONTINUE;

    label LABEL_SELECT_LANGUAGE;
    //
    // This is where we will dynamically add a OneOf type op-code to select
    // Languages from the currently available choices
    //
    label LABEL_END;

    goto FRONT_PAGE_ITEM_THREE,
      prompt  = STRING_TOKEN(STR_BOOT_MANAGER),
      help    = STRING_TOKEN(STR_BOOT_MANAGER_HELP),
      flags   = INTERACTIVE,
      key     = FRONT_PAGE_KEY_BOOT_MANAGER;

    goto FRONT_PAGE_ITEM_FOUR,
      prompt  = STRING_TOKEN(STR_DEVICE_MANAGER),
      help    = STRING_TOKEN(STR_DEVICE_MANAGER_HELP),
      flags   = INTERACTIVE,
      key     = FRONT_PAGE_KEY_DEVICE_MANAGER;

    goto FRONT_PAGE_ITEM_FIVE,
      prompt  = STRING_TOKEN(STR_BOOT_MAINT_MANAGER),
      help    = STRING_TOKEN(STR_BOOT_MAINT_MANAGER_HELP),
      flags   = INTERACTIVE,
      key     = FRONT_PAGE_KEY_BOOT_MAINTAIN;

  endform;

endformset;
