///** @file
//  
//    Driver Health formset.
//  
//  Copyright (c) 2004 - 2011, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

#include "DeviceManagerVfr.h"

formset
  guid      = DRIVER_HEALTH_FORMSET_GUID,
  title     = STRING_TOKEN(STR_DH_BANNER),
  help      = STRING_TOKEN(STR_EMPTY_STRING),
  classguid = DRIVER_HEALTH_FORMSET_GUID,
 
  form formid = DRIVER_HEALTH_FORM_ID,
      title  = STRING_TOKEN(STR_DH_BANNER);

      label LABEL_DRIVER_HEALTH;
      label LABEL_DRIVER_HEALTH_END;  
            
      subtitle text = STRING_TOKEN(STR_LAST_STRING);
      label LABEL_DRIVER_HEALTH_REAPIR_ALL;
      label LABEL_DRIVER_HEALTH_REAPIR_ALL_END;
            
      subtitle text = STRING_TOKEN(STR_LAST_STRING);
      subtitle text = STRING_TOKEN(STR_HELP_FOOTER);
      subtitle text = STRING_TOKEN(STR_LAST_STRING);      
  endform;   
endformset;
