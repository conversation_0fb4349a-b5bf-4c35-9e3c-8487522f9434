///** @file
//  
//    Device Manager formset.
//  
//  Copyright (c) 2004 - 2011, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

#include "DeviceManagerVfr.h"

#define EFI_DISK_DEVICE_CLASS              0x0001
#define EFI_VIDEO_DEVICE_CLASS             0x0002
#define EFI_NETWORK_DEVICE_CLASS           0x0004
#define EFI_INPUT_DEVICE_CLASS             0x0008
#define EFI_ON_BOARD_DEVICE_CLASS          0x0010
#define EFI_OTHER_DEVICE_CLASS             0x0020

#define DEVICE_MANAGER_CLASS               0x0000
#define FRONT_PAGE_SUBCLASS                0x0003

formset
  guid      = DEVICE_MANAGER_FORMSET_GUID,
  title     = STRING_TOKEN(STR_DEVICE_MANAGER_TITLE),
  help      = STRING_TOKEN(STR_EMPTY_STRING),
  classguid = DEVICE_MANAGER_FORMSET_GUID,

  form formid = DEVICE_MANAGER_FORM_ID,
       title  = STRING_TOKEN(STR_DEVICE_MANAGER_TITLE);

    subtitle text = STRING_TOKEN(STR_DEVICES_LIST);
    //
    // This is where devices get added to the device manager hierarchy
    //
    label EFI_DISK_DEVICE_CLASS;
//    label LABEL_END;  // Since next opcode is a label, so this one could be omitted to save code size

    label EFI_VIDEO_DEVICE_CLASS;
//    label LABEL_END;

    label EFI_NETWORK_DEVICE_CLASS;
//    label LABEL_END;

    label EFI_INPUT_DEVICE_CLASS;
//    label LABEL_END;

    label EFI_ON_BOARD_DEVICE_CLASS;
//    label LABEL_END;

//    label EFI_OTHER_DEVICE_CLASS;

    label LABEL_DEVICES_LIST;
    label LABEL_END;
      
    subtitle text = STRING_TOKEN(STR_EMPTY_STRING);

    label LABEL_VBIOS;
    label LABEL_END;
    
    subtitle text = STRING_TOKEN(STR_EMPTY_STRING);
    subtitle text = STRING_TOKEN(STR_EXIT_STRING);

  endform;

  form formid = NETWORK_DEVICE_LIST_FORM_ID,
       title = STRING_TOKEN(STR_FORM_NETWORK_DEVICE_LIST_TITLE);

    subtitle text = STRING_TOKEN(STR_NETWORK_DEVICE_LIST_STRING);

       label LABEL_NETWORK_DEVICE_LIST_ID;
       label LABEL_END;
       subtitle text = STRING_TOKEN(STR_EMPTY_STRING);
    subtitle text = STRING_TOKEN(STR_EXIT_STRING);
  endform;

  form formid = NETWORK_DEVICE_FORM_ID,
       title = STRING_TOKEN(STR_FORM_NETWORK_DEVICE_TITLE);

    subtitle text = STRING_TOKEN(STR_NETWORK_DEVICE_STRING);

       label LABEL_NETWORK_DEVICE_ID;
       label LABEL_END;
       subtitle text = STRING_TOKEN(STR_EMPTY_STRING);
    subtitle text = STRING_TOKEN(STR_EXIT_STRING);
  endform;
endformset;

