///** @file
//  
//    String definitions for the Device Manager.
//  
//  Copyright (c) 2004 - 2014, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

/=#

#langdef   en-US "English"
#langdef   fr-FR "Français"

#string STR_DEVICE_MANAGER_TITLE       #language en-US  "Device Manager"
                                       #language fr-FR  "Device Manager"
#string STR_DEVICES_LIST               #language en-US  "Devices List"
                                       #language fr-FR  "Devices List"
#string STR_DISK_DEVICE                #language en-US  "Disk Devices"
                                       #language fr-FR  "Disk Devices"
#string STR_VIDEO_DEVICE               #language en-US  "Video Devices"
                                       #language fr-FR  "Video Devices"
#string STR_NETWORK_DEVICE             #language en-US  "Network Devices"
                                       #language fr-FR  "Network Devices"
#string STR_INPUT_DEVICE               #language en-US  "Input Devices"
                                       #language fr-FR  "Input Devices"
#string STR_ON_BOARD_DEVICE            #language en-US  "Motherboard Devices"
                                       #language fr-FR  "Motherboard Devices"
#string STR_OTHER_DEVICE               #language en-US  "Other Devices"
                                       #language fr-FR  "Other Devices"
#string STR_EMPTY_STRING               #language en-US  ""
                                       #language fr-FR  ""
#string STR_DRIVER_HEALTH_STATUS_HELP  #language en-US  "Check whether all the controllers in the platform are healthy"
                                       #language fr-FR  "Check whether all the controllers in the platform are healthy"
#string STR_DRIVER_HEALTH_ALL_HEALTHY  #language en-US  "  The platform is healthy"
                                       #language fr-FR  "  The platform is healthy"
#string STR_DRIVER_NOT_HEALTH          #language en-US  "  Some drivers are not healthy"
                                       #language fr-FR  "  Some drivers are not healthy"
#string STR_DH_BANNER                  #language en-US  "Driver Health"
                                       #language fr-FR  "Driver Health"
#string STR_DM_DRIVER_HEALTH_TITLE     #language en-US  "Driver Health"
                                       #language fr-FR  "Driver Health"
#string STR_DH_STATUS_LIST             #language en-US  "All Driver Health Status List"
                                       #language fr-FR  "All Driver Health Status List"
#string STR_DRIVER_HEALTH_REPAIR_ALL   #language en-US  "Repair All"
                                       #language fr-FR  "Repair All"                                       
#string STR_DH_REPAIR_ALL_TITLE        #language en-US  "Repair the whole platform"
                                       #language fr-FR  "Repair the whole platform"
#string STR_DH_REPAIR_ALL_HELP         #language en-US  "Repair the whole platform until all modules reach terminal status"
                                       #language fr-FR  "Repair the whole platform until all modules reach terminal status"
#string STR_DH_REPAIR_SINGLE_TITLE     #language en-US  "Item cannot get name"
                                       #language fr-FR  "Item cannot get name"  
#string STR_DH_REPAIR_SINGLE_HELP      #language en-US  "Repair single driver with specify controller"
                                       #language fr-FR  "Repair single driver with specify controller"

#string STR_EXIT_STRING                #language en-US  "Press ESC to exit."
                                       #language fr-FR  "Press ESC to exit."
#string STR_REPAIR_REQUIRED            #language en-US  "  Repair Required."
                                       #language fr-FR  "  Repair Required."
#string STR_CONFIGURATION_REQUIRED     #language en-US  "  Configuration Required."
                                       #language fr-FR  "  Configuration Required."
#string STR_OPERATION_FAILED           #language en-US  "  Operation Failed."
                                       #language fr-FR  "  Operation Failed."
#string STR_RECONNECT_REQUIRED         #language en-US  "  Reconnect Required."
                                       #language fr-FR  "  Reconnect Required."
#string STR_REBOOT_REQUIRED            #language en-US  "  Reboot Required."
                                       #language fr-FR  "  Reboot Required."
#string STR_NO_OPERATION_REQUIRED      #language en-US  "  No Operation Required."
                                       #language fr-FR  "  No Operation Required."
#string STR_DRIVER_HEALTH_HEALTHY      #language en-US  "  Healthy."
                                       #language fr-FR  "  Healthy."
#string STR_FORM_NETWORK_DEVICE_TITLE  #language en-US  "Network Device"
                                       #language fr-FR  "Network Device"
#string STR_FORM_NETWORK_DEVICE_HELP   #language en-US  "Network Device Help..."
                                       #language fr-FR  "Network Device Help..."
#string STR_NETWORK_DEVICE_STRING      #language en-US  "Network Device"
                                       #language fr-FR  "Network Device"
#string STR_FORM_NETWORK_DEVICE_LIST_HELP   #language en-US  "Select the network device according the MAC address"
                                            #language fr-FR  "Select the network device according the MAC address"
#string STR_FORM_NETWORK_DEVICE_LIST_TITLE  #language en-US  "Network Device List"
                                            #language fr-FR  "Network Device List"
#string STR_NETWORK_DEVICE_LIST_STRING      #language en-US  "Network Device List"
                                            #language fr-FR  "Network Device List"
#string STR_NETWORK_DEVICE_HELP             #language en-US  "Network Device"
                                            #language fr-FR  "Network Device"
//
// Ensure that this is the last string.  We are using it programmatically
// to do string token re-usage settings for the Device Manager since we are
// constantly recreating this page based on HII population.
////
