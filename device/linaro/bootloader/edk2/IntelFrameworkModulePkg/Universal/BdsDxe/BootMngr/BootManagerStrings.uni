///** @file
//  
//    String definitions for BdsPlatform formset.
//  
//  Copyright (c) 2004 - 2011, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

/=#

#langdef   en-US "English"
#langdef   fr-FR "Français"

#string STR_BM_BANNER                  #language en-US  "Boot Manager"
                                       #language fr-FR  "Boot Manager"
#string STR_HELP_FOOTER                #language en-US  "↑ and ↓ to change option, ENTER to select an option, ESC to exit"
                                       #language fr-FR  "↑ pour ↓ changer l'option, ENTRER choisir une option, ESC pour sortir"
#string STR_AND                        #language en-US  " and "
                                       #language fr-FR  " et "
#string STR_BOOT_OPTION_BANNER         #language en-US  "Boot Option Menu"
                                       #language fr-FR  "le Menu d'Option de Botte"
#string STR_ANY_KEY_CONTINUE           #language en-US  "Press any key to continue..."
                                       #language fr-FR  "Appuie n'importe quelle pour continuer..."
#string STR_LAST_STRING                #language en-US  ""
                                       #language fr-FR  ""

