///** @file
//  
//    String definitions for BdsPlatform formset.
//  
//  Copyright (c) 2004 - 2010, Intel Corporation. All rights reserved.<BR>
//  This program and the accompanying materials
//  are licensed and made available under the terms and conditions of the BSD License
//  which accompanies this distribution.  The full text of the license may be found at
//  http://opensource.org/licenses/bsd-license.php
//  
//  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
//  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//  
//**/

/=#

#langdef   en-US "English"
#langdef   fr-FR "Français"

#string STR_INTERNAL_EFI_SHELL         #language en-US  "[Internal EFI Shell]"
                                       #language fr-FR  "[la Coquille interne de EFI]"
#string STR_LEGACY_BOOT_A              #language en-US  "[Internal Legacy Boot A:]"
                                       #language fr-FR  "[Le Legs interne Charge A:]"
#string STR_PROCESSED_ALL_BOOT_OPTIONS #language en-US  "[ProcessedAllBootOptions(): Load shell from the FV]"
                                       #language fr-FR  "[ProcessedAllBootOptions() : la coquille de Chargement du FV]"
#string STR_BOOT_FAILED                #language en-US  "Boot Failed. "
                                       #language fr-FR  "la Botte A. "
#string STR_BOOT_SUCCEEDED             #language en-US  "Boot Succeeded - "
                                       #language fr-FR  "Charge Réussi - "
#string STR_DEFAULT_TIMEOUT_BANNER     #language en-US  "Default boot selection will be booted in "
                                       #language fr-FR  "la sélection de botte de Défaut sera dans "
#string STR_SECONDS                    #language en-US  " seconds"
                                       #language fr-FR  " les secondes"
#string STR_EXIT_DATA                  #language en-US  "ExitData: "
                                       #language fr-FR  "ExitData: "
#string STR_LOADING                    #language en-US  "Loading: "
                                       #language fr-FR  "Chargement: "
#string STR_ENTER_NORMAL_BOOT          #language en-US  "Entering Normal Boot Process..."
                                       #language fr-FR  "l'Entrer le Procédé de Botte Normal..."
#string STR_PERFORM_MEM_TEST           #language en-US  "Perform memory test (ESC to skip)"
                                       #language fr-FR  "Exécute l'examen de mémoire (ESC pour sauter)"
#string STR_MEMORY_TEST_PERCENT        #language en-US  "% of the system memory tested OK"
                                       #language fr-FR  "% de la mémoire de système essayée D'ACCORD"
#string STR_ESC_TO_SKIP_MEM_TEST       #language en-US  "Press ESC key to skip memory test"
                                       #language fr-FR  "Appuie sur ESC sauter examen de mémoire"
#string STR_MEM_TEST_COMPLETED         #language en-US  " bytes of system memory tested OK\r\n"
                                       #language fr-FR  "octets dela mémoire de système essayée D'ACCORD\r\n"
#string STR_NO_EXT_MEM_FOUND           #language en-US  "Memory resource updated!"
                                       #language fr-FR  "la mémoire Non étendue trouvée!"
#string STR_SYSTEM_MEM_ERROR           #language en-US  "System encounters memory errors"
                                       #language fr-FR  "le Système rencontre les erreurs de mémoire"
#string STR_START_BOOT_OPTION          #language en-US  "Start boot option"
                                       #language fr-FR  "l'option de botte de Début"
                                       