// /** @file
// Intel Framework Module Package.
//
// This package contains the definitions and module implementation
// which follows Intel EFI Framework Specification.
//
// Copyright (c) 2007 - 2016, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
//
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_PACKAGE_ABSTRACT            #language en-US "Intel Framework Module Package"

#string STR_PACKAGE_DESCRIPTION         #language en-US "This package contains the definitions and module implementation that follow the Intel EFI Framework Specification."



#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdLogoFile_PROMPT  #language en-US "FFS Name of Boot Logo File"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdLogoFile_HELP  #language en-US "FFS filename to find the default BMP Logo file."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdShellFile_PROMPT  #language en-US "FFS Name of Shell Application"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdShellFile_HELP  #language en-US "FFS filename to find the shell application."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdIsaBusSupportedFeatures_PROMPT  #language en-US "ISA Bus Features"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdIsaBusSupportedFeatures_HELP  #language en-US "ISA Bus features to support DMA, Slave DMA and ISA Memory. <BR><BR>\n"
                                                                                                       "BIT0 indicates if DMA is supported<BR>\n"
                                                                                                       "BIT1 indicates if only slave DMA is supported<BR>\n"
                                                                                                       "BIT2 indicates if ISA memory is supported<BR>\n"
                                                                                                       "Other BITs are reserved and must be zero. If more than one features are supported, the different BIT will be enabled at the same time."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_ERR_80000002 #language en-US "Reserved bits must be set to zero."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdFdcBaseAddress_PROMPT  #language en-US "I/O Base Address of Floppy Device Controller"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdFdcBaseAddress_HELP  #language en-US "I/O Base address of floppy device controller."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBiosVideoSetTextVgaModeEnable_PROMPT  #language en-US "Switch to Text VGA Mode on UEFI Boot"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBiosVideoSetTextVgaModeEnable_HELP  #language en-US "Indicates if BiosVideo driver will switch to 80x25 Text VGA Mode when exiting boot service.<BR><BR>\n"
                                                                                                             "TRUE  - Switch to Text VGA Mode.<BR>\n"
                                                                                                             "FALSE - Does not switch to Text VGA Mode.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBiosVideoCheckVbeEnable_PROMPT  #language en-US "Enable Check for VESA BIOS Extension Service"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBiosVideoCheckVbeEnable_HELP  #language en-US "Indicates if BiosVideo driver will check for VESA BIOS Extension service support.<BR><BR>\n"
                                                                                                       "TRUE  - Check for VESA BIOS Extension service.<BR>\n"
                                                                                                       "FALSE - Does not check for VESA BIOS Extension service.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBiosVideoCheckVgaEnable_PROMPT  #language en-US "Enable Check for VGA Service"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBiosVideoCheckVgaEnable_HELP  #language en-US "Indicates if BiosVideo driver will check for VGA service support. NOTE: If both PcdBiosVideoCheckVbeEnable and PcdBiosVideoCheckVgaEnable are set to FALSE, that means Graphics Output protocol will not be installed, the VGA miniport protocol will be installed instead.<BR><BR>\n"
                                                                                                       "TRUE  - Check for VGA service<BR>\n"
                                                                                                       "FALSE - Does not check for VGA service<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdLegacyBiosCacheLegacyRegion_PROMPT  #language en-US "Enable Cacheability for Legacy Region"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdLegacyBiosCacheLegacyRegion_HELP  #language en-US "Set memory space for legacy region cacheable?<BR><BR>\n"
                                                                                                           "TRUE  - Cacheable<BR>\n"
                                                                                                           "FALSE - Non cacheable<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdEbdaReservedMemorySize_PROMPT  #language en-US "Reserved EBDA Memory Size"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdEbdaReservedMemorySize_HELP  #language en-US "Specify memory size with bytes to reserve EBDA below 640K for OPROM. The value should be a multiple of 4KB."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_ERR_80000001 #language en-US "Invalid value provided."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdS3AcpiReservedMemorySize_PROMPT  #language en-US "Reserved S3 Boot ACPI Memory Size"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdS3AcpiReservedMemorySize_HELP  #language en-US "Specify memory size with page number for a pre-allocated reserved memory to be used by PEI in S3 phase. The default size 32K. When changing the value make sure the memory size is large enough to meet PEI requirement in the S3 phase."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdEndOpromShadowAddress_PROMPT  #language en-US "Top Address of Shadowed Legacy OpROM"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdEndOpromShadowAddress_HELP  #language en-US "Specify the end of address below 1MB for the OPROM. The last shadowed OpROM should not exceed this address."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdLowPmmMemorySize_PROMPT  #language en-US "Low PMM (Post Memory Manager) Size"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdLowPmmMemorySize_HELP  #language en-US "Specify the low PMM (Post Memory Manager) size with bytes below 1MB. The value should be a multiple of 4KB."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdHighPmmMemorySize_PROMPT  #language en-US "High PMM (Post Memory Manager) Size"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdHighPmmMemorySize_HELP  #language en-US "Specify the high PMM (Post Memory Manager) size with bytes above 1MB. The value should be a multiple of 4KB."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdFastPS2Detection_PROMPT  #language en-US "Enable fast PS2 detection"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdFastPS2Detection_HELP  #language en-US "Indicates if to use the optimized timing for best PS2 detection performance.\n"
                                                                                                "Note this PCD could be set to TRUE for best boot performance and set to FALSE for best device compatibility.<BR><BR>\n"
                                                                                                "TRUE  - Use the optimized timing for best PS2 detection performance.<BR>\n"
                                                                                                "FALSE - Use the normal timing to detect PS2.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdStatusCodeUseOEM_PROMPT  #language en-US "Report StatusCode via OEM Device"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdStatusCodeUseOEM_HELP  #language en-US "Indicates if OEM device is enabled as StatusCode report device. It is only used in Framework StatusCode implementation. <BR><BR>\n"
                                                                                                "TRUE  - Enable OEM device.<BR>\n"
                                                                                                "FALSE - Disable OEM device.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdStatusCodeUseDataHub_PROMPT  #language en-US "Log StatusCode into DataHub"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdStatusCodeUseDataHub_HELP  #language en-US "Indicates if StatusCode report is loged into DataHub.<BR><BR>\n"
                                                                                                    "TRUE  - Log StatusCode report into DataHub.<BR>\n"
                                                                                                    "FALSE - Does not log StatusCode report into DataHub.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdIsaBusSerialUseHalfHandshake_PROMPT  #language en-US "Enable Serial device Half Hand Shake"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdIsaBusSerialUseHalfHandshake_HELP  #language en-US "Indicates if Serial device uses half hand shake.<BR><BR>\n"
                                                                                                            "TRUE  - Serial device uses half hand shake.<BR>\n"
                                                                                                            "FALSE - Serial device doesn't use half hand shake.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdPlatformCsmSupport_PROMPT  #language en-US "Turn on Legacy Support in S3 boot"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdPlatformCsmSupport_HELP  #language en-US "Enable Legacy OS support during ACPI S3 boot.<BR><BR>\n"
                                                                                                  "TRUE  - Support Legacy OS with S3 boot.<BR>\n"
                                                                                                  "FALSE - Does not support Legacy OS with S3 boot.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdPs2KbdExtendedVerification_PROMPT  #language en-US "Turn on PS2 Keyboard Extended Verification"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdPs2KbdExtendedVerification_HELP  #language en-US "Enable PS2 keyboard extended verification. Extended verification will increase boot time.<BR><BR>\n"
                                                                                                          "TRUE  - Enable extended verification.<BR>\n"
                                                                                                          "FALSE - Disable extended verification.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdInstallAcpiSupportProtocol_PROMPT  #language en-US "Enable Framework ACPI Support"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdInstallAcpiSupportProtocol_HELP  #language en-US "Indicates if Framework Acpi Support protocol is installed.<BR><BR>\n"
                                                                                                          "TRUE  - Install Framework Acpi Support protocol.<BR>\n"
                                                                                                          "FALSE - Doesn't install Framework Acpi Support protocol.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdPs2MouseExtendedVerification_PROMPT  #language en-US "Turn on PS2 Mouse Extended Verification"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdPs2MouseExtendedVerification_HELP  #language en-US "Enable PS2 mouse extended verification. Extended verification will increase boot time.<BR><BR>\n"
                                                                                                            "TRUE  - Enable extended verification.<BR>\n"
                                                                                                            "FALSE - Disable extended verification.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBootlogoOnlyEnable_PROMPT  #language en-US "Enable Boot Logo only"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBootlogoOnlyEnable_HELP  #language en-US "Indicates if only Boot logo is showed and all message output is disabled in BDS.<BR><BR>\n"
                                                                                                  "TRUE  - Only Boot Logo is showed in boot.<BR>\n"
                                                                                                  "FALSE - All messages and Boot Logo are showed in boot.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBootState_PROMPT  #language en-US "Boot State Flag"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdBootState_HELP  #language en-US "Indicates if the machine has completed one boot cycle before. After the complete boot, BootState will be set to FALSE.<BR><BR>\n"
                                                                                         "TRUE  - The complete boot cycle has not happened before.<BR>\n"
                                                                                         "FALSE - The complete boot cycle has happened before.<BR>"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdOpromReservedMemoryBase_PROMPT  #language en-US "Reserved Memory Base Address for OPROM"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdOpromReservedMemoryBase_HELP  #language en-US "Specify memory base address for OPROM to find free memory.\n"
                                                                                                       "Some OPROMs do not use EBDA or PMM to allocate memory for its usage,\n"
                                                                                                       "instead they find the memory filled with zero from 0x20000.\n"
                                                                                                       "The value should be a multiple of 4KB.\n"
                                                                                                       "The range should be below the EBDA reserved range from\n"
                                                                                                       "(CONVENTIONAL_MEMORY_TOP - Reserved EBDA Memory Size) to CONVENTIONAL_MEMORY_TOP."

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdOpromReservedMemorySize_PROMPT  #language en-US "Reserved Memory Size for OPROM"

#string STR_gEfiIntelFrameworkModulePkgTokenSpaceGuid_PcdOpromReservedMemorySize_HELP  #language en-US "Specify memory size with bytes for OPROM to find free memory.\n"
                                                                                                       "The value should be a multiple of 4KB. And the range should be below the EBDA reserved range from\n"
                                                                                                       "(CONVENTIONAL_MEMORY_TOP - Reserved EBDA Memory Size) to CONVENTIONAL_MEMORY_TOP."

