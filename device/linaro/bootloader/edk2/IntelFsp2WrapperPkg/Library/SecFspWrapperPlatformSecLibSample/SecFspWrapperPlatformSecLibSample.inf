## @file
#  Sample to provide FSP wrapper platform sec related function.
#
#  Copyright (c) 2014 - 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SecFspWrapperPlatformSecLibSample
  FILE_GUID                      = 8F1AC44A-CE7E-4E29-95BB-92E321BB1573
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformSecLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FspWrapperPlatformSecLibSample.c
  SecRamInitData.c
  SecPlatformInformation.c
  SecGetPerformance.c
  SecTempRamDone.c
  PlatformInit.c

[Sources.IA32]
  Ia32/Fsp.h
  Ia32/SecEntry.nasm
  Ia32/PeiCoreEntry.nasm
  Ia32/Stack.nasm

################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  IntelFsp2Pkg/IntelFsp2Pkg.dec
  IntelFsp2WrapperPkg/IntelFsp2WrapperPkg.dec

[LibraryClasses]
  LocalApicLib
  SerialPortLib
  DebugLib
  BaseMemoryLib

[Ppis]
  gEfiSecPlatformInformationPpiGuid       ## CONSUMES
  gPeiSecPerformancePpiGuid               ## CONSUMES
  gTopOfTemporaryRamPpiGuid               ## PRODUCES

[Pcd]
  gIntelFsp2WrapperTokenSpaceGuid.PcdFsptBaseAddress              ## CONSUMES
  gIntelFsp2WrapperTokenSpaceGuid.PcdFspmBaseAddress              ## CONSUMES

[FixedPcd]
  gIntelFsp2WrapperTokenSpaceGuid.PcdCpuMicrocodePatchAddress     ## CONSUMES
  gIntelFsp2WrapperTokenSpaceGuid.PcdCpuMicrocodePatchRegionSize  ## CONSUMES
  gIntelFsp2WrapperTokenSpaceGuid.PcdFlashMicrocodeOffset         ## CONSUMES
  gIntelFsp2WrapperTokenSpaceGuid.PcdFlashCodeCacheAddress        ## CONSUMES
  gIntelFsp2WrapperTokenSpaceGuid.PcdFlashCodeCacheSize           ## CONSUMES
