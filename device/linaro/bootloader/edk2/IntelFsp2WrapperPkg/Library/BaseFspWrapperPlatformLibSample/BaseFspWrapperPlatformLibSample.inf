## @file
#  Sample to provide FSP wrapper platform related function.
#
#  Copyright (c) 2014 - 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspWrapperPlatformLibSample
  FILE_GUID                      = 12F38E73-B34D-4559-99E5-AE2DCD002156
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspWrapperPlatformLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FspWrapperPlatformLibSample.c


################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  IntelFsp2Pkg/IntelFsp2Pkg.dec
  IntelFsp2WrapperPkg/IntelFsp2WrapperPkg.dec

[LibraryClasses]

[Pcd]
  gIntelFsp2PkgTokenSpaceGuid.PcdTemporaryRamBase  ## CONSUMES
  gIntelFsp2PkgTokenSpaceGuid.PcdTemporaryRamSize  ## CONSUMES
