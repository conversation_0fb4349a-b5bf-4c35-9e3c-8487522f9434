### @file
#  Provide FSP wrapper API test related function.
#
#  Copyright (C) 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010017
  BASE_NAME                      = BaseFspWrapperApiTestLibNull
  FILE_GUID                      = E7E96F88-017B-417C-8DC8-B84C2B877020
  VERSION_STRING                 = 1.0
  MODULE_TYPE                    = PEIM
  LIBRARY_CLASS                  = FspWrapperApiTestLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FspWrapperApiTestNull.c

################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  IntelFsp2Pkg/IntelFsp2Pkg.dec
  IntelFsp2WrapperPkg/IntelFsp2WrapperPkg.dec

[LibraryClasses]
  DebugLib

[Guids]