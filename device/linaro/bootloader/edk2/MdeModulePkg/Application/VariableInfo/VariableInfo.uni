// /** @file
// A shell application that displays statistical information about variable usage.
//
// This application can display statistical information about variable usage for SMM variable
// driver and non-SMM variable driver.
// Note that if Variable Dxe/Smm driver doesn't enable the feature by setting PcdVariableCollectStatistics
// as TRUE, the application will not display variable statistical information.
//
// Copyright (c) 2007 - 2016, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "A shell application that displays statistical information about variable usage"

#string STR_MODULE_DESCRIPTION          #language en-US "This application can display statistical information about variable usage for SMM variable driver and non-SMM variable driver. Note that if Variable DXE/SMM driver doesn't enable the feature by setting PcdVariableCollectStatistics as TRUE, the application will not display variable statistical information."

