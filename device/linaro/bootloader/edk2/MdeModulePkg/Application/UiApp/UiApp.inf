## @file
#  UiApp module is driver for BDS phase.
#
#  Copyright (c) 2011 - 2016, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#  
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UiApp
  MODULE_UNI_FILE                = UiApp.uni
  FILE_GUID                      = 462CAA21-7614-4503-836E-8AB6F4662331
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeUserInterface

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  FrontPage.h
  String.h
  Ui.h
  FrontPageVfr.Vfr
  FrontPageStrings.uni
  FrontPage.c
  String.c
  FrontPageCustomizedUi.c
  FrontPageCustomizedUiSupport.c
  FrontPageCustomizedUi.h
  FrontPageCustomizedUiSupport.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  DevicePathLib
  BaseLib
  UefiRuntimeServicesTableLib
  ReportStatusCodeLib
  MemoryAllocationLib
  UefiLib
  UefiBootServicesTableLib
  BaseMemoryLib
  DebugLib
  PrintLib
  HiiLib
  UefiApplicationEntryPoint
  PcdLib
  UefiHiiServicesLib
  UefiBootManagerLib
  
[Guids]
  gEfiIfrTianoGuid                              ## CONSUMES ## GUID (Extended IFR Guid Opcode)
  gEfiIfrFrontPageGuid                          ## CONSUMES ## GUID

[Protocols]
  gEfiSmbiosProtocolGuid                        ## CONSUMES
  gEfiHiiConfigAccessProtocolGuid               ## CONSUMES

[FeaturePcd]

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdUefiVariableDefaultPlatformLangCodes  ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdConOutRow                       ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdConOutColumn                    ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdVideoHorizontalResolution       ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdVideoVerticalResolution         ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupConOutColumn               ## CONSUMES ## SOMETIMES_PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupConOutRow                  ## CONSUMES ## SOMETIMES_PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupVideoHorizontalResolution  ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupVideoVerticalResolution    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdFirmwareVersionString           ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdTestKeyUsed                     ## CONSUMES

[UserExtensions.TianoCore."ExtraFiles"]
  UiAppExtra.uni
