// /** @file
// Shell application to dump UEFI memory and SMRAM profile information.
//
// Note that if the feature is not enabled by setting PcdMemoryProfilePropertyMask,
// the application will not display memory profile information.
//
// Copyright (c) 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Shell application to dump UEFI memory and SMRAM profile information."

#string STR_MODULE_DESCRIPTION          #language en-US "Note that if the feature is not enabled by setting PcdMemoryProfilePropertyMask, the application will not display memory profile information."

