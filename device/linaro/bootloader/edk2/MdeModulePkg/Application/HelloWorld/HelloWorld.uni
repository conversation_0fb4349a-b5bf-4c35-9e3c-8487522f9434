// /** @file
// Sample UEFI Application Reference EDKII Module.
//
// This is a sample shell application that will print "UEFI Hello World!" to the
// UEFI Console based on PCD setting.
// 
// It demos how to use EDKII PCD mechanism to make code more flexible.
//
// Copyright (c) 2008 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Sample UEFI Application Reference EDKII Module"

#string STR_MODULE_DESCRIPTION          #language en-US "This is a sample shell application that will print UEFI Hello World! to the UEFI Console based on PCD setting. It demonstrates how to use the EDKII PCD mechanism to make code more flexible"

