## @file
#  The application to show the Boot Manager Menu.
#
#  The application pops up a menu showing all the boot options referenced by
#  BootOrder NV variable and user can choose to boot from one of them.
#  
#  Copyright (c) 2011 - 2015, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#  
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BootManagerMenuApp
  MODULE_UNI_FILE                = BootManagerMenuApp.uni
  FILE_GUID                      = EEC25BDC-67F2-4D95-B1D5-F81B2039D11D
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0 
  ENTRY_POINT                    = BootManagerMenuEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  BootManagerMenu.c
  BootManagerMenu.h
  BootManagerMenuStrings.uni

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  HiiLib
  DebugLib
  UefiLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiApplicationEntryPoint
  UefiBootManagerLib
  
[Guids]

[Protocols]
  gEfiBootLogoProtocolGuid                      ## CONSUMES
  gEfiLoadedImageDevicePathProtocolGuid         ## CONSUMES

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdConOutRow                          ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdConOutColumn                       ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdVideoHorizontalResolution          ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdVideoVerticalResolution            ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupConOutColumn                  ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupConOutRow                     ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupVideoHorizontalResolution     ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetupVideoVerticalResolution       ## SOMETIMES_CONSUMES

[UserExtensions.TianoCore."ExtraFiles"]
  BootManagerMenuAppExtra.uni
