// /** @file
// The application to show the Boot Manager Menu.
//
// The application pops up a menu showing all the boot options referenced by
// BootOrder NV variable and user can choose to boot from one of them.
//
// Copyright (c) 2015, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php.
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "The application to show the Boot Manager Menu"

#string STR_MODULE_DESCRIPTION          #language en-US "The application pops up a menu showing all the boot options referenced by BootOrder NV variable and user can choose to boot from one of them."

