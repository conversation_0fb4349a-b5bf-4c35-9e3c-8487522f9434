##  @file
#  A shell application that triggers capsule update process.
#
# This application can trigger capsule update process. It can also
# generate capsule image, or dump capsule variable information.
#
#  Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CapsuleApp
  MODULE_UNI_FILE                = CapsuleApp.uni
  FILE_GUID                      = 4CEF31DA-8682-4274-9CC4-AEE7516A5E7B
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = UefiMain

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  CapsuleApp.c
  CapsuleDump.c
  AppSupport.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[Guids]
  gEfiFileInfoGuid                       ## CONSUMES   ## GUID
  gEfiPartTypeSystemPartGuid             ## CONSUMES   ## GUID
  gEfiGlobalVariableGuid                 ## CONSUMES   ## GUID
  gEfiCapsuleReportGuid                  ## CONSUMES   ## GUID
  gEfiFmpCapsuleGuid                     ## CONSUMES   ## GUID
  gWindowsUxCapsuleGuid                  ## CONSUMES   ## GUID
  gEfiCertTypeRsa2048Sha256Guid          ## CONSUMES   ## GUID
  gEfiCertPkcs7Guid                      ## CONSUMES   ## GUID
  gEfiSystemResourceTableGuid            ## CONSUMES   ## GUID

[Protocols]
  gEfiLoadedImageProtocolGuid            ## CONSUMES
  gEfiSimpleFileSystemProtocolGuid       ## CONSUMES
  gEfiGraphicsOutputProtocolGuid         ## CONSUMES
  gEfiFirmwareManagementProtocolGuid     ## CONSUMES
  gEfiShellParametersProtocolGuid        ## CONSUMES

[LibraryClasses]
  BaseLib
  UefiApplicationEntryPoint
  DebugLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib
  UefiLib
  PrintLib

[UserExtensions.TianoCore."ExtraFiles"]
  CapsuleAppExtra.uni
