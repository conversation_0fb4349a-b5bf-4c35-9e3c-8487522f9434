// /** @file
// A shell application that triggers capsule update process.
//
// This application can trigger capsule update process. It can also
// generate capsule image, or dump capsule variable information.
//
// Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "A shell application that triggers capsule update process."

#string STR_MODULE_DESCRIPTION          #language en-US "This application can trigger capsule update process. It can also generate capsule image, or dump capsule variable information."

