// /** @file
// This driver enumerates I2C devices on I2C bus and produce I2C IO Protocol on I2C devices.
//
// This driver enumerates I2C devices on I2C bus and produce I2C IO Protocol on I2C devices.
//
// Copyright (c) 2013 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "This driver enumerates I2C devices on I2C bus and produce I2C IO Protocol on I2C devices."

#string STR_MODULE_DESCRIPTION          #language en-US "This driver enumerates I2C devices on I2C bus and produce I2C IO Protocol on I2C devices."

