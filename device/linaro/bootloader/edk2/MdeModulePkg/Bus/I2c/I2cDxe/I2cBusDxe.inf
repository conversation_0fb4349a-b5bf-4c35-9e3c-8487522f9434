## @file
#  This driver enumerates I2C devices on I2C bus and produce I2C IO Protocol on I2C devices.
#
#  Copyright (c) 2013 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = I2cBusDxe
  MODULE_UNI_FILE                = I2cBusDxe.uni
  FILE_GUID                      = 0C34B372-2622-4A13-A46E-BFD0DEB48BFF
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeI2cBus
  UNLOAD_IMAGE                   = I2cBusUnload

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources.common]
  I2cDxe.h
  I2cBus.c

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DevicePathLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib

[Packages]
  MdePkg/MdePkg.dec

[Protocols]
  gEfiI2cIoProtocolGuid                             ## BY_START
  ## BY_START
  ## TO_START
  gEfiDevicePathProtocolGuid
  gEfiI2cEnumerateProtocolGuid                      ## TO_START
  gEfiI2cHostProtocolGuid                           ## TO_START

[UserExtensions.TianoCore."ExtraFiles"]
  I2cBusDxeExtra.uni

