## @file
#  This driver produce I2C Host Protocol on I2C controller handle.
#
#  Copyright (c) 2013 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = I2cHostDxe
  MODULE_UNI_FILE                = I2cHostDxe.uni
  FILE_GUID                      = CDEC3671-816E-43DC-A002-DCD645229338
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeI2cHost
  UNLOAD_IMAGE                   = I2cHostUnload

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources.common]
  I2cDxe.h
  I2cHost.c

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DevicePathLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib

[Packages]
  MdePkg/MdePkg.dec

[Protocols]
  gEfiI2cHostProtocolGuid                           ## BY_START
  gEfiI2cMasterProtocolGuid                         ## TO_START
  gEfiI2cBusConfigurationManagementProtocolGuid     ## TO_START

[UserExtensions.TianoCore."ExtraFiles"]
  I2cHostDxeExtra.uni

