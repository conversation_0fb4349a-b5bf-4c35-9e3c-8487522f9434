// /** @file
// ATA Bus driver to enumerate and identfy ATA devices.
//
// This driver follows UEFI driver model and layers on ATA Pass Thru protocol defined
// in UEFI spec 2.2. It installs Block IO and Disk Info protocol for each ATA device
// it enumerates and identifies successfully.
//
// Copyright (c) 2009 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "ATA Bus driver to enumerate and identify ATA devices"

#string STR_MODULE_DESCRIPTION          #language en-US "This driver follows the UEFI driver model and layers on the ATA Pass Thru protocol defined in UEFI Specification 2.2. It installs Block IO and Disk Info protocols for each ATA device it enumerates and identifies successfully."

