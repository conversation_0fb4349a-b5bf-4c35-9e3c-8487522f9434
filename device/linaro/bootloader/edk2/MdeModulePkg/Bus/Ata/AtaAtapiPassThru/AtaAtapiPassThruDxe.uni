// /** @file
// AtaAtapiPassThru driver to provide native IDE/AHCI mode support.
//
// This driver installs AtaPassThru and ExtScsiPassThru protocol in each ide/sata controller
// to access to all attached Ata/Atapi devices.
//
// Copyright (c) 2010 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "AtaAtapiPassThru driver to provide native IDE/AHCI mode support."

#string STR_MODULE_DESCRIPTION          #language en-US "This driver installs AtaPassThru and ExtScsiPassThru protocols in each IDE/SATA controller to access to all attached ATA/ATAPI devices."

