// /** @file
// The SataControllerDxe driver is responsible for managing the standard SATA controller.
//
// It consumes PciIo protocol and produces IdeControllerInit protocol for upper layer use.
//
// Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Responsible for managing the standard SATA controller"

#string STR_MODULE_DESCRIPTION          #language en-US "Implements the IdeControllerInit protocol interface for upper layer use\n"
 