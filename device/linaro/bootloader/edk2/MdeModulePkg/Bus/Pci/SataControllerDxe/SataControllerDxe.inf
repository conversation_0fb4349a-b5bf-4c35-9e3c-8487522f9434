## @file
#  
#    SataController driver to manage SATA compliance IDE/AHCI host controllers.
#
#  Copyright (c) 2011 - 2016, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#  
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SataController
  MODULE_UNI_FILE                = SataControllerDxe.uni
  FILE_GUID                      = 820C59BB-274C-43B2-83EA-DAC673035A59
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeSataControllerDriver

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC ARM AARCH64
#
#  DRIVER_BINDING                =  gSataControllerDriverBinding                        
#  COMPONENT_NAME                =  gSataControllerComponentName
#  COMPONENT_NAME2               =  gSataControllerComponentName2
#

[Sources]
  ComponentName.c
  SataController.c
  SataController.h

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  DebugLib
  UefiLib
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  UefiBootServicesTableLib

[Protocols]
  gEfiPciIoProtocolGuid                     ## TO_START
  gEfiIdeControllerInitProtocolGuid         ## BY_START

[UserExtensions.TianoCore."ExtraFiles"]
  SataControllerDxeExtra.uni

