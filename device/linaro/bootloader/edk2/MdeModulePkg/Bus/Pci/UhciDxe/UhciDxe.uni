// /** @file
// The UhciDxe driver is responsible for managing the behavior of UHCI controller.
//
// It implements the interfaces of monitoring the status of all ports and transferring
// Control, Bulk, Interrupt and Isochronous requests to Usb1.x device
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Responsible for managing the behavior of the UHCI controller"

#string STR_MODULE_DESCRIPTION          #language en-US "It implements the interfaces of monitoring the status of all ports and transferring Control, Bulk, Interrupt and Isochronous requests to a Usb1.x device."

