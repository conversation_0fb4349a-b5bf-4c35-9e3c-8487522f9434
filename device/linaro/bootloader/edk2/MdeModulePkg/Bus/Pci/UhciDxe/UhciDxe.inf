## @file
#  The UhciDxe driver is responsible for managing the behavior of UHCI controller. 
#  It implements the interfaces of monitoring the status of all ports and transferring
#  Control, Bulk, Interrupt and Isochronous requests to Usb1.x device
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UhciDxe
  MODULE_UNI_FILE                = UhciDxe.uni
  FILE_GUID                      = 2FB92EFA-2EE0-4bae-9EB6-7464125E1EF7
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  
  ENTRY_POINT                    = UhciDriverEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC ARM AARCH64
#
#  DRIVER_BINDING                =  gUhciDriverBinding                        
#  COMPONENT_NAME                =  gUhciComponentName
#  COMPONENT_NAME2               =  gUhciComponentName2
#

[Sources]
  UhciSched.c
  UhciDebug.c
  UsbHcMem.h
  UhciDebug.h
  UhciQueue.c
  UhciReg.c
  UsbHcMem.c
  UhciQueue.h
  Uhci.c
  Uhci.h
  UhciReg.h
  UhciSched.h
  ComponentName.c
  ComponentName.h


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[FeaturePcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdTurnOffUsbLegacySupport  ## CONSUMES

[LibraryClasses]
  MemoryAllocationLib
  BaseLib
  UefiLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  BaseMemoryLib
  DebugLib
  PcdLib
  ReportStatusCodeLib

[Guids]
  gEfiEventExitBootServicesGuid                 ## SOMETIMES_CONSUMES ## Event

[Protocols]
  gEfiPciIoProtocolGuid                         ## TO_START
  gEfiUsb2HcProtocolGuid                        ## BY_START

# [Event]
# EVENT_TYPE_PERIODIC_TIMER       ## CONSUMES
#

[UserExtensions.TianoCore."ExtraFiles"]
  UhciDxeExtra.uni
