// /** @file
// The UfsPciHcDxe driver is used by upper layer to retrieve mmio base address of managed pci-based Ufs host controller.
//
// Copyright (c) 2015, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Providing interface for upper layer to retrieve mmio base address of managed pci-based Ufs host controller."

#string STR_MODULE_DESCRIPTION          #language en-US "It implements the interface of getting mmio base address of managed pci-based Ufs host controller."

