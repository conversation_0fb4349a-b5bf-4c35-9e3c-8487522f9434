// /** @file
// PEIM to produce gEfiPeiVirtualBlockIoPpiGuid PPI for ATA controllers in the platform.
//
// This PPI can be consumed by PEIM which produce gEfiPeiDeviceRecoveryModulePpiGuid
// for Atapi CD ROM device.
// 
// This module discovers CDROM devices in Legacy and native mode and installs block IO ppis for them.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions
// of the BSD License which accompanies this distribution.  The
// full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// 
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "PEIM to produce gEfiPeiVirtualBlockIoPpiGuid PPI for ATA controllers in the platform"

#string STR_MODULE_DESCRIPTION          #language en-US "This PPI can be consumed by PEIM, which produces gEfiPeiDeviceRecoveryModulePpiGuid for an Atapi CD ROM device. This module discovers CDROM devices in Legacy and native mode and installs block IO ppis for them."

