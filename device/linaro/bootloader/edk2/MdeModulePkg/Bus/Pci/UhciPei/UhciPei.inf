## @file
# The UhcPeim driver is responsible for managing the behavior of UHCI controller at PEI phase.
#
# It produces gPeiUsbHostControllerPpiGuid based on gPeiUsbControllerPpiGuid which is used
# to enable recovery function from USB Drivers.
#
# Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions
# of the BSD License which accompanies this distribution.  The
# full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UhciPei
  MODULE_UNI_FILE                = UhciPei.uni
  FILE_GUID                      = C463CEAC-FC57-4f36-88B7-356C750C3BCA
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = UhcPeimEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  UhcPeim.c
  UhcPeim.h


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec


[LibraryClasses]
  IoLib
  TimerLib
  BaseMemoryLib
  PeiServicesLib
  PeimEntryPoint
  DebugLib


[Ppis]
  gPeiUsbHostControllerPpiGuid                  ## PRODUCES
  gPeiUsbControllerPpiGuid                      ## CONSUMES


[Depex]
  gEfiPeiMemoryDiscoveredPpiGuid AND gPeiUsbControllerPpiGuid AND gEfiPeiBootInRecoveryModePpiGuid

[UserExtensions.TianoCore."ExtraFiles"]
  UhciPeiExtra.uni
