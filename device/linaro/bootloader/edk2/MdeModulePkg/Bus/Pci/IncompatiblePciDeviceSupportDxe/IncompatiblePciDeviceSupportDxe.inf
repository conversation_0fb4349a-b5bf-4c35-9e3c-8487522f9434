## @file
#  PCI Incompatible device support module template.
#
#  Installs EFI PCI Incompatible Device Support protocol and includes one incompatile 
#  pci devices list template.
#
#  Copyright (c) 2009 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = IncompatiblePciDeviceSupport
  MODULE_UNI_FILE                = IncompatiblePciDeviceSupport.uni
  FILE_GUID                      = AD70855E-0CC5-4abf-8979-BE762A949EA3
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = IncompatiblePciDeviceSupportEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  IncompatiblePciDeviceSupport.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  MemoryAllocationLib
  DebugLib

[Protocols]
  gEfiIncompatiblePciDeviceSupportProtocolGuid    ## PRODUCES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  IncompatiblePciDeviceSupportExtra.uni
