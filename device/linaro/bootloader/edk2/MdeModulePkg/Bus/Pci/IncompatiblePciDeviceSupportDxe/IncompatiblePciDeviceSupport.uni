// /** @file
// PCI Incompatible device support module template.
//
// Installs EFI PCI Incompatible Device Support protocol and includes one incompatile
// pci devices list template.
//
// Copyright (c) 2009 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "PCI Incompatible device support module template"

#string STR_MODULE_DESCRIPTION          #language en-US "Installs EFI PCI Incompatible Device Support protocol and includes one incompatible PCI device list template."

