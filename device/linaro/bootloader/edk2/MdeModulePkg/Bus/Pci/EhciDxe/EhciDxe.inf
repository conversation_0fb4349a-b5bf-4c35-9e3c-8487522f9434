## @file
#  The EhciDxe driver is responsible for managing the behavior of EHCI controller.
#  It implements the interfaces of monitoring the status of all ports and transferring 
#  Control, Bulk, Interrupt and Isochronous requests to Usb2.0 device.
#
#  Note that EhciDxe driver is enhanced to guarantee that the EHCI controller get attached
#  to the EHCI controller before the UHCI driver attaches to the companion UHCI controller. 
#  This way avoids the control transfer on a shared port between EHCI and companion host
#  controller when UHCI gets attached earlier than EHCI and a USB 2.0 device inserts.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = EhciDxe
  MODULE_UNI_FILE                = EhciDxe.uni
  FILE_GUID                      = BDFE430E-8F2A-4db0-9991-6F856594777E
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = EhcDriverEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC ARM AARCH64
#
#  DRIVER_BINDING                =  gEhciDriverBinding                        
#  COMPONENT_NAME                =  gEhciComponentName
#  COMPONENT_NAME2               =  gEhciComponentName2
#

[Sources]
  UsbHcMem.h
  EhciUrb.c
  EhciReg.h
  UsbHcMem.c
  EhciSched.c
  EhciDebug.c
  EhciReg.c
  EhciDebug.h
  ComponentName.c
  ComponentName.h
  EhciUrb.h
  Ehci.h
  EhciSched.h
  Ehci.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[FeaturePcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdTurnOffUsbLegacySupport  ## CONSUMES

[LibraryClasses]
  MemoryAllocationLib
  BaseLib
  UefiLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  BaseMemoryLib
  DebugLib
  PcdLib
  ReportStatusCodeLib

[Guids]
  gEfiEventExitBootServicesGuid                 ## SOMETIMES_CONSUMES ## Event

[Protocols]
  gEfiPciIoProtocolGuid                         ## TO_START
  gEfiUsb2HcProtocolGuid                        ## BY_START

# [Event]
# EVENT_TYPE_PERIODIC_TIMER       ## CONSUMES
#

[UserExtensions.TianoCore."ExtraFiles"]
  EhciDxeExtra.uni
