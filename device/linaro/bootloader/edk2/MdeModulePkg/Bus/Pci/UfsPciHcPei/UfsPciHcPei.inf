## @file
#  Component Description File For Universal Flash Storage Pci Host Controller Pei Module.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UfsPciHcPei
  MODULE_UNI_FILE                = UfsPciHcPei.uni
  FILE_GUID                      = 905DC1AD-C44D-4965-98AC-B6B4444BFD65
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 0.9

  ENTRY_POINT                    = InitializeUfsHcPeim

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  UfsPciHcPei.c
  UfsPciHcPei.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  PciLib
  DebugLib
  PeiServicesLib
  MemoryAllocationLib
  PeimEntryPoint

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdUfsPciHostControllerMmioBase   ## CONSUMES

[Ppis]
  gEdkiiPeiUfsHostControllerPpiGuid         ## PRODUCES

[Depex]
  gEfiPeiMasterBootModePpiGuid AND gEfiPeiMemoryDiscoveredPpiGuid

[UserExtensions.TianoCore."ExtraFiles"]
  UfsPciHcPeiExtra.uni