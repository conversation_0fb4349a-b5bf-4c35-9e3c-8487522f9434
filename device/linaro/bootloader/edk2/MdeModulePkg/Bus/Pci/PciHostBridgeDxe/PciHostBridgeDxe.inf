## @file
#   Generic PCI Host Bridge driver.
#
#  Copyright (c) 2009 - 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PciHostBridgeDxe
  FILE_GUID                      = 128FB770-5E79-4176-9E51-9BB268A17DD1
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializePciHostBridge

[Sources]
  PciHostBridge.h
  PciRootBridge.h
  PciHostBridge.c
  PciRootBridgeIo.c
  PciHostResource.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  DxeServicesTableLib
  DevicePathLib
  BaseMemoryLib
  BaseLib
  PciSegmentLib
  PciHostBridgeLib

[Protocols]
  gEfiMetronomeArchProtocolGuid                   ## CONSUMES
  gEfiCpuIo2ProtocolGuid                          ## CONSUMES
  gEfiDevicePathProtocolGuid                      ## BY_START
  gEfiPciRootBridgeIoProtocolGuid                 ## BY_START
  gEfiPciHostBridgeResourceAllocationProtocolGuid ## BY_START

[Depex]
  gEfiCpuIo2ProtocolGuid AND
  gEfiMetronomeArchProtocolGuid AND
  gEfiCpuArchProtocolGuid
