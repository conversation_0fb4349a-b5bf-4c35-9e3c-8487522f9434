## @file
# Serial driver for standard UARTS on a SIO chip or PCI/PCIE card.
#
# Produces the Serial I/O protocol for standard UARTS using Super I/O or PCI I/O.
#
# Copyright (c) 2007 - 2015, Intel Corporation. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution. The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PciSioSerialDxe
  MODULE_UNI_FILE                = PciSioSerialDxe.uni
  FILE_GUID                      = E2775B47-D453-4EE3-ADA7-391A1B05AC17
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializePciSioSerial

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#
#  DRIVER_BINDING                =  gSerialControllerDriver
#  COMPONENT_NAME                =  gPciSioSerialComponentName
#  COMPONENT_NAME2               =  gPciSioSerialComponentName2
#

[Sources]
  ComponentName.c
  SerialIo.c
  Serial.h
  Serial.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  PcdLib
  ReportStatusCodeLib
  UefiBootServicesTableLib
  MemoryAllocationLib
  BaseMemoryLib
  DevicePathLib
  UefiLib
  UefiDriverEntryPoint
  DebugLib
  IoLib

[Guids]
  gEfiUartDevicePathGuid                        ## SOMETIMES_CONSUMES   ## GUID

[Protocols]
  gEfiSioProtocolGuid                           ## TO_START
  gEfiDevicePathProtocolGuid                    ## TO_START
  gEfiPciIoProtocolGuid                         ## TO_START
  gEfiSerialIoProtocolGuid                      ## BY_START
  gEfiDevicePathProtocolGuid                    ## BY_START

[FeaturePcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialUseHalfHandshake|FALSE   ## CONSUMES

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultBaudRate|115200    ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultDataBits|8         ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultParity|1           ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultStopBits|1         ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialClockRate|1843200 ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdPciSerialParameters     ## CONSUMES

[UserExtensions.TianoCore."ExtraFiles"]
  PciSioSerialDxeExtra.uni
