## @file
# Intel Framework Package Reference Implementations
#
# This package provides definitions and libraries that comply to Intel Framework Specifications.
# Copyright (c) 2007 - 2015, Intel Corporation. All rights reserved.<BR>
#
#    This program and the accompanying materials are licensed and made available
#    under the terms and conditions of the BSD License which accompanies this distribution.
#    The full text of the license may be found at http://opensource.org/licenses/bsd-license.php
#    THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS, WITHOUT WARRANTIES
#    OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = IntelFrameworkPkg
  PACKAGE_UNI_FILE               = IntelFrameworkPkg.uni
  PACKAGE_GUID                   = 2759ded5-bb57-4b06-af4f-c398fa552719
  PACKAGE_VERSION                = 0.96

[Includes]
  Include                        # Root include for the package

[Guids]
  ## Include/Guid/DataHubRecords.h
  gEfiCacheSubClassGuid          = { 0x7f0013a7, 0xdc79, 0x4b22, { 0x80, 0x99, 0x11, 0xf7, 0x5f, 0xdc, 0x82, 0x9d }}

  ## Include/Guid/DataHubRecords.h
  gEfiMemorySubClassGuid         = { 0x4E8F4EBB, 0x64B9, 0x4e05, { 0x9b, 0x18, 0x4c, 0xfe, 0x49, 0x23, 0x50, 0x97 }}

  ## Include/Guid/DataHubRecords.h
  gEfiMiscSubClassGuid           = { 0x772484B2, 0x7482, 0x4b91, { 0x9f, 0x9a, 0xad, 0x43, 0xf8, 0x1c, 0x58, 0x81 }}

  ## Include/Guid/DataHubRecords.h
  gEfiProcessorSubClassGuid      = { 0x26fdeb7e, 0xb8af, 0x4ccf, { 0xaa, 0x97, 0x02, 0x63, 0x3c, 0xe4, 0x8c, 0xa7 }}

  ## Include/Guid/Capsule.h
  gEfiCapsuleGuid                = { 0x3B6686BD, 0x0D76, 0x4030, { 0xB7, 0x0E, 0xB5, 0x51, 0x9E, 0x2F, 0xC5, 0xA0 }}

  ## Include/Guid/Capsule.h
  gEfiConfigFileNameGuid         = { 0x98B8D59B, 0xE8BA, 0x48EE, { 0x98, 0xDD, 0xC2, 0x95, 0x39, 0x2F, 0x1E, 0xDB }}

  ## Include/Guid/SmramMemoryReserve.h
  gEfiSmmPeiSmramMemoryReserveGuid = { 0x6dadf1d1, 0xd4cc, 0x4910, { 0xbb, 0x6e, 0x82, 0xb1, 0xfd, 0x80, 0xff, 0x3d }}

  ## Include/Guid/SmmCommunicate.h
  gSmmCommunicateHeaderGuid      = { 0xf328e36c, 0x23b6, 0x4a95, { 0x85, 0x4b, 0x32, 0xe1, 0x95, 0x34, 0xcd, 0x75 }}

  ## Include/Guid/FirmwareFileSystem.h
  gEfiFirmwareFileSystemGuid     = { 0x7A9354D9, 0x0468, 0x444a, {0x81, 0xCE, 0x0B, 0xF6, 0x17, 0xD8, 0x90, 0xDF }}

  ## Include/Guid/BlockIo.h
  gEfiPeiIdeBlockIoPpiGuid       = { 0x964e5b22, 0x6459, 0x11d2, { 0x8e, 0x39, 0x00, 0xa0, 0xc9, 0x69, 0x72, 0x3b }}

  ## Include/Guid/BlockIo.h
  gEfiPei144FloppyBlockIoPpiGuid = { 0xda6855bd, 0x07b7, 0x4c05, { 0x9e, 0xd8, 0xe2, 0x59, 0xfd, 0x36, 0x0e, 0x22 }}

[Ppis]
  ## Include/Ppi/BootScriptExecuter.h
  gEfiPeiBootScriptExecuterPpiGuid  = { 0xabd42895, 0x78cf, 0x4872, { 0x84, 0x44, 0x1b, 0x5c, 0x18, 0x0b, 0xfb, 0xff }}

  ## Include/Ppi/Security.h
  gEfiPeiSecurityPpiGuid            = { 0x1388066E, 0x3A57, 0x4EFA, { 0x98, 0xF3, 0xC1, 0x2F, 0x3A, 0x95, 0x8A, 0x29 }}

  ## Include/Ppi/Smbus.h
  gEfiPeiSmbusPpiGuid               = { 0xabd42895, 0x78cf, 0x4872, { 0x84, 0x44, 0x1b, 0x5c, 0x18, 0x0b, 0xfb, 0xda }}

  ## Include/Ppi/PciCfg.h
  gEfiPciCfgPpiInServiceTableGuid   = { 0xe1f2eba0, 0xf7b9, 0x4a26, { 0x86, 0x20, 0x13, 0x12, 0x21, 0x64, 0x2a, 0x90 }}

  ## Include/Ppi/ReadOnlyVariable.h
  gEfiPeiReadOnlyVariablePpiGuid    = { 0x3CDC90C6, 0x13FB, 0x4A75, { 0x9E, 0x79, 0x59, 0xE9, 0xDD, 0x78, 0xB9, 0xFA }}

  ## Include/Ppi/SectionExtraction.h
  gEfiPeiSectionExtractionPpiGuid   = { 0x4F89E208, 0xE144, 0x4804, { 0x9E, 0xC8, 0x0F, 0x89, 0x4F, 0x7E, 0x36, 0xD7 }}

  ## Include/Ppi/FvLoadFile.h
  gEfiPeiFvFileLoaderPpiGuid        = { 0x7e1f0d85, 0x4ff,  0x4bb2, { 0x86, 0x6a, 0x31, 0xa2, 0x99, 0x6a, 0x48, 0xa8 }}

  ## Include/Ppi/FindFv.h
  gEfiFindFvPpiGuid                 = { 0x36164812, 0xa023, 0x44e5, { 0xbd, 0x85, 0x05, 0xbf, 0x3c, 0x77, 0x00, 0xaa }}
  
  ## Include/Ppi/S3Resume.h
  gEfiPeiS3ResumePpiGuid            = { 0x4426CCB2, 0xE684, 0x4a8a, { 0xae, 0x40, 0x20, 0xd4, 0xb0, 0x25, 0xb7, 0x10 }}

[Protocols]
  ## Include/Protocol/AcpiS3Save.h
  gEfiAcpiS3SaveProtocolGuid     = { 0x125F2DE1, 0xFB85, 0x440C, { 0xA5, 0x4C, 0x4D, 0x99, 0x35, 0x8A, 0x8D, 0x38 }}

  ## Include/Protocol/AcpiSupport.h
  gEfiAcpiSupportProtocolGuid    = { 0xdbff9d55, 0x89b7, 0x46da, { 0xbd, 0xdf, 0x67, 0x7d, 0x3d, 0xc0, 0x24, 0x1d }}

  ## Include/Protocol/BootScriptSave.h
  gEfiBootScriptSaveProtocolGuid = { 0x470e1529, 0xb79e, 0x4e32, { 0xa0, 0xfe, 0x6a, 0x15, 0x6d, 0x29, 0xf9, 0xb2 }}

  ## Include/Protocol/LegacyBios.h
  gEfiLegacyBiosProtocolGuid     = { 0xdb9a1e3d, 0x45cb, 0x4abb, { 0x85, 0x3b, 0xe5, 0x38, 0x7f, 0xdb, 0x2e, 0x2d }}

  ## Include/Protocol/LegacyBiosPlatform.h
  gEfiLegacyBiosPlatformProtocolGuid = { 0x783658a3, 0x4172, 0x4421, { 0xa2, 0x99, 0xe0, 0x09, 0x07, 0x9c, 0x0c, 0xb4 }}

  ## Include/Protocol/LegacyInterrupt.h
  gEfiLegacyInterruptProtocolGuid = { 0x31ce593d, 0x108a, 0x485d, { 0xad, 0xb2, 0x78, 0xf2, 0x1f, 0x29, 0x66, 0xbe }}

  ## Include/Protocol/LegacyRegion.h
  gEfiLegacyRegionProtocolGuid   = { 0x0fc9013a, 0x0568, 0x4ba9, { 0x9b, 0x7e, 0xc9, 0xc3, 0x90, 0xa6, 0x60, 0x9b }}

  ## Include/Protocol/Legacy8259.h
  gEfiLegacy8259ProtocolGuid     = { 0x38321dba, 0x4fe0, 0x4e17, { 0x8a, 0xec, 0x41, 0x30, 0x55, 0xea, 0xed, 0xc1 }}

  ## Include/Protocol/CpuIo.h
  gEfiCpuIoProtocolGuid          = { 0xB0732526, 0x38C8, 0x4b40, { 0x88, 0x77, 0x61, 0xc7, 0xb0, 0x6a, 0xac, 0x45 }}

  ## Include/Protocol/DataHub.h
  gEfiDataHubProtocolGuid        = { 0xae80d021, 0x618e, 0x11d4, { 0xbc, 0xd7, 0x00, 0x80, 0xc7, 0x3c, 0x88, 0x81 }}

  ## Include/Protocol/FirmwareVolume.h
  gEfiFirmwareVolumeProtocolGuid = { 0x389F751F, 0x1838, 0x4388, { 0x83, 0x90, 0xcd, 0x81, 0x54, 0xbd, 0x27, 0xf8 }}

  ## Include/Protocol/SectionExtraction.h
  gEfiSectionExtractionProtocolGuid = { 0x448F5DA4, 0x6DD7, 0x4FE1, { 0x93, 0x07, 0x69, 0x22, 0x41, 0x92, 0x21, 0x5D }}

  ## Include/Protocol/FrameworkHii.h
  gEfiHiiProtocolGuid            = { 0xd7ad636e, 0xb997, 0x459b, { 0xbf, 0x3f, 0x88, 0x46, 0x89, 0x79, 0x80, 0xe1 }}

  ## Include/Protocol/FrameworkHii.h
  gEfiHiiCompatibilityProtocolGuid = { 0x5542cce1, 0xdf5c, 0x4d1b, { 0xab, 0xca, 0x36, 0x4f, 0x77, 0xd3, 0x99, 0xfb }}

  ## Include/Protocol/FrameworkMpService.h
  gFrameworkEfiMpServiceProtocolGuid = { 0xf33261e7, 0x23cb, 0x11d5, {0xbd, 0x5c, 0x0, 0x80, 0xc7, 0x3c, 0x88, 0x81}}

  ## Include/Protocol/SmmBase.h
  gEfiSmmBaseProtocolGuid        = { 0x1390954D, 0xda95, 0x4227, { 0x93, 0x28, 0x72, 0x82, 0xc2, 0x17, 0xda, 0xa8 }}

  ## Include/Protocol/SmmAccess.h
  gEfiSmmAccessProtocolGuid      = { 0x3792095a, 0xe309, 0x4c1e, { 0xaa, 0x01, 0x85, 0xf5, 0x65, 0x5a, 0x17, 0xf1 }}

  ## Include/Protocol/SmmControl.h
  gEfiSmmControlProtocolGuid     = { 0x8d12e231, 0xc667, 0x4fd1, { 0x98, 0xf2, 0x24, 0x49, 0xa7, 0xe7, 0xb2, 0xe5 }}

  ## Include/Protocol/SmmSwDispatch.h
  gEfiSmmSwDispatchProtocolGuid  = { 0xe541b773, 0xdd11, 0x420c, { 0xb0, 0x26, 0xdf, 0x99, 0x36, 0x53, 0xf8, 0xbf }}

  ## Include/Protocol/SmmSxDispatch.h
  gEfiSmmSxDispatchProtocolGuid  = { 0x14fc52be, 0x01dc, 0x426c, { 0x91, 0xae, 0xa2, 0x3c, 0x3e, 0x22, 0x0a, 0xe8 }}

  ## Include/Protocol/SmmPeriodicTimerDispatch.h
  gEfiSmmPeriodicTimerDispatchProtocolGuid = { 0x9cca03fc, 0x4c9e, 0x4a19, { 0x9b, 0x06, 0xed, 0x7b, 0x47, 0x9b, 0xde, 0x55 }}

  ## Include/Protocol/SmmUsbDispatch.h
  gEfiSmmUsbDispatchProtocolGuid = { 0xa05b6ffd, 0x87af, 0x4e42, { 0x95, 0xc9, 0x62, 0x28, 0xb6, 0x3c, 0xf3, 0xf3 }}

  ## Include/Protocol/SmmGpiDispatch.h
  gEfiSmmGpiDispatchProtocolGuid = { 0xe0744b81, 0x9513, 0x49cd, { 0x8c, 0xea, 0xe9, 0x24, 0x5e, 0x70, 0x39, 0xda }}

  ## Include/Protocol/SmmStandbyButtonDispatch.h
  gEfiSmmStandbyButtonDispatchProtocolGuid = { 0x78965b98, 0xb0bf, 0x449e, { 0x8b, 0x22, 0xd2, 0x91, 0x4e, 0x49, 0x8a, 0x98 }}

  ## Include/Protocol/SmmPowerButtonDispatch.h
  gEfiSmmPowerButtonDispatchProtocolGuid = { 0xb709efa0, 0x47a6, 0x4b41, { 0xb9, 0x31, 0x12, 0xec, 0xe7, 0xa8, 0xee, 0x56 }}

  ## Include/Protocol/SmmIchnDispatch.h
  gEfiSmmIchnDispatchProtocolGuid = { 0xc50b323e, 0x9075, 0x4f2a, { 0xac, 0x8e, 0xd2, 0x59, 0x6a, 0x10, 0x85, 0xcc }}

  ## Include/Protocol/SmmCpuIo.h
  gEfiSmmCpuIoGuid  = { 0x5f439a0b, 0x45d8, 0x4682, {0xa4, 0xf4, 0xf0, 0x57, 0x6b, 0x51, 0x34, 0x41}}
  
  ## Include/Protocol/FrameworkFormCallback.h
  gEfiFormCallbackProtocolGuid   = { 0xF3E4543D, 0xCF35, 0x6CEF, { 0x35, 0xC4, 0x4F, 0xE6, 0x34, 0x4D, 0xFC, 0x54 }}

  ## Include/Protocol/FrameworkFormBrowser.h
  gEfiFormBrowserProtocolGuid    = { 0xE5A1333E, 0xE1B4, 0x4D55, { 0xCE, 0xEB, 0x35, 0xC3, 0xEF, 0x13, 0x34, 0x43 }}

  ## Include/Protocol/FrameworkFormBrowser.h
  gEfiFormBrowserCompatibilityProtocolGuid    = { 0xfb7c852, 0xadca, 0x4853, { 0x8d, 0xf, 0xfb, 0xa7, 0x1b, 0x1c, 0xe1, 0x1a }}
  
  ## Include/Protocol/FrameworkFirmwareVolumeBlock.h
  gFramerworkEfiFirmwareVolumeBlockProtocolGuid = { 0xDE28BC59, 0x6228, 0x41BD, { 0xBD, 0xF6, 0xA3, 0xB9, 0xAD, 0xB5, 0x8D, 0xA1 }}    

  ## Include/Protocol/SmmCpuSaveState.h
  gEfiSmmCpuSaveStateProtocolGuid = { 0x21f302ad, 0x6e94, 0x471b, {0x84, 0xbc, 0xb1, 0x48, 0x0, 0x40, 0x3a, 0x1d}}


[UserExtensions.TianoCore."ExtraFiles"]
  IntelFrameworkPkgExtra.uni
