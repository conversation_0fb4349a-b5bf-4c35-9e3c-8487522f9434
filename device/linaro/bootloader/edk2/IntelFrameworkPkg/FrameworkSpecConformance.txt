##
# This file is used to document mismatches between Intel Platform Innovation Framework specification
# (http://www.intel.com/technology/framework/spec.htm) and data structures defind at IntelFrameworkPkg
# package in EdkII Open Source Project (https://github.com/tianocore/edk2/tree/master/IntelFrameworkPkg)
##

##
# The general consideration about keeping the mismatches in EdkII:
# 1. Some definitions defined in Framework specification may bring a little complexity on implementation. EdkII
#    makes changes on them from the view of code development.
# 2. Some definitions are NOT defined in Framework specification, but introduced in Edk. EdkII chooses to keep
#    them for backward-compatibility.
# 3. The name of some definitions are NOT consistent with Framework specification. If the name doesn't bring
#    misunderstanding literally, EdkII chooses to keep them for backward-compatibility.
# 4. Some defintitions don't exactly match Framework specification, some new field members are introduced in EdkII
#    to reflect the latest industry standard.
#
# Note: 
#    The IntelFrameworkPkg contains Framework specification contents that were not adopted by UEFI/PI, and names may be
#    changed (such as adding "FRAMEWORK_") to avoid name collisions with approved UEFI/PI specifications.
##

##
# Mismatch with Intel Platform Innovation Framework for DataHubSubclass Specification (Version 0.90)
##
  1. Guid/DataHubRecords.h
    #define EFI_STRING_TOKEN          UINT16

    This macro named "EFI_STRING_TOKEN" is *NOT* defined in Framework specification. Keeping this inconsistency
    for backward compatibility.

  2. Guid/DataHubRecords.h
    #pragma pack(1)
    typedef struct {
      UINT8                             LastPciBus;
    } EFI_MISC_LAST_PCI_BUS_DATA;
    ...
    typedef struct {
      EFI_SUBCLASS_TYPE1_HEADER         Header;
      EFI_MISC_SUBCLASS_RECORDS         Record;
    } EFI_MISC_SUBCLASS_DRIVER_DATA;
    #pragma pack()

    Section "Alignment" in DataHubSubclass specification say "Fields in a data hub record should be aligned at their
    natural boundaries". But in EdkII, the data structures above are packed. 
    Keeping this inconsistency for backward compatibility.
    
  3. Guid/DataHubRecords.h
    #define EFI_SUBCLASS_INSTANCE_RESERVED       0
    #define EFI_SUBCLASS_INSTANCE_NON_APPLICABLE 0xFFFF

    The symbols above are *NOT* defined in DataHubSubclass specification. But the values are defined and are meaningful.
    According to DataHubSubclass spec, value 0 means Reserved and -1 means Not Applicable. EdkII introduces these macros
    to faciliate user development.

##
# Mismatch with Intel Platform Innovation Framework for CacheSubclass Specification (Version 0.90)
##
  1. Guid/DataHubRecords.h
    typedef EFI_EXP_BASE2_DATA       EFI_MAXIMUM_CACHE_SIZE_DATA;

    The definition named "EFI_MAXIMUM_CACHE_SIZE_DATA" is *NOT* consistent with CacheSubclass specification, in which
    the name should be EFI_CACHE_MAXIMUM_SIZE_DATA. Keeping this inconsistency for backward compatibility.

  2. Guid/DataHubRecords.h
    typedef struct {
      UINT32                          Level           :3;
      UINT32                          Socketed        :1;
      UINT32                          Reserved2       :1;
      UINT32                          Location        :2;
      UINT32                          Enable          :1;
      UINT32                          OperationalMode :2;
      UINT32                          Reserved1       :22;
    } EFI_CACHE_CONFIGURATION_DATA;

    The field type of the definition is *NOT* consistent with CacheSubclass specification. Specification defines
    them as UINT16, which is incorrect and should be UINT32 because the total width of bit-fields is 32bits width.
    
  3. Guid/DataHubRecords.h
    typedef enum {
      CacheSizeRecordType              = 1,
      MaximumSizeCacheRecordType       = 2,
      CacheSpeedRecordType             = 3,
      CacheSocketRecordType            = 4,
      CacheSramTypeRecordType          = 5,
      CacheInstalledSramTypeRecordType = 6,
      CacheErrorTypeRecordType         = 7,
      CacheTypeRecordType              = 8,
      CacheAssociativityRecordType     = 9,
      CacheConfigRecordType            = 10
    } EFI_CACHE_VARIABLE_RECORD_TYPE;

    The data structure and all enumeration fields are *NOT* defined in CacheSubclass specification, which only
    defines the following macros to specify the record number of the data record:
      #define EFI_CACHE_SIZE_RECORD_NUMBER                    0x00000001
      #define EFI_CACHE_MAXIMUM_SIZE_RECORD_NUMBER            0x00000002
      #define EFI_CACHE_SPEED_RECORD_NUMBER                   0x00000003
      #define EFI_CACHE_SOCKET_RECORD_NUMBER                  0x00000004
      #define EFI_CACHE_SRAM_SUPPORT_RECORD_NUMBER            0x00000005 
      #define EFI_CACHE_SRAM_INSTALL_RECORD_NUMBER            0x00000006 
      #define EFI_CACHE_ERROR_SUPPORT_RECORD_NUMBER           0x00000007
      #define EFI_CACHE_TYPE_RECORD_NUMBER                    0x00000008
      #define EFI_CACHE_ASSOCIATIVITY_RECORD_NUMBER           0x00000009
      #define EFI_CACHE_CONFIGURATION_RECORD_NUMBER           0x0000000A
    Keeping this inconsistency for backward compatibility.

  4. Guid/DataHubRecords.h
    typedef union {
      EFI_CACHE_SIZE_DATA               CacheSize;
      ...
      EFI_CACHE_ASSOCIATION_DATA        CacheAssociation;
    } EFI_CACHE_VARIABLE_RECORD;

    typedef struct {
       EFI_SUBCLASS_TYPE1_HEADER        DataRecordHeader;
       EFI_CACHE_VARIABLE_RECORD        VariableRecord;
    } EFI_CACHE_DATA_RECORD;

    The definitions above are *NOT* defined in CacheSubclass specification. EdkII introduces them to simplify the
    code logic. Therefore developer doesn't need to allocate memory dynamically to construct variable length data record.
    Keeping this inconsistency for backward compatibility.

##
# Mismatch with Intel Platform Innovation Framework for ProcSubclass Specification (Version 0.90)
##
  1. Guid/DataHubRecords.h
    #define EFI_PROCESSOR_SUBCLASS_VERSION    0x00010000

    The value of the definition is *NOT* consistent with ProcSubclass specification, in which the value is 0x0100.
    Keeping this inconsistency from the perspective of binary consistency.

  2. Guid/DataHubRecords.h
    typedef struct {
      UINT32                            ProcessorBrandIndex    :8;
      UINT32                            ProcessorClflush       :8;
      UINT32                            ProcessorReserved      :8;
      UINT32                            ProcessorDfltApicId    :8;
    } EFI_PROCESSOR_MISC_INFO;

    The definition is *NOT* consistent with ProcSubclass specification, in which the name of third field is defined
    as "LogicalProcessorCount" rather than "ProcessorReserved".
    Keeping this inconsistency for backward compatibility.

  3. Guid/DataHubRecords.h
    typedef enum {
      ...
      EfiProcessorFamilyUltraSparcIIIi         = 0x58,
      ...
      EfiProcessorFamilyIntelPentiumM          = 0xB9,
      EfiProcessorFamilyIntelCeleronD          = 0xBA,
      EfiProcessorFamilyIntelPentiumD          = 0xBB,
      EfiProcessorFamilyIntelPentiumEx         = 0xBC,
      EfiProcessorFamilyIntelCoreSolo          = 0xBD,  
      EfiProcessorFamilyReserved               = 0xBE,  
      EfiProcessorFamilyIntelCore2             = 0xBF,
      ...
      EfiProcessorFamilyG6                     = 0xCB,
      EfiProcessorFamilyzArchitectur           = 0xCC,
      EfiProcessorFamilyViaC7M                 = 0xD2,
      EfiProcessorFamilyViaC7D                 = 0xD3,
      EfiProcessorFamilyViaC7                  = 0xD4,
      EfiProcessorFamilyViaEden                = 0xD5,
      ...
      EfiProcessorFamilyIndicatorFamily2       = 0xFE,
      EfiProcessorFamilyReserved1              = 0xFF
    } EFI_PROCESSOR_FAMILY_DATA;

    a. In ProcSubclass specification 0.9, the field name whose value equals to 0x58 is "EfiProcessorFamilyUltraSparcIIi".
       Due to the name has been defined in previous field, changing it to "EfiProcessorFamilyUltraSparcIIIi" to avoid
       build break.
    b. The other fields listed here are *NOT* defined in ProcSubclass specification 0.9. They are introduced to
       support new processor family (type 4) defined in SmBios 2.6 specification.
       Keeping this inconsistency to reflect the latest industry standard.

  4. Guid/DataHubRecords.h
    typedef enum {
      ...
      EfiProcessorSocket939              = 0x12,
      EfiProcessorSocketmPGA604          = 0x13,
      EfiProcessorSocketLGA771           = 0x14,
      EfiProcessorSocketLGA775           = 0x15
    } EFI_PROCESSOR_SOCKET_TYPE_DATA;

    The fields listed here are *NOT* defined in ProcSubclass specification 0.9. They are introduced to support
    new processor upgrade (type 4 offset 19h) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  5. Guid/DataHubRecords.h
    typedef EFI_INTER_LINK_DATA         EFI_CACHE_ASSOCIATION_DATA;

    The definition name "EFI_CACHE_ASSOCIATION_DATA" is *NOT* consistent with ProcSubclass specification 0.9, in which
    the name should be "EFI_PROCESSOR_CACHE_ASSOCIATION_DATA". Keeping this inconsistency for backward compatibility.

  6. Guid/DataHubRecords.h
    typedef enum {
      EfiProcessorHealthy        = 1,
      EfiProcessorPerfRestricted = 2,
      EfiProcessorFuncRestricted = 3 
    } EFI_PROCESSOR_HEALTH_STATUS;

    The structure name "EFI_PROCESSOR_HEALTH_STATUS" is *NOT* consistent with ProcSubclass specification 0.9, in which
    the name should be "EFI_PROCESSOR_HEALTH_STATUS_DATA". Keeping this inconsistency for backward compatibility.

  7. Guid/DataHubRecords.h
    typedef enum {
      ProcessorCoreFrequencyRecordType     = 1,
      ProcessorFsbFrequencyRecordType      = 2,
      ProcessorVersionRecordType           = 3,
      ProcessorManufacturerRecordType      = 4,
      ProcessorSerialNumberRecordType      = 5,
      ProcessorIdRecordType                = 6,
      ProcessorTypeRecordType              = 7,
      ProcessorFamilyRecordType            = 8,
      ProcessorVoltageRecordType           = 9,
      ProcessorApicBaseAddressRecordType   = 10,
      ProcessorApicIdRecordType            = 11,
      ProcessorApicVersionNumberRecordType = 12,
      CpuUcodeRevisionDataRecordType       = 13,
      ProcessorStatusRecordType            = 14,
      ProcessorSocketTypeRecordType        = 15,
      ProcessorSocketNameRecordType        = 16,
      CacheAssociationRecordType           = 17,
      ProcessorMaxCoreFrequencyRecordType  = 18,
      ProcessorAssetTagRecordType          = 19,
      ProcessorMaxFsbFrequencyRecordType   = 20,
      ProcessorPackageNumberRecordType     = 21,
      ProcessorCoreFrequencyListRecordType = 22,
      ProcessorFsbFrequencyListRecordType  = 23,
      ProcessorHealthStatusRecordType      = 24,
      ProcessorCoreCountRecordType         = 25,
      ProcessorEnabledCoreCountRecordType  = 26,
      ProcessorThreadCountRecordType       = 27,
      ProcessorCharacteristicsRecordType   = 28,
      ProcessorFamily2RecordType           = 29,
      ProcessorPartNumberRecordType        = 30,
    } EFI_CPU_VARIABLE_RECORD_TYPE;

    The enumeration fields from ProcessorCoreFrequencyRecordType to ProcessorHealthStatusRecordType are *NOT* defined 
    in ProcSubclass specification 0.9, which only defines the following macros to specify the record number of the data record:
      #define EFI_PROCESSOR_FREQUENCY_RECORD_NUMBER           0x00000001
      #define EFI_PROCESSOR_BUS_FREQUENCY_RECORD_NUMBER       0x00000002
      #define EFI_PROCESSOR_VERSION_RECORD_NUMBER             0x00000003
      #define EFI_PROCESSOR_MANUFACTURER_RECORD_NUMBER        0x00000004
      #define EFI_PROCESSOR_SERIAL_NUMBER_RECORD_NUMBER       0x00000005
      #define EFI_PROCESSOR_ID_RECORD_NUMBER                  0x00000006
      #define EFI_PROCESSOR_TYPE_RECORD_NUMBER                0x00000007
      #define EFI_PROCESSOR_FAMILY_RECORD_NUMBER              0x00000008
      #define EFI_PROCESSOR_VOLTAGE_RECORD_NUMBER             0x00000009
      #define EFI_PROCESSOR_APIC_BASE_ADDRESS_RECORD_NUMBER   0x0000000A
      #define EFI_PROCESSOR_APIC_ID_RECORD_NUMBER             0x0000000B
      #define EFI_PROCESSOR_APIC_VER_NUMBER_RECORD_NUMBER     0x0000000C
      #define EFI_PROCESSOR_MICROCODE_REVISION_RECORD_NUMBER  0x0000000D
      #define EFI_PROCESSOR_STATUS_RECORD_NUMBER              0x0000000E
      #define EFI_PROCESSOR_SOCKET_TYPE_RECORD_NUMBER         0x0000000F
      #define EFI_PROCESSOR_SOCKET_NAME_RECORD_NUMBER         0x00000010
      #define EFI_PROCESSOR_CACHE_ASSOCIATION_RECORD_NUMBER   0x00000011
      #define EFI_PROCESSOR_MAX_FREQUENCY_RECORD_NUMBER       0x00000012
      #define EFI_PROCESSOR_ASSET_TAG_RECORD_NUMBER           0x00000013
      #define EFI_PROCESSOR_MAX_FSB_FREQUENCY_RECORD_NUMBER   0x00000014
      #define EFI_PROCESSOR_PACKAGE_NUMBER_RECORD_NUMBER      0x00000015
      #define EFI_PROCESSOR_FREQUENCY_LIST_RECORD_NUMBER      0x00000016
      #define EFI_PROCESSOR_FSB_FREQUENCY_LIST_RECORD_NUMBER  0x00000017
      #define EFI_PROCESSOR_HEALTH_STATUS_RECORD_NUMBER       0x00000018
    Keeping this inconsistency for backward compatibility.

    The enumeration fields from ProcessorCoreCountRecordType to ProcessorPartNumberRecordType are *NOT* defined 
    in ProcSubclass specification 0.9. 
    They are introduced to support new fields for type 4 defined in SmBios 2.6 specification.
    Keeping this inconsistency to reflect the latest industry standard.

  8. Guid/DataHubRecords.h
    typedef union {
      EFI_PROCESSOR_CORE_FREQUENCY_LIST_DATA  ProcessorCoreFrequencyList;
      ...
      EFI_PROCESSOR_FAMILY2_DATA              ProcessorFamily2;
    } EFI_CPU_VARIABLE_RECORD;
    
    typedef struct {
      EFI_SUBCLASS_TYPE1_HEADER         DataRecordHeader;
      EFI_CPU_VARIABLE_RECORD           VariableRecord;
    } EFI_CPU_DATA_RECORD;

    The definitions above are *NOT* defined in ProcSubclass specification 0.9. EdkII introduces them to simplify the
    code logic. Therefore developer doesn't need to allocate memory dynamically to construct variable length data record.
    Keeping this inconsistency for backward compatibility.

  9. Guid/DataHubRecords.h
    typedef STRING_REF                  EFI_PROCESSOR_PART_NUMBER_DATA;

    typedef enum {
      EfiProcessorFamilySh3           = 0x104,
      EfiProcessorFamilySh4           = 0x105,
      EfiProcessorFamilyArm           = 0x118,
      EfiProcessorFamilyStrongArm     = 0x119,
      EfiProcessorFamily6x86          = 0x12C,
      EfiProcessorFamilyMediaGx       = 0x12D,
      EfiProcessorFamilyMii           = 0x12E,
      EfiProcessorFamilyWinChip       = 0x140,
      EfiProcessorFamilyDsp           = 0x15E,
      EfiProcessorFamilyVideo         = 0x1F4
    } EFI_PROCESSOR_FAMILY2_DATA;

    typedef UINT8                       EFI_PROCESSOR_CORE_COUNT_DATA;

    typedef UINT8                       EFI_PROCESSOR_ENABLED_CORE_COUNT_DATA;

    typedef UINT8                       EFI_PROCESSOR_THREAD_COUNT_DATA;

    typedef struct {
      UINT16  Reserved              :1;
      UINT16  Unknown               :1;
      UINT16  Capable64Bit          :1;
      UINT16  Reserved2             :13;
    } EFI_PROCESSOR_CHARACTERISTICS_DATA;

    The fields listed here are *NOT* defined in ProcSubclass specification 0.9. They are introduced to support
    new fields for type 4 defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

##
# Mismatch with Intel Platform Innovation Framework for MemSubclass Specification (Version 0.90)
##
  1. Guid/DataHubRecords.h
    typedef enum _EFI_MEMORY_FORM_FACTOR {
      ...
      EfiMemoryFormFactorFbDimm                   = 0x0F
    } EFI_MEMORY_FORM_FACTOR;
    
    typedef enum _EFI_MEMORY_ARRAY_TYPE {
      ...
      EfiMemoryTypeDdr2                           = 0x13,
      EfiMemoryTypeDdr2FbDimm                     = 0x14
    } EFI_MEMORY_ARRAY_TYPE;
    
    typedef enum {
      ...
      EfiMemoryStatePartial      = 6
    } EFI_MEMORY_STATE;

    The fields listed above are *NOT* defined in MemSubclass specification 0.9. They are introduced to support
    new memory device (type 17) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  2. Guid/DataHubRecords.h
    typedef struct { 
      ...
      EFI_EXP_BASE10_DATA      MemorySpeed; 
      ...
    } EFI_MEMORY_ARRAY_LINK_DATA;

    The field name "MemorySpeed" in the definition above is *NOT* consistent with MemSubclass specification 0.9,
    in which it is defined as MemoryTypeSpeed. Keeping this inconsistency for backward compatibility.

  3. Guid/DataHubRecords.h
    #define EFI_MEMORY_CONTROLLER_INFORMATION_RECORD_NUMBER    0x00000008

    typedef enum {  
      EfiErrorDetectingMethodOther   = 1,
      EfiErrorDetectingMethodUnknown = 2,
      EfiErrorDetectingMethodNone    = 3,
      EfiErrorDetectingMethodParity  = 4,
      EfiErrorDetectingMethod32Ecc   = 5,
      EfiErrorDetectingMethod64Ecc   = 6,
      EfiErrorDetectingMethod128Ecc  = 7,
      EfiErrorDetectingMethodCrc     = 8
    } EFI_MEMORY_ERROR_DETECT_METHOD_TYPE;

    typedef struct {
      UINT8                             Other                 :1;
      UINT8                             Unknown               :1;
      UINT8                             None                  :1;
      UINT8                             SingleBitErrorCorrect :1;
      UINT8                             DoubleBitErrorCorrect :1;
      UINT8                             ErrorScrubbing        :1;
      UINT8                             Reserved              :2;
    } EFI_MEMORY_ERROR_CORRECT_CAPABILITY;

    typedef enum {  
      EfiMemoryInterleaveOther      = 1,
      EfiMemoryInterleaveUnknown    = 2,
      EfiMemoryInterleaveOneWay     = 3,
      EfiMemoryInterleaveTwoWay     = 4,
      EfiMemoryInterleaveFourWay    = 5,
      EfiMemoryInterleaveEightWay   = 6,
      EfiMemoryInterleaveSixteenWay = 7
    } EFI_MEMORY_SUPPORT_INTERLEAVE_TYPE;

    typedef struct {
      UINT16                            Other    :1;
      UINT16                            Unknown  :1;
      UINT16                            SeventyNs:1;
      UINT16                            SixtyNs  :1;
      UINT16                            FiftyNs  :1;
      UINT16                            Reserved :11;
    } EFI_MEMORY_SPEED_TYPE;

    typedef struct {
      UINT16                            Other       :1;
      UINT16                            Unknown     :1;
      UINT16                            Standard    :1;
      UINT16                            FastPageMode:1;
      UINT16                            EDO         :1;
      UINT16                            Parity      :1;
      UINT16                            ECC         :1;
      UINT16                            SIMM        :1;
      UINT16                            DIMM        :1;
      UINT16                            BurstEdo    :1;
      UINT16                            SDRAM       :1;
      UINT16                            Reserved    :5;
    } EFI_MEMORY_SUPPORTED_TYPE;

    typedef struct {
      UINT8                             Five    :1;
      UINT8                             Three   :1;
      UINT8                             Two     :1;
      UINT8                             Reserved:5;
    } EFI_MEMORY_MODULE_VOLTAGE_TYPE;

    typedef struct {
      EFI_MEMORY_ERROR_DETECT_METHOD_TYPE ErrorDetectingMethod;
      EFI_MEMORY_ERROR_CORRECT_CAPABILITY ErrorCorrectingCapability;
      EFI_MEMORY_SUPPORT_INTERLEAVE_TYPE  MemorySupportedInterleave;
      EFI_MEMORY_SUPPORT_INTERLEAVE_TYPE  MemoryCurrentInterleave;
      UINT8                               MaxMemoryModuleSize;
      EFI_MEMORY_SPEED_TYPE               MemorySpeedType;
      EFI_MEMORY_SUPPORTED_TYPE           MemorySupportedType;
      EFI_MEMORY_MODULE_VOLTAGE_TYPE      MemoryModuleVoltage;
      UINT8                               NumberofMemorySlot;
      EFI_MEMORY_ERROR_CORRECT_CAPABILITY EnabledCorrectingCapability;
      UINT16                              *MemoryModuleConfigHandles;
    } EFI_MEMORY_CONTROLLER_INFORMATION;

    typedef struct {
      EFI_MEMORY_ERROR_DETECT_METHOD_TYPE   ErrorDetectingMethod;
      EFI_MEMORY_ERROR_CORRECT_CAPABILITY   ErrorCorrectingCapability;
      EFI_MEMORY_SUPPORT_INTERLEAVE_TYPE    MemorySupportedInterleave;
      EFI_MEMORY_SUPPORT_INTERLEAVE_TYPE    MemoryCurrentInterleave;
      UINT8                                 MaxMemoryModuleSize;
      EFI_MEMORY_SPEED_TYPE                 MemorySpeedType;
      EFI_MEMORY_SUPPORTED_TYPE             MemorySupportedType;
      EFI_MEMORY_MODULE_VOLTAGE_TYPE        MemoryModuleVoltage;
      UINT8                                 NumberofMemorySlot;
      EFI_MEMORY_ERROR_CORRECT_CAPABILITY   EnabledCorrectingCapability;
      EFI_INTER_LINK_DATA                   MemoryModuleConfig[1];
    } EFI_MEMORY_CONTROLLER_INFORMATION_DATA;

    The definitions above are *NOT* defined in MemSubclass specification 0.9. They are introduced to support
    new memory controller information (type 5) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  4. Guid/DataHubRecords.h
    #define EFI_MEMORY_32BIT_ERROR_INFORMATION_RECORD_NUMBER    0x00000009

    typedef enum {  
      EfiMemoryErrorOther             = 1,
      EfiMemoryErrorUnknown           = 2,
      EfiMemoryErrorOk                = 3,
      EfiMemoryErrorBadRead           = 4,
      EfiMemoryErrorParity            = 5,
      EfiMemoryErrorSigleBit          = 6,
      EfiMemoryErrorDoubleBit         = 7,
      EfiMemoryErrorMultiBit          = 8,
      EfiMemoryErrorNibble            = 9,
      EfiMemoryErrorChecksum          = 10,
      EfiMemoryErrorCrc               = 11,
      EfiMemoryErrorCorrectSingleBit  = 12,
      EfiMemoryErrorCorrected         = 13,
      EfiMemoryErrorUnCorrectable     = 14
    } EFI_MEMORY_ERROR_TYPE;

    typedef enum {  
      EfiMemoryGranularityOther               = 1,
      EfiMemoryGranularityOtherUnknown        = 2,
      EfiMemoryGranularityDeviceLevel         = 3,
      EfiMemoryGranularityMemPartitionLevel   = 4
    } EFI_MEMORY_ERROR_GRANULARITY_TYPE;

    typedef enum {  
      EfiMemoryErrorOperationOther            = 1,
      EfiMemoryErrorOperationUnknown          = 2,
      EfiMemoryErrorOperationRead             = 3,
      EfiMemoryErrorOperationWrite            = 4,
      EfiMemoryErrorOperationPartialWrite     = 5
    } EFI_MEMORY_ERROR_OPERATION_TYPE;

    typedef struct {
      EFI_MEMORY_ERROR_TYPE               MemoryErrorType;
      EFI_MEMORY_ERROR_GRANULARITY_TYPE   MemoryErrorGranularity;
      EFI_MEMORY_ERROR_OPERATION_TYPE     MemoryErrorOperation;
      UINT32                              VendorSyndrome;
      UINT32                              MemoryArrayErrorAddress;
      UINT32                              DeviceErrorAddress;
      UINT32                              DeviceErrorResolution;
    } EFI_MEMORY_32BIT_ERROR_INFORMATION;

    The definitions above are *NOT* defined in MemSubclass specification 0.9. They are introduced to support
    new 32-bit memory error information (type 18) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  5. Guid/DataHubRecords.h
    #define EFI_MEMORY_64BIT_ERROR_INFORMATION_RECORD_NUMBER    0x0000000A
    
    typedef struct {
      EFI_MEMORY_ERROR_TYPE             MemoryErrorType;
      EFI_MEMORY_ERROR_GRANULARITY_TYPE MemoryErrorGranularity;
      EFI_MEMORY_ERROR_OPERATION_TYPE   MemoryErrorOperation;
      UINT32                            VendorSyndrome;
      UINT64                            MemoryArrayErrorAddress;
      UINT64                            DeviceErrorAddress;
      UINT32                            DeviceErrorResolution;
    } EFI_MEMORY_64BIT_ERROR_INFORMATION;

    The definitions above are *NOT* defined in MemSubclass specification 0.9. They are introduced to support
    new 64-bit memory error information (type 33) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  6. Guid/DataHubRecords.h
    typedef union _EFI_MEMORY_SUBCLASS_RECORDS {
      EFI_MEMORY_SIZE_DATA                 SizeData;
      ...
      EFI_MEMORY_64BIT_ERROR_INFORMATION   Memory64bitErrorInfo;
    } EFI_MEMORY_SUBCLASS_RECORDS;

    typedef struct {
      EFI_SUBCLASS_TYPE1_HEADER         Header;
      EFI_MEMORY_SUBCLASS_RECORDS       Record;
    } EFI_MEMORY_SUBCLASS_DRIVER_DATA;

    The definitions above are *NOT* defined in MemSubclass specification 0.9. EdkII introduces them to simplify the
    code logic. Therefore developer doesn't need to allocate memory dynamically to construct variable length data record.
    Keeping this inconsistency for backward compatibility.

##
# Mismatch with Intel Platform Innovation Framework for MiscSubclass Specification (Version 0.90)
##
  1. Guid/DataHubRecords.h
    #pragma pack(1)
    typedef struct _USB_PORT_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } USB_PORT_DEVICE_PATH;
    
    typedef struct _IDE_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } IDE_DEVICE_PATH;
    
    typedef struct _RMC_CONN_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } RMC_CONN_DEVICE_PATH;
    
    typedef struct _RIDE_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } RIDE_DEVICE_PATH;
    
    typedef struct _GB_NIC_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   PciBridgeDevicePath;
      PCI_DEVICE_PATH                   PciXBridgeDevicePath;
      PCI_DEVICE_PATH                   PciXBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } GB_NIC_DEVICE_PATH;
    
    typedef struct _PS2_CONN_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   LpcBridgeDevicePath;
      ACPI_HID_DEVICE_PATH              LpcBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } PS2_CONN_DEVICE_PATH;
    
    typedef struct _SERIAL_CONN_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   LpcBridgeDevicePath;
      ACPI_HID_DEVICE_PATH              LpcBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } SERIAL_CONN_DEVICE_PATH;
    
    typedef struct _PARALLEL_CONN_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   LpcBridgeDevicePath;
      ACPI_HID_DEVICE_PATH              LpcBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } PARALLEL_CONN_DEVICE_PATH;
    
    typedef struct _FLOOPY_CONN_DEVICE_PATH {
      ACPI_HID_DEVICE_PATH              PciRootBridgeDevicePath;
      PCI_DEVICE_PATH                   LpcBridgeDevicePath;
      ACPI_HID_DEVICE_PATH              LpcBusDevicePath;
      EFI_DEVICE_PATH_PROTOCOL          EndDevicePath;
    } FLOOPY_CONN_DEVICE_PATH;
    
    typedef union _EFI_MISC_PORT_DEVICE_PATH {
      USB_PORT_DEVICE_PATH              UsbDevicePath;
      IDE_DEVICE_PATH                   IdeDevicePath;
      RMC_CONN_DEVICE_PATH              RmcConnDevicePath;
      RIDE_DEVICE_PATH                  RideDevicePath;
      GB_NIC_DEVICE_PATH                GbNicDevicePath;
      PS2_CONN_DEVICE_PATH              Ps2ConnDevicePath;
      SERIAL_CONN_DEVICE_PATH           SerialConnDevicePath;
      PARALLEL_CONN_DEVICE_PATH         ParallelConnDevicePath;
      FLOOPY_CONN_DEVICE_PATH           FloppyConnDevicePath;
    } EFI_MISC_PORT_DEVICE_PATH;
    #pragma pack()

    a. The definitions above are *NOT* defined in MiscSubclass specifications 0.9. EdkII introduces them to simplify the
       code logic. Therefore developer doesn't need to allocate memory dynamically to construct variable length device
       path for various device.
       Keeping this inconsistency for backward compatibility.

    b. The definitions above are packed. This way violates the rule of alignment defined in DataHubSubclass specification.
       Section "Alignment" in DataHubSubclass specification say "Fields in a data hub record should be aligned at their
       natural boundaries". Keeping this inconsistency for backward compatibility.

  2. Guid/DataHubRecords.h
    typedef struct {
      ...
      EFI_MISC_PORT_DEVICE_PATH         PortPath;
    } EFI_MISC_PORT_INTERNAL_CONNECTOR_DESIGNATOR_DATA;

    The definition is *NOT* consistent with MiscSubclass specification, in which the type of last field is defined as
    "EFI_DEVICE_PATH_PROTOCOL". The definition in Specification may bring a little complexity on implementation. User
    have to allocate variable length memory to contain device path info and free them finially.
    EdkII introduced an union type named EFI_MISC_PORT_DEVICE_PATH to avoid the logic above.

  3. Guid/DataHubRecords.h
    typedef struct {
      ...
      UINT8                                       BiosMajorRelease;
      UINT8                                       BiosMinorRelease;
      UINT8                                       BiosEmbeddedFirmwareMajorRelease;
      UINT8                                       BiosEmbeddedFirmwareMinorRelease;
    } EFI_MISC_BIOS_VENDOR_DATA;

    The fields listed above are *NOT* defined in MiscSubclass specification 0.9. They are introduced to support
    new bios information (type 0) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  4. Guid/DataHubRecords.h
    typedef struct {
      ...
      STRING_REF                        SystemSKUNumber;
      STRING_REF                        SystemFamily;
    } EFI_MISC_SYSTEM_MANUFACTURER_DATA;

    The fields listed above are *NOT* defined in MiscSubclass specification 0.9. They are introduced to support
    new system information (type 1) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  5. Guid/DataHubRecords.h
    typedef struct {
      ...
      EFI_INTER_LINK_DATA               ManagementDeviceThresholdLink;
      UINT8                             ComponentType;
    } EFI_MISC_MANAGEMENT_DEVICE_COMPONENT_DESCRIPTION_DATA;

    a. The field "ManagementDeviceThresholdLink" above is *NOT* defined in MiscSubclass specification 0.9. It is introduced to support
       new management device component (type 35) defined in SmBios 2.6 specification. 
       Keeping this inconsistency to reflect the latest industry standard.
    b. The field "ComponentType" above is *NOT* defined in MiscSubclass specifications 0.9. It's implementation-specific to simplify the code logic.
       Keeping this inconsistency for backward compatibility.

  6. Guid/DataHubRecords.h
    typedef struct {
      UINT32                            ChassisType       :16;
      UINT32                            ChassisLockPresent:1;
      UINT32                            Reserved          :15;
    } EFI_MISC_CHASSIS_STATUS;

    The definition is *NOT* consistent with MiscSubclass specification 0.9, in which the first field is assigned a wrong field
    name "EFI_MISC_CHASSIS_TYPE". Due to EFI_MISC_CHASSIS_TYPE has been declared as a data type, it can not be used as a
    field name again. EdkII changes its name to "ChassisType" to pass build.

  7. Guid/DataHubRecords.h
    typedef enum {
      ...
      EfiSlotTypeAgp2X                  = 0x10,
      ...
      EfiSlotTypePciExpress             = 0xA5,
      EfiSlotTypePciExpressX1           = 0xA6,
      EfiSlotTypePciExpressX2           = 0xA7,
      EfiSlotTypePciExpressX4           = 0xA8,
      EfiSlotTypePciExpressX8           = 0xA9,
      EfiSlotTypePciExpressX16          = 0xAA
    } EFI_MISC_SLOT_TYPE;

    a. The field name "EfiSlotTypeAgp2X" is *NOT* consistent with MiscSubclass specification 0.9, in which it is named
       "EfiSlotTypeApg2X".
       From its literal sense, this field represents a AGP type display card, so it should be named as "EfiSlotTypeAgp2X".
    b. The enumeration fields from "EfiSlotTypePciExpress" to "EfiSlotTypePciExpressX16" are *NOT* defined in MiscSubclass specification 0.9.
       They are introduced to support new system slots (type 9) defined in SmBios 2.6 specification.
       Keeping this inconsistency to reflect the latest industry standard.

  8. Guid/DataHubRecords.h
    typedef struct {
      ...
      EFI_MISC_ONBOARD_DEVICE_STATUS    OnBoardDeviceStatus;
      ...
    } EFI_MISC_ONBOARD_DEVICE_DATA;

    The definition is *NOT* consistent with MiscSubclass specification 0.9, in which the field "OnBoardDeviceStatus" is 
    named as "OnBoardDeviceType". Keeping this inconsistency for backward compatibility.

  9. Guid/DataHubRecords.h
    #define EFI_MISC_PORTABLE_BATTERY_RECORD_NUMBER   0x00000010

    The name of the definition is *NOT* consistent with MiscSubclass specification 0.9, in which it is defined as
    "EFI_MISC_BATTERY_LOCATION_RECORD_NUMBER". Keeping this inconsistency for backward compatibility.

  10. Guid/DataHubRecords.h
    typedef enum {  
      EfiPortableBatteryDeviceChemistryOther = 1,
      EfiPortableBatteryDeviceChemistryUnknown = 2,
      EfiPortableBatteryDeviceChemistryLeadAcid = 3,
      EfiPortableBatteryDeviceChemistryNickelCadmium = 4,
      EfiPortableBatteryDeviceChemistryNickelMetalHydride = 5,
      EfiPortableBatteryDeviceChemistryLithiumIon = 6,
      EfiPortableBatteryDeviceChemistryZincAir = 7,
      EfiPortableBatteryDeviceChemistryLithiumPolymer = 8
    } EFI_MISC_PORTABLE_BATTERY_DEVICE_CHEMISTRY;

    The name of the definition is *NOT* consistent with MiscSubclass specification, in which it is defined as
    "EFI_MISC_BATTERY_DEVICE_CHEMISTRY". And all field names have a redundant "Portable" string compared with MisSubclass 
    specification 0.9.
    Keeping this inconsistency for backward compatibility.

  11. Guid/DataHubRecords.h
    typedef struct {
      STRING_REF                                 Location;
      STRING_REF                                 Manufacturer;
      STRING_REF                                 ManufactureDate;
      STRING_REF                                 SerialNumber;
      STRING_REF                                 DeviceName;
      EFI_MISC_PORTABLE_BATTERY_DEVICE_CHEMISTRY DeviceChemistry;
      UINT16                                     DesignCapacity;
      UINT16                                     DesignVoltage;
      STRING_REF                                 SBDSVersionNumber;
      UINT8                                      MaximumError;
      UINT16                                     SBDSSerialNumber;
      UINT16                                     SBDSManufactureDate;
      STRING_REF                                 SBDSDeviceChemistry;
      UINT8                                      DesignCapacityMultiplier;
      UINT32                                     OEMSpecific;
      UINT8                                      BatteryNumber;
      BOOLEAN                                    Valid;
    } EFI_MISC_PORTABLE_BATTERY;

    The definition is *NOT* consistent with MiscSubclass specification 0.9, in which the structure name is defined as
    "EFI_MISC_BATTERY_LOCATION_DATA". Moreover, the name and the order of all fields are also different with MiscSubclass 
    specification 0.9. Keeping this inconsistency for backward compatibility.

  12. Guid/DataHubRecords.h
    typedef enum {
      ...
    } EFI_MISC_BOOT_INFORMATION_STATUS_DATA_TYPE;

    The name of the definition is *NOT* consistent with MiscSubclass specification 0.9, in which it is defined as
    "EFI_MISC_BOOT_INFORMATION_STATUS_TYPE". Keeping this inconsistency for backward compatibility.

  13. Guid/DataHubRecords.h
    typedef struct {
      EFI_MISC_BOOT_INFORMATION_STATUS_DATA_TYPE BootInformationStatus;
      ...
    } EFI_MISC_BOOT_INFORMATION_STATUS_DATA;

    The definition is *NOT* consistent with MiscSubclass specification 0.9, in which the type of the first field is 
    "EFI_MISC_BOOT_INFORMATION_STATUS_TYPE". Keeping this inconsistency for backward compatibility.

  14. Guid/DataHubRecords.h
    typedef struct {
      ...
    } EFI_MISC_SYSTEM_POWER_SUPPLY_DATA;

    The name of the definition is *NOT* consistent with MiscSubclass specification 0.9, in which it is defined as
    "EFI_MISC_POWER_SUPPLY_UNIT_GROUP_DATA". Keeping this inconsistency for backward compatibility.

  15. Guid/DataHubRecords.h
    typedef struct {
      ...
    } SMBIOS_STRUCTURE_HDR;

    The name of the definition is *NOT* consistent with MiscSubclass specification 0.9, in which the structure name
    is defined as "EFI_SMBIOS_STRUCTURE_HDR". Due to this structure is commonly used by vendor to construct SmBios
    type 0x80~0xFF table, Keeping this inconsistency for backward compatibility.

  16. Guid/DataHubRecords.h
    typedef struct {
      SMBIOS_STRUCTURE_HDR              Header;
      ...
    } EFI_MISC_SMBIOS_STRUCT_ENCAPSULATION_DATA;

    The definition is *NOT* consistent with MiscSubclass specification 0.9, in which the type of the first field is 
    "EFI_SMBIOS_STRUCTURE_HDR". Keeping this inconsistency for backward compatibility.

  17. Guid/DataHubRecords.h
    typedef struct {
      UINT16                            PowerSupplyHotReplaceable:1;
      UINT16                            PowerSupplyPresent       :1;
      UINT16                            PowerSupplyUnplugged     :1;
      UINT16                            InputVoltageRangeSwitch  :4;
      UINT16                            PowerSupplyStatus        :3;
      UINT16                            PowerSupplyType          :4;
      UINT16                            Reserved                 :2;
    } EFI_MISC_POWER_SUPPLY_CHARACTERISTICS;

    all field type in the definition are *NOT* consistent with MiscSubclass specification 0.9, in which it is defined as
    "UINT32" and the total width of bit-fields is 32bits width.
    Keeping this inconsistency for backward compatibility.

  18. Guid/DataHubRecords.h
    #define EFI_MISC_SYSTEM_EVENT_LOG_RECORD_NUMBER    0x00000020

    typedef struct {
      UINT16                            LogAreaLength;
      UINT16                            LogHeaderStartOffset;
      UINT16                            LogDataStartOffset;
      UINT8                             AccessMethod;
      UINT8                             LogStatus;
      UINT32                            LogChangeToken;
      UINT32                            AccessMethodAddress;
      UINT8                             LogHeaderFormat;
      UINT8                             NumberOfSupportedLogType;
      UINT8                             LengthOfLogDescriptor;
    } EFI_MISC_SYSTEM_EVENT_LOG_DATA;

    #define ACCESS_INDEXIO_1INDEX8BIT_DATA8BIT    0x00
    #define ACCESS_INDEXIO_2INDEX8BIT_DATA8BIT    0X01
    #define ACCESS_INDEXIO_1INDEX16BIT_DATA8BIT   0X02
    #define ACCESS_MEMORY_MAPPED                  0x03
    #define ACCESS_GPNV                           0x04

    The definitions listed above are *NOT* defined in MiscSubclass specification 0.9. It is introduced to support
    new system event log (type 15) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  19. Guid/DataHubRecords.h
    #define EFI_MISC_MANAGEMENT_DEVICE_THRESHOLD_RECORD_NUMBER    0x00000021

    typedef struct {
      UINT16                            LowerThresNonCritical;
      UINT16                            UpperThresNonCritical;
      UINT16                            LowerThresCritical;
      UINT16                            UpperThresCritical;
      UINT16                            LowerThresNonRecover;
      UINT16                            UpperThresNonRecover;
    } EFI_MISC_MANAGEMENT_DEVICE_THRESHOLD;

    The definitions listed above are *NOT* defined in MiscSubclass specification 0.9. It is introduced to support
    new management device threshold data (type 36) defined in SmBios 2.6 specification. 
    Keeping this inconsistency to reflect the latest industry standard.

  20. Guid/DataHubRecords.h
    typedef union {
      EFI_MISC_LAST_PCI_BUS_DATA                         LastPciBus;
      ...
      EFI_MISC_MANAGEMENT_DEVICE_THRESHOLD               MiscManagementDeviceThreshold;
    } EFI_MISC_SUBCLASS_RECORDS;

    typedef struct {
      EFI_SUBCLASS_TYPE1_HEADER         Header;
      EFI_MISC_SUBCLASS_RECORDS         Record;
    } EFI_MISC_SUBCLASS_DRIVER_DATA;

    The definitions above are *NOT* defined in MemSubclass specification 0.9. EdkII introduces them to simplify the
    code logic. Therefore developer doesn't need to allocate memory dynamically to construct variable length data record.
    Keeping this inconsistency for backward compatibility.

  21. Guid/DataHubRecords.h
    typedef struct {
      EFI_MISC_COOLING_DEVICE_TYPE      CoolingDeviceType;
      EFI_INTER_LINK_DATA               CoolingDeviceTemperatureLink;
      UINT8                             CoolingDeviceUnitGroup;
      UINT16                            CoolingDeviceNominalSpeed;
      UINT32                            CoolingDeviceOemDefined;
    } EFI_MISC_COOLING_DEVICE_TEMP_LINK_DATA;

    The "CoolingDeviceUnitGroup" field and "CoolingDeviceNominalSpeed" field are *NOT* consistent with 
    MiscSubclass specification 0.9. These fields are aligned with SMBIOS 2.6 specification. And user can easily
    assign any value to CoolingDeviceNominalSpeed.

  22. Guid/DataHubRecords.h
    typedef enum {
      ...
      EfiSlotDataBusWidth1xOrx1         = 0x8,
      EfiSlotDataBusWidth2xOrx2         = 0x9,
      EfiSlotDataBusWidth4xOrx4         = 0xA,
      EfiSlotDataBusWidth8xOrx8         = 0xB,
      EfiSlotDataBusWidth12xOrx12       = 0xC,
      EfiSlotDataBusWidth16xOrx16       = 0xD,
      EfiSlotDataBusWidth32xOrx32       = 0xE
    } EFI_MISC_SLOT_DATA_BUS_WIDTH;

    The enumeration fields from "EfiSlotDataBusWidth1xOrx1" to "EfiSlotDataBusWidth32xOrx32" are *NOT* defined in MiscSubclass specification 0.9.
    They are introduced to support new system slots (type 9) defined in SmBios 2.6 specification.
    Keeping this inconsistency to reflect the latest industry standard.

  23. Guid/DataHubRecords.h
    typedef struct {
      ...
      UINT16                            TemperatureProbeMaximumValue;
      UINT16                            TemperatureProbeMinimumValue;
      UINT16                            TemperatureProbeResolution;
      UINT16                            TemperatureProbeTolerance;
      UINT16                            TemperatureProbeAccuracy;
      UINT16                            TemperatureProbeNominalValue;
      UINT16                            MDLowerNoncriticalThreshold;
      UINT16                            MDUpperNoncriticalThreshold;
      UINT16                            MDLowerCriticalThreshold;
      UINT16                            MDUpperCriticalThreshold;
      UINT16                            MDLowerNonrecoverableThreshold;
      UINT16                            MDUpperNonrecoverableThreshold;
      ...
    } EFI_MISC_TEMPERATURE_PROBE_DESCRIPTION_DATA;

    The structure fields from "TemperatureProbeMaximumValue" to "MDUpperNonrecoverableThreshold" are *NOT* consistent with MiscSubclass specification 0.9.
    The specification defines the fields type as EFI_EXP_BASE10_DATA. In fact, they should be UINT16 type because they refer to 16bit width data.
    Keeping this inconsistency for backward compatibility.

  24. Guid/DataHubRecords.h
    #define EFI_MISC_IPMI_INTERFACE_TYPE_DATA_RECORD_NUMBER EFI_MISC_IPMI_INTERFACE_TYPE_RECORD_NUMBER

    The definition above is *NOT* defined in MiscSubclass specifications 0.9. It's defined for backward compatibility.

##
# Mismatch with Intel Platform Innovation Framework for Status Codes Specification (Version 0.92)
##
  1. Include/Framework/StatusCode.h
    #define EFI_IOB_ATA_BUS_SMART_ENABLE               (EFI_SUBCLASS_SPECIFIC | 0x00000000)
    #define EFI_IOB_ATA_BUS_SMART_DISABLE              (EFI_SUBCLASS_SPECIFIC | 0x00000001)
    #define EFI_IOB_ATA_BUS_SMART_OVERTHRESHOLD        (EFI_SUBCLASS_SPECIFIC | 0x00000002)
    #define EFI_IOB_ATA_BUS_SMART_UNDERTHRESHOLD       (EFI_SUBCLASS_SPECIFIC | 0x00000003)

    #define EFI_IOB_ATA_BUS_SMART_NOTSUPPORTED         (EFI_SUBCLASS_SPECIFIC | 0x00000000)
    #define EFI_IOB_ATA_BUS_SMART_DISABLED             (EFI_SUBCLASS_SPECIFIC | 0x00000001)

    #define EFI_SW_DXE_BS_PC_BEGIN_CONNECTING_DRIVERS  (EFI_SUBCLASS_SPECIFIC | 0x00000005)
    #define EFI_SW_DXE_BS_PC_VERIFYING_PASSWORD        (EFI_SUBCLASS_SPECIFIC | 0x00000006)

    #define EFI_SW_DXE_RT_PC_S0                        (EFI_SUBCLASS_SPECIFIC | 0x00000000)
    #define EFI_SW_DXE_RT_PC_S1                        (EFI_SUBCLASS_SPECIFIC | 0x00000001)
    #define EFI_SW_DXE_RT_PC_S2                        (EFI_SUBCLASS_SPECIFIC | 0x00000002)
    #define EFI_SW_DXE_RT_PC_S3                        (EFI_SUBCLASS_SPECIFIC | 0x00000003)
    #define EFI_SW_DXE_RT_PC_S4                        (EFI_SUBCLASS_SPECIFIC | 0x00000004)
    #define EFI_SW_DXE_RT_PC_S5                        (EFI_SUBCLASS_SPECIFIC | 0x00000005)

    #define EFI_SW_CSM_LEGACY_ROM_INIT                 (EFI_SUBCLASS_SPECIFIC | 0x00000000)

    The definitions above are *NOT* defined in Framework StatusCodes specification 0.92. But these subclass-specific error code
    operations are needed for EdkII implementation.
    Keeping this inconsistency for backward compatibility.

  2. Include/Framework/StatusCode.h
    typedef union {
      CHAR8   *Ascii;
      CHAR16  *Unicode;
      ...
    } EFI_STATUS_CODE_STRING;

    The definition is *NOT* consistent with Framework SatausCodes specification 0.92, in which the first field is defined as "CHAR8 Ascii[]"
    and the second field is defined as "CHAR16 Unicode[]". Keeping this inconsistency for backward compatibility.

  3. Include/Framework/StatusCode.h
    #define EFI_SW_EC_X64_DIVIDE_ERROR      EXCEPT_X64_DIVIDE_ERROR
    #define EFI_SW_EC_X64_DEBUG             EXCEPT_X64_DEBUG
    #define EFI_SW_EC_X64_NMI               EXCEPT_X64_NMI
    #define EFI_SW_EC_X64_BREAKPOINT        EXCEPT_X64_BREAKPOINT
    #define EFI_SW_EC_X64_OVERFLOW          EXCEPT_X64_OVERFLOW
    #define EFI_SW_EC_X64_BOUND             EXCEPT_X64_BOUND
    #define EFI_SW_EC_X64_INVALID_OPCODE    EXCEPT_X64_INVALID_OPCODE
    #define EFI_SW_EC_X64_DOUBLE_FAULT      EXCEPT_X64_DOUBLE_FAULT
    #define EFI_SW_EC_X64_INVALID_TSS       EXCEPT_X64_INVALID_TSS
    #define EFI_SW_EC_X64_SEG_NOT_PRESENT   EXCEPT_X64_SEG_NOT_PRESENT
    #define EFI_SW_EC_X64_STACK_FAULT       EXCEPT_X64_STACK_FAULT
    #define EFI_SW_EC_X64_GP_FAULT          EXCEPT_X64_GP_FAULT
    #define EFI_SW_EC_X64_PAGE_FAULT        EXCEPT_X64_PAGE_FAULT
    #define EFI_SW_EC_X64_FP_ERROR          EXCEPT_X64_FP_ERROR
    #define EFI_SW_EC_X64_ALIGNMENT_CHECK   EXCEPT_X64_ALIGNMENT_CHECK
    #define EFI_SW_EC_X64_MACHINE_CHECK     EXCEPT_X64_MACHINE_CHECK
    #define EFI_SW_EC_X64_SIMD              EXCEPT_X64_SIMD

    The definitions are *NOT* defined in Framework StatusCodes specification 0.92, in which IA32 and IPF exception subclass error code definitions
    are defined but omit the corresponding definitions for X64. EdkII introduce these definitions for implementation. 

##
# Mismatch with Intel Platform Innovation Framework for EFI Boot Script Specification (Version 0.91)
##
  1. Include/Protocol/BootScriptSave.h
    #define EFI_BOOT_SCRIPT_SAVE_PROTOCOL_GUID \
    { \
      0x470e1529, 0xb79e, 0x4e32, {0xa0, 0xfe, 0x6a, 0x15, 0x6d, 0x29, 0xf9, 0xb2 } \
    }

    The macro name "EFI_BOOT_SCRIPT_SAVE_PROTOCOL_GUID" is *NOT* consistent with Framework BootScript specification 0.91,
    in which it's defined as "EFI_BOOT_SCRIPT_SAVE_GUID". Keeping this inconsistency for backward compatibility.

  2. Include/Protocol/BootScriptSave.h
    EFI_STATUS
    EFI_BOOTSERVICE
    (EFIAPI *EFI_BOOT_SCRIPT_WRITE) (
      IN EFI_BOOT_SCRIPT_SAVE_PROTOCOL            *This,
      ...
    );

    The first parameter's type is *NOT* consistent with Framework BootScript specification 0.91, in which it's defined as
    "struct _EFI_BOOT_SCRIPT_SAVE_PROTOCOL". Keeping this inconsistency for backward compatibility.

  3. Include/Framework/BootScript.h
    #define EFI_BOOT_SCRIPT_MEM_POLL_OPCODE               0x09
    #define EFI_BOOT_SCRIPT_INFORMATION_OPCODE            0x0A
    #define EFI_BOOT_SCRIPT_PCI_CONFIG2_WRITE_OPCODE      0x0B
    #define EFI_BOOT_SCRIPT_PCI_CONFIG2_READ_WRITE_OPCODE 0x0C
    #define EFI_BOOT_SCRIPT_DISPATCH_2_OPCODE             0x0D

    The OPCODEs above are not defined in Framework BootScript Specification 0.91, but adopted by PI 1.0 Spec. And they
    are needed for EdkII implementation.

  4. Include/Framework/BootScript.h
    #define EFI_BOOT_SCRIPT_TABLE_OPCODE                  0xAA
    #define EFI_BOOT_SCRIPT_TERMINATE_OPCODE              0xFF

    The two OPCODEs are *NOT* defined in Framework BootScript specification 0.91. EdkII introduces them to indicate the start
    or end of the boot script table.
    Keeping this inconsistency for backward compatibility.

  5. Include/Protocol/BootScriptSave.h
    typedef  
    EFI_STATUS  
    (EFIAPI *EFI_BOOT_SCRIPT_CLOSE_TABLE) (
      IN  EFI_BOOT_SCRIPT_SAVE_PROTOCOL            *This,
      ...
      );

    The first parameter's type is *NOT* consistent with BootScript specification, in which it's defined as
    "struct _EFI_BOOT_SCRIPT_SAVE_PROTOCOL". Keeping this inconsistency for backward compatibility.

  6. Include/Include/BootScriptExecuter.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_PEI_BOOT_SCRIPT_EXECUTE)(
      IN     EFI_PEI_SERVICES                        **PeiServices,
      IN     EFI_PEI_BOOT_SCRIPT_EXECUTER_PPI        *This,
      ...
      );

    The second parameter's type is *NOT* consistent with BootScript specification, in which it's defined as
    "struct _EFI_PEI_BOOT_SCRIPT_EXECUTER_PPI". Keeping this inconsistency for backward compatibility.

##
# Mismatch with Intel Platform Innovation Framework for EFI DXE CIS (Version 0.91)
##
  1. Include/Framework/DxeCis.h
    EFI_STATUS_CODE_ARCH_PROTOCOL is removed.
    
    EdkII doesn't provide EFI_STATUS_CODE_ARCH_PROTOCOL definition due to ReportStatusCode() field has been
    removed from EFI Runtime Service Table of PI specification. EFI_STATUS_CODE_ARCH_PROTOCOL is *NOT* required,
    and is replaced with EFI_STATUS_CODE_RUNTIME_PROTOCOL.

##
# Mismatch with Intel Platform Innovation Framework for EFI Firmware Volume Specification (Version 0.9)
##
  1. Include/Framework/FirmwareVolumeImageFormat.h
    #define EFI_AGGREGATE_AUTH_STATUS_ALL               0x00000f
    #define EFI_LOCAL_AUTH_STATUS_ALL                   0x0f0000

    The two macros are *NOT* defined in Framework FV specification 0.9. EdkII introduces them as a mask to calculate the
    value of authentication status.

##
# Mismatch with Intel Platform Innovation Framework for EFI Human Interface Infrastructure Specification (Version 0.92)
##
  1. Include/Protocol/FrameworkHii.h
    #define EFI_HII_PROTOCOL_GUID \
      { \
        0xd7ad636e, 0xb997, 0x459b, {0xbf, 0x3f, 0x88, 0x46, 0x89, 0x79, 0x80, 0xe1} \
      }

    The Framework HII specification 0.92 changed part of HII interfaces but did not update the protocol GUID.
    This change should cause a change of GUID in both of code and HII spec. EdkII updates the GUID in code, 
    but the Framework HII specification 0.92 is not updated. This is a known issue.

  2. Include/Protocol/FrameworkHii.h
    typedef struct {
      ...
      EFI_HANDLE          COBExportHandle;
    } EFI_HII_HANDLE_PACK;

    The last field "COBExportHandle" of EFI_HII_HANDLE_PACK is *NOT* defined in the Framework HII specification
    0.92. Keeping this inconsistency for backward compatibility.

  3. Include/Protocol/FrameworkHii.h
    typedef struct {
      UINTN     NumberOfPackages;
      EFI_GUID  *GuidId;
    } EFI_HII_PACKAGES;

    The definition is *NOT* consistent with Framework HII specification 0.92, in which a field "HandlePack" is defined.
    EdkII changes the EFI_HII_PACKAGES to contain various number of packages of different types just after the structure
    as inline data, which will bring the flexibility on development.

  4. Include/Protocol/FrameworkHii.h
    struct _EFI_HII_PROTOCOL {
      ...
      EFI_HII_RESET_STRINGS                 ResetStrings;
      ...
    };

    The field listed above is *NOT* defined in Framework HII specification 0.92. EdkII adds this field to provide 
    an ability of removing any new strings that were added after the initial string export for this handle.  

  5. Include/Protocol/FrameworkHii.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_HII_GLYPH_TO_BLT)(
      ...
      IN     EFI_GRAPHICS_OUTPUT_BLT_PIXEL Foreground,
      IN     EFI_GRAPHICS_OUTPUT_BLT_PIXEL Background,
      ...
      IN OUT EFI_GRAPHICS_OUTPUT_BLT_PIXEL *BltBuffer
      );

    The type of the parameters listed above are *NOT* consistent with Framework HII specification 0.92, in which
    the type of these parameters is EFI_UGA_PIXEL. Here the definition uses the EFI_GRAPHICS_OUTPUT_BLT_PIXEL which
    defined in UEFI2.1 spec. Keeping this inconsistency for backward compatibility.

  6. Include/Protocol/FrameworkHii.h
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      UINT8                             Flags;
    } EFI_IFR_SUPPRESS;
    
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      UINT8                             Flags;
    } EFI_IFR_GRAY_OUT;
    
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      STRING_REF                        Popup;
      UINT8                             Flags;
    } EFI_IFR_INCONSISTENT;
    
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      UINT16                            QuestionId;
      UINT8                             Width;
      UINT16                            Value;
    } FRAMEWORK_EFI_IFR_EQ_ID_VAL;
    
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      UINT16                            QuestionId;
      UINT8                             Width;
      UINT16                            ListLength;
      UINT16                            ValueList[1];
    } FRAMEWORK_EFI_IFR_EQ_ID_LIST;
    
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      UINT16                            QuestionId1;
      UINT8                             Width;
      UINT16                            QuestionId2;
    } FRAMEWORK_EFI_IFR_EQ_ID_ID;
    
    typedef struct {
      FRAMEWORK_EFI_IFR_OP_HEADER       Header;
      UINT16                            VariableId;
      UINT16                            Value;
    } EFI_IFR_EQ_VAR_VAL;

    The defintions are not complied with Framework HII spec 0.92. Keeping the inconsistent for implementation needed.

  7. Include/Protocol/FrameworkFormCallback.h
    #define RESET_REQUIRED  1 
    #define EXIT_REQUIRED   2
    #define SAVE_REQUIRED   4
    #define NV_CHANGED      8
    #define NV_NOT_CHANGED  16

    These macros are *NOT* defined in the Framework HII specification 0.92. These Flags are introduced to describe
    the standard behavior of the browser after the callback.
    Keeping this inconsistency for backward compatibility.

  8. Include/Protocol/FrameworkFormCallback.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_NV_WRITE)(
      ...
      IN     UINT32                        Attributes,
      ...
      );

    The definition is *NOT* consistent with Framework HII specification 0.92, in which the type of Attributes
    parameter is defined as "UINT32 *". EdkII changes the type of Attributes from UINT32 * to UINT32 because
    the input paramter is not necessary to use pointer date type.

##
# Mismatch with Intel Platform Innovation Framework for PEI CIS Specification (Version 0.91)
##
  1. Include/Ppi/ReadOnlyVariable.h
    #define EFI_VARIABLE_READ_ONLY          0x00000008

    In Framework PeiCis specification 0.91, neither the macro or its value is defined.
    Keeping this inconsistency for backward compatibility.

  2. Include/Ppi/FindFv.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_PEI_FIND_FV_FINDFV)(
      IN EFI_PEI_FIND_FV_PPI             *This,
      IN EFI_PEI_SERVICES                **PeiServices,
      IN UINT8                           *FvNumber,
      IN OUT EFI_FIRMWARE_VOLUME_HEADER  **FVAddress
      );

    The definition is *NOT* consistent with Framework PeiCis specification 0.91. Compared with spec, the order
    of the first and second parameters is reversed. Keeping this inconsistency for backward compatibility.

##
# Mismatch with Intel Platform Innovation Framework for EFI SMM CIS (Version 0.91)
##
  1. Include/Guid/SmramMemoryReserve.h
    typedef struct {
      UINT32                NumberOfSmmReservedRegions;
      ...
    } EFI_SMRAM_HOB_DESCRIPTOR_BLOCK;

    1) The name of the definition is *NOT* consistent with Framework SmmCis specification 0.91, in which it's 
    defined as "EFI_HOB_SMRAM_DESCRIPTOR_BLOCK" rather than "EFI_SMRAM_HOB_DESCRIPTOR_BLOCK". 
    Keeping this inconsistency for backward compatibility.

    2) The definition of NumberOfSmmReservedRegions is *NOT* consistent with Framework SmmCis specification 0.91,
    in which the type of this field is defined as UINTN. However, HOBs are supposed to be CPU neutral, so UINTN
    is incorrect and UINT32 should be used.

  2. Include/Guid/SmramMemoryReserve.h
    typedef enum {
      ...
      IchnIoTrap3,
      IchnIoTrap2,
      IchnIoTrap1,
      IchnIoTrap0,
      IchnPciExpress,
      IchnMonitor,
      IchnSpi,
      IchnQRT,
      IchnGpioUnlock,
      ...
    } EFI_SMM_ICHN_SMI_TYPE;

    The enumeration fields listed above are *NOT* defined in Framework SmmCis specification 0.91. EdkII introduces
    these fields to support new SMI types.

  3. Include/Framework/SmmCis.h
    typedef union {
      ///
      /// The processor save-state information for IA-32 processors. 
      ///
      EFI_SMI_CPU_SAVE_STATE     Ia32SaveState;
      ///
      /// Note: Inconsistency with the Framework SMM CIS spec - Itanium save state not included.
      ///
      /// The processor save-state information for Itanium processors.
      ///
      /// EFI_PMI_SYSTEM_CONTEXT ItaniumSaveState;
    } EFI_SMM_CPU_SAVE_STATE;

##
# Mismatch with Intel Platform Innovation Framework for EFI S3 Resume Boot Path Specification (Version 0.9)
##
  1. Include/Protocol/AcpiS3Save.h
    typedef
    EFI_STATUS
    EFI_BOOTSERVICE
    (EFIAPI *EFI_ACPI_GET_LEGACY_MEMORY_SIZE) (
      IN  EFI_ACPI_S3_SAVE_PROTOCOL *This,
      OUT UINTN *Size
      );

    The first parameter's type is *NOT* consistent with Framework S3Resume specification, in which it's defined as
    "struct _EFI_ACPI_S3_SAVE_PROTOCOL". Keeping this inconsistency for backward compatibility.

  2. Include/Protocol/AcpiS3Save.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_ACPI_S3_SAVE) (
      IN EFI_ACPI_S3_SAVE_PROTOCOL      *This,
      IN VOID                           *LegacyMemoryAddress 
      );

    The first parameter's type is *NOT* consistent with Framework S3Resume specification, in which it's defined as
    "struct _EFI_ACPI_S3_SAVE_PROTOCOL". Also the EFI_BOOTSERVICE modifier is removed from the function declaration.

  3. Include/Protocol/AcpiS3Save.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_ACPI_GET_LEGACY_MEMORY_SIZE)(
      IN  EFI_ACPI_S3_SAVE_PROTOCOL     *This,
      OUT UINTN                         *Size
    );

    The first parameter's type is *NOT* consistent with Framework S3Resume specification, in which it's defined as
    "struct _EFI_ACPI_S3_SAVE_PROTOCOL". Also the EFI_BOOTSERVICE modifier is removed from the function declaration.

##
# Mismatch with Intel Platform Innovation Framework for EFI ACPI Specification (Version 0.91)
##
  1. Include/Protocol/AcpiSupport.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_ACPI_GET_ACPI_TABLE)(
      ...
      );

    The function modifier is *NOT* consistent with Framework Acpi specification. The EFI_BOOTSERVICE modifier
    is removed from the function declaration.

  2. Include/Protocol/AcpiSupport.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_ACPI_SET_ACPI_TABLE)(
      ...
      );

    The function modifier is *NOT* consistent with Framework Acpi specification. The EFI_BOOTSERVICE modifier
    is removed from the function declaration.

  3. Include/Protocol/AcpiSupport.h
    typedef
    EFI_STATUS
    (EFIAPI *EFI_ACPI_PUBLISH_TABLES)(
      ...
      );

    The function modifier is *NOT* consistent with Framework Acpi specification. The EFI_BOOTSERVICE modifier
    is removed from the function declaration.
