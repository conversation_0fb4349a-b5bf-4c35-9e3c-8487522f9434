/** @file
  Provides the parent dispatch service for the USB SMI source generator.

Copyright (c) 2007 - 2010, Intel Corporation. All rights reserved.<BR>
This program and the accompanying materials are licensed and made available under 
the terms and conditions of the BSD License that accompanies this distribution.  
The full text of the license may be found at
http://opensource.org/licenses/bsd-license.php.                                          
    
THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,                     
WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.

  @par Revision Reference:
  This Protocol is defined in Framework of EFI SMM Core Interface Spec
  Version 0.9.

**/

#ifndef _EFI_SMM_USB_DISPATCH_H_
#define _EFI_SMM_USB_DISPATCH_H_

//
// Share some common definitions with PI SMM
//
#include <Protocol/SmmUsbDispatch2.h>

//
// Global ID for the USB Protocol
//
#define EFI_SMM_USB_DISPATCH_PROTOCOL_GUID \
  { \
    0xa05b6ffd, 0x87af, 0x4e42, {0x95, 0xc9, 0x62, 0x28, 0xb6, 0x3c, 0xf3, 0xf3 } \
  }

typedef struct _EFI_SMM_USB_DISPATCH_PROTOCOL  EFI_SMM_USB_DISPATCH_PROTOCOL;

typedef struct {
  ///
  /// Describes whether this child handler will be invoked in response to a USB legacy
  /// emulation event, such as port-trap on the PS/2* keyboard control registers, or to a
  /// USB wake event, such as resumption from a sleep state. 
  ///
  EFI_USB_SMI_TYPE          Type;
  ///
  /// The device path is part of the context structure and describes the location of the
  /// particular USB host controller in the system for which this register event will occur.
  /// This location is important because of the possible integration of several USB host
  /// controllers in a system. 
  ///
  EFI_DEVICE_PATH_PROTOCOL  *Device;
} EFI_SMM_USB_DISPATCH_CONTEXT;

//
// Member functions
//
/**
  Dispatch function for a USB SMI handler.

  @param[in]  DispatchHandle    Handle of this dispatch function.
  @param[in]  DispatchContext   Pointer to the dispatch function's context.
                                The DispatchContext fields are filled in
                                by the dispatching driver prior to
                                invoking this dispatch function.

**/
typedef
VOID
(EFIAPI *EFI_SMM_USB_DISPATCH)(
  IN  EFI_HANDLE                    DispatchHandle,
  IN  EFI_SMM_USB_DISPATCH_CONTEXT  *DispatchContext
  );

/**
  Register a child SMI source dispatch function with a parent SMM driver.

  @param[in]  This              The pointer to the EFI_SMM_USB_DISPATCH_PROTOCOL instance.
  @param[in]  DispatchFunction  The pointer to dispatch function to be invoked 
                                for this SMI source.
  @param[in]  DispatchContext   The pointer to the dispatch function's context.
                                The caller fills this context in before calling
                                the register function to indicate to the register
                                function the USB SMI types for which the dispatch
                                function should be invoked.
  @param[out] DispatchHandle    The handle generated by the dispatcher to track the 
                                function instance.

  @retval EFI_SUCCESS           The dispatch function has been successfully
                                registered and the SMI source has been enabled.
  @retval EFI_DEVICE_ERROR      The driver was unable to enable the SMI source.
  @retval EFI_OUT_OF_RESOURCES  Not enough memory (system or SMM) to manage this
                                child.
  @retval EFI_INVALID_PARAMETER DispatchContext is invalid. The USB SMI type
                                is not within valid range.

**/
typedef
EFI_STATUS
(EFIAPI *EFI_SMM_USB_REGISTER)(
  IN  EFI_SMM_USB_DISPATCH_PROTOCOL           *This,
  IN  EFI_SMM_USB_DISPATCH                    DispatchFunction,
  IN  EFI_SMM_USB_DISPATCH_CONTEXT            *DispatchContext,
  OUT EFI_HANDLE                              *DispatchHandle
  );

/**
  Unregisters a USB service.

  @param[in]  This              The pointer to the EFI_SMM_USB_DISPATCH_PROTOCOL instance.
  @param[in]  DispatchHandle    Handle of the service to remove.

  @retval EFI_SUCCESS           The dispatch function has been successfully
                                unregistered and the SMI source has been disabled
                                if there are no other registered child dispatch
                                functions for this SMI source.
  @retval EFI_INVALID_PARAMETER The DispatchHandle was not valid.

**/
typedef
EFI_STATUS
(EFIAPI *EFI_SMM_USB_UNREGISTER)(
  IN EFI_SMM_USB_DISPATCH_PROTOCOL            *This,
  IN EFI_HANDLE                               DispatchHandle
  );

///
/// The EFI_SMM_USB_DISPATCH_PROTOCOL provides the ability to install child handlers for the
/// given event types.
///
struct _EFI_SMM_USB_DISPATCH_PROTOCOL {
  EFI_SMM_USB_REGISTER    Register;
  EFI_SMM_USB_UNREGISTER  UnRegister;
};

extern EFI_GUID gEfiSmmUsbDispatchProtocolGuid;

#endif
