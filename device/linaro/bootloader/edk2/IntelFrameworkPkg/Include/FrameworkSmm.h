/** @file
  Header file that support Framework extensions to UEFI/PI for SMM modules.

  This header file must include Framework extension definitions common to DXE
  modules.

Copyright (c) 2007 - 2010, Intel Corporation. All rights reserved.<BR>
This program and the accompanying materials are licensed and made available under 
the terms and conditions of the BSD License that accompanies this distribution.  
The full text of the license may be found at
http://opensource.org/licenses/bsd-license.php.                                          
    
THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,                     
WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.

**/

#ifndef _FRAMEWORK_SMM_H_
#define _FRAMEWORK_SMM_H_

#include <FrameworkDxe.h>
#include <Framework/SmmCis.h>

#endif
