## @file
# SMBUS library that layers on top of the SMBUS PPI.
#
# Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PeiSmbusLibSmbusPpi
  MODULE_UNI_FILE                = PeiSmbusLibSmbusPpi.uni
  FILE_GUID                      = 51C4C059-67F0-4e3c-9A55-FF42A8291C8C
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = SmbusLib|PEIM 


#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  SmbusLib.c
  PeiSmbusLib.c
  InternalSmbusLib.h


[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec


[LibraryClasses]
  BaseMemoryLib
  PeiServicesLib
  DebugLib
  PeiServicesTablePointerLib

[Ppis]
  gEfiPeiSmbusPpiGuid                           ## CONSUMES

[Depex]
  gEfiPeiSmbusPpiGuid
