// /** @file
// Library to abstract Framework extensions that conflict with UEFI 2.0 Specification.
//
// This library is helpful to port Framework/Tinao code that has conflicts with UEFI 2.0.
// It hides the old conflicts with library functions and supporting implementations of
// the old (EDK/EFI 1.10) and new (EDK II/UEFI 2.0) way.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Library to abstract Framework extensions that conflict with the UEFI 2.0 Specification"

#string STR_MODULE_DESCRIPTION          #language en-US "This library is helpful to port Framework/Tiano code that has conflicts with UEFI 2.0. It hides the old conflicts with library functions and supporting implementations of the old (EDK/EFI 1.10) and new (EDK II/UEFI 2.0) methods."

