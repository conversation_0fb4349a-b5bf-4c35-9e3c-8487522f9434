## @file
#  Library to abstract Framework extensions that conflict with UEFI 2.0 Specification.
#
#  This library is helpful to port Framework/Tinao code that has conflicts with UEFI 2.0. 
#  It hides the old conflicts with library functions and supporting implementations of
#  the old (EDK/EFI 1.10) and new (EDK II/UEFI 2.0) way.
#
#  Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FrameworkUefiLib
  MODULE_UNI_FILE                = FrameworkUefiLib.uni
  FILE_GUID                      = B2F0D71A-A39F-4094-854B-0C6BA6910CCE
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = UefiLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER

#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  UefiLibPrint.c
  UefiNotTiano.c
  UefiDriverModel.c
  Console.c
  UefiLib.c
  UefiLibInternal.h

[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec

[LibraryClasses]
  PrintLib
  PcdLib
  MemoryAllocationLib
  DebugLib
  BaseMemoryLib
  BaseLib
  UefiBootServicesTableLib
  DevicePathLib
  
[Guids]
  gEfiEventReadyToBootGuid                      ## SOMETIMES_CONSUMES  ## Event
  gEfiEventLegacyBootGuid                       ## SOMETIMES_CONSUMES  ## Event

[Protocols]
  gEfiDriverBindingProtocolGuid                 ## SOMETIMES_PRODUCES
  gEfiSimpleTextOutProtocolGuid                 ## SOMETIMES_CONSUMES
  gEfiGraphicsOutputProtocolGuid                ## SOMETIMES_CONSUMES
  gEfiHiiFontProtocolGuid                       ## SOMETIMES_CONSUMES
  gEfiComponentNameProtocolGuid                 ## SOMETIMES_PRODUCES
  gEfiComponentName2ProtocolGuid                ## SOMETIMES_PRODUCES
  gEfiDriverConfigurationProtocolGuid           ## SOMETIMES_PRODUCES
  gEfiDriverConfiguration2ProtocolGuid          ## SOMETIMES_PRODUCES
  gEfiDriverDiagnosticsProtocolGuid             ## SOMETIMES_PRODUCES
  gEfiDriverDiagnostics2ProtocolGuid            ## SOMETIMES_PRODUCES
  gEfiUgaDrawProtocolGuid                       ## SOMETIMES_CONSUMES


[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdUefiLibMaxPrintBufferSize ## SOMETIMES_CONSUMES

[FeaturePcd]
  gEfiMdePkgTokenSpaceGuid.PcdDriverDiagnosticsDisable    ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdComponentNameDisable        ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdDriverDiagnostics2Disable   ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdComponentName2Disable       ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdUgaConsumeSupport           ## CONSUMES

