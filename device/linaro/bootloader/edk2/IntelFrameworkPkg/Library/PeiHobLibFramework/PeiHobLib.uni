// /** @file
// Instance of HOB Library using PEI Services.
//
// HOB Library implementation that uses PEI Services to retrieve the HOB List.
// This library instance uses EFI_HOB_TYPE_CV defined in Intel framework HOB specification v0.9
// to implement HobLib BuildCvHob() API.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Instance of HOB Library using PEI Services"

#string STR_MODULE_DESCRIPTION          #language en-US "The HOB Library implementation that uses PEI Services to retrieve the HOB List. This library instance uses EFI_HOB_TYPE_CV defined in Intel Framework HOB Specification v0.9 to implement the HobLib BuildCvHob() API."

