## @file
# Framework SMM driver entry point library.
#
# Register driver in SMRAM and wrapper driver's library constructors and entry point.
#
# Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DxeSmmDriverEntryPoint
  MODULE_UNI_FILE                = DxeSmmDriverEntryPoint.uni
  FILE_GUID                      = 79C5C7B7-1083-42a6-AD15-2A4E7C4274D7
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = UefiDriverEntryPoint|DXE_SMM_DRIVER


#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  DriverEntryPoint.c


[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec


[LibraryClasses]
  DebugLib
  UefiBootServicesTableLib
  DevicePathLib

[Protocols]
  gEfiLoadedImageProtocolGuid                   ## CONSUMES
  gEfiSmmBaseProtocolGuid                       ## CONSUMES
  gEfiDevicePathProtocolGuid                    ## CONSUMES
  
[Depex]
  gEfiSmmBaseProtocolGuid

