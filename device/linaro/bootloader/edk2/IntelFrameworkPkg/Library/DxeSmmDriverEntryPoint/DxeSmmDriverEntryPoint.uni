// /** @file
// Framework SMM driver entry point library.
//
// Register driver in SMRAM and wrapper driver's library constructors and entry point.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// This program and the accompanying materials
// are licensed and made available under the terms and conditions of the BSD License
// which accompanies this distribution. The full text of the license may be found at
// http://opensource.org/licenses/bsd-license.php
// THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
// WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "SMM Driver Entry Point Library"

#string STR_MODULE_DESCRIPTION          #language en-US "Registers a driver in SMRAM, and wrappers the driver's library constructors and entry point."

