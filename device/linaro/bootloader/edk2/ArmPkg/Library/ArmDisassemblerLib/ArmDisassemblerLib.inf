#/** @file
#  ARM Disassembler library
#
#  Copyright (c) 2008, Apple Inc. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmDisassemblerLib
  FILE_GUID                      = 7ACEC173-F15D-426C-8F2F-BD86B4183EF1
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmDisassemblerLib


[Sources.ARM]
  ArmDisassembler.c
  ThumbDisassembler.c

[Sources.AARCH64]
  Aarch64Disassembler.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  BaseLib
  PrintLib
  DebugLib
  PeCoffGetEntryPointLib
