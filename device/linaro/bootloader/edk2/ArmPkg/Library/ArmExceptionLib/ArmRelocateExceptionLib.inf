## @file
# Instance of CpuExceptionHandlerLib Library for ARM/AArch64 architectures
#
# This library instance is used when exception vectors must be relocated to
# a specific address.  The address is specified by PcdCpuVectorBaseAddress.
# Since the alignment requirement for in-place exception handlers causes
# image size to increase, this instance is useful for modules that need to
# minimize space used in their FV (like XIP modules).  See ArmExceptionLib.inf
# for the in-place exception handler alternative.
#
#  Copyright (c) 2011-2012, ARM Limited. All rights reserved.
#  Copyright (c) 2016 HP Development Company, L.P.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmRelocateExceptionLib
  FILE_GUID                      = 62AA447A-1FBA-429E-9E0D-CE0D2D8DCF58
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CpuExceptionHandlerLib

[Sources.common]
  ArmExceptionLib.c

[Sources.Arm]
  Arm/ArmException.c
  Arm/ExceptionSupport.asm | RVCT
  Arm/ExceptionSupport.S   | GCC

[Sources.AARCH64]
  AArch64/AArch64Exception.c
  AArch64/ExceptionSupport.S

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  DebugLib
  DefaultExceptionHandlerLib
  BaseMemoryLib
  CacheMaintenanceLib

[Pcd]
  gArmTokenSpaceGuid.PcdDebuggerExceptionSupport
  gArmTokenSpaceGuid.PcdCpuVectorBaseAddress

[BuildOptions]
  # We must pass a define to specify that we are relocating vectors so the
  # vector alignment is relaxed (space savings); note that this must be done
  # as a define and not a PCD since it affects assembly directives.
  *_*_*_PP_FLAGS = -DARM_RELOCATE_VECTORS
  *_*_*_CC_FLAGS = -DARM_RELOCATE_VECTORS
