#/** @file
# Reset System lib using PSCI hypervisor or secure monitor calls
#
# Copyright (c) 2008, Apple Inc. All rights reserved.<BR>
# Copyright (c) 2014, Linaro Ltd. All rights reserved.<BR>
# Copyright (c) 2014, ARM Ltd. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmPsciResetSystemLib
  FILE_GUID                      = A8F59B69-A105-41C7-8F5A-2C60DD7FD7AA
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = EfiResetSystemLib

[Sources]
  ArmPsciResetSystemLib.c

[Packages]
  ArmPkg/ArmPkg.dec
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec

[LibraryClasses]
  DebugLib
  BaseLib
  ArmSmcLib
