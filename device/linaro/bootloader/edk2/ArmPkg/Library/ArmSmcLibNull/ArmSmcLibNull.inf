#/** @file
#
#  ArmSmcLib when no SMC support is desired (might be the case for CPU without the
#  Trustzone support / ARM Security Extension)
#
#  Copyright (c) 2012-2013, ARM Ltd. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmSmcLibNull
  FILE_GUID                      = 140e8004-16e1-4de1-a352-c6ef51110ecf
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmSmcLib

[Sources]
  ArmSmcLibNull.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec
