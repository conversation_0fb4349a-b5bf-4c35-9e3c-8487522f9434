#/** @file
#
#
# Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UncachedMemoryAllocationLib
  FILE_GUID                      = 3C1EA826-696A-4E8A-B89D-3C5369B84F2A
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = UncachedMemoryAllocationLib
  CONSTRUCTOR                    = DebugUncachedMemoryAllocationLibConstructor

[Sources.common]
  DebugUncachedMemoryAllocationLib.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec


[LibraryClasses]
  BaseLib
  MemoryAllocationLib
  ArmLib

[Protocols]
  gEfiCpuArchProtocolGuid
  gVirtualUncachedPagesProtocolGuid

[FixedPcd]
  gArmTokenSpaceGuid.PcdArmUncachedMemoryMask


[Depex]
  gEfiCpuArchProtocolGuid AND gVirtualUncachedPagesProtocolGuid
