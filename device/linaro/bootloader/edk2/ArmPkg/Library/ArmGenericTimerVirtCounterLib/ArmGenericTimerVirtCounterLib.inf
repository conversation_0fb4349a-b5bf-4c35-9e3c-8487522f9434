#/** @file
#  Implement ArmGenericTimerCounterLib using the virtual timer
#
#  Copyright (c) 2014, Linaro Ltd. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmGenericTimerVirtCounterLib
  FILE_GUID                      = 3C0D77CC-4F27-49C8-B25C-2D01D81ED4D8
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmGenericTimerCounterLib

[Sources]
  ArmGenericTimerVirtCounterLib.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
