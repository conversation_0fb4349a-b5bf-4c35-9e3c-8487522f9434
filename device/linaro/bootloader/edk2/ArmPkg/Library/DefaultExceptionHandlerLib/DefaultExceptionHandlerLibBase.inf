#/** @file
#
# Copyright (c) 2012, ARM Ltd. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DefaultExceptionHandlerBaseLib
  FILE_GUID                      = 3d5261d5-5eb7-4559-98e7-475aa9d0dc42
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DefaultExceptionHandlerLib

[Sources.common]
  DefaultExceptionHandlerBase.c

[Sources.ARM]
  Arm/DefaultExceptionHandler.c

[Sources.AARCH64]
  AArch64/DefaultExceptionHandler.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  BaseLib
  PrintLib
  DebugLib
  PeCoffGetEntryPointLib
  ArmDisassemblerLib
  SerialPortLib
