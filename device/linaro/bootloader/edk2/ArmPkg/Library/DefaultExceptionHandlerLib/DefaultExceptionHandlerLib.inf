#/** @file
#
#  Copyright (c) 2008, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2011 - 2013, ARM Ltd. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DefaultExceptionHandlerLib
  FILE_GUID                      = EACDB354-DF1A-4AF9-A171-499737ED818F
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DefaultExceptionHandlerLib

[Sources.common]
  DefaultExceptionHandlerUefi.c

[Sources.ARM]
  Arm/DefaultExceptionHandler.c

[Sources.AARCH64]
  AArch64/DefaultExceptionHandler.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  UefiLib
  BaseLib
  PrintLib
  DebugLib
  PeCoffGetEntryPointLib
  ArmDisassemblerLib
  SerialPortLib
