
#/** @file
# Debug Library for UEFI drivers
#
# Library to abstract Framework extensions that conflict with UEFI 2.0 Specification
# Copyright (c) 2007, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SemiHostingDebugLib
  FILE_GUID                      = 2A8D3FC4-8DB1-4D27-A3F3-780AF03CF848
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DebugLib|BASE SEC DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER


[Sources.common]
  DebugLib.c


[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  BaseMemoryLib
  BaseLib
  PcdLib
  PrintLib
  SemihostLib
  DebugLib

[Pcd.common]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPrintErrorLevel
  gEfiMdePkgTokenSpaceGuid.PcdDebugClearMemoryValue
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

