#/** @file
#
#  Copyright (c) 2016 Linaro Ltd. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmMmuPeiLib
  FILE_GUID                      = b50d8d53-1ad1-44ea-9e69-8c89d4a6d08b
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmMmuLib|PEIM
  CONSTRUCTOR                    = ArmMmuPeiLibConstructor

[Sources.AARCH64]
  AArch64/ArmMmuLibCore.c
  AArch64/ArmMmuPeiLibConstructor.c
  AArch64/ArmMmuLibReplaceEntry.S

[Packages]
  ArmPkg/ArmPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  CacheMaintenanceLib
  MemoryAllocationLib

[Pcd.AARCH64]
  gEmbeddedTokenSpaceGuid.PcdPrePiCpuMemorySize
