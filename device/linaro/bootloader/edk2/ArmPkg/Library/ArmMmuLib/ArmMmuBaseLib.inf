#/** @file
#
#  Copyright (c) 2016 Linaro Ltd. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmMmuBaseLib
  FILE_GUID                      = da8f0232-fb14-42f0-922c-63104d2c70bd
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmMmuLib
  CONSTRUCTOR                    = ArmMmuBaseLibConstructor

[Sources.AARCH64]
  AArch64/ArmMmuLibCore.c
  AArch64/ArmMmuLibReplaceEntry.S

[Sources.ARM]
  Arm/ArmMmuLibCore.c
  Arm/ArmMmuLibV7Support.S   |GCC 
  Arm/ArmMmuLibV7Support.asm |RVCT 

[Packages]
  ArmPkg/ArmPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  CacheMaintenanceLib
  MemoryAllocationLib

[Pcd.AARCH64]
  gEmbeddedTokenSpaceGuid.PcdPrePiCpuMemorySize

[Pcd.ARM]
  gArmTokenSpaceGuid.PcdNormalMemoryNonshareableOverride
