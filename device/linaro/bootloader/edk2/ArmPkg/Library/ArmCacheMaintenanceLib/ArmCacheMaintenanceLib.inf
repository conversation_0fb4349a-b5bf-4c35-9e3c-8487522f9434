#/** @file
#  Implement CacheMaintenanceLib for ARM architectures
#
#  Copyright (c) 2008, Apple Inc. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmCacheMaintenanceLib
  FILE_GUID                      = 1A20BE1F-33AD-450C-B49A-7123FCA8B7F9
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CacheMaintenanceLib

[Sources.common]
  ArmCacheMaintenanceLib.c

[Packages]
  ArmPkg/ArmPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
