#/* @file
#  Copyright (c) 2015, Linaro Ltd. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#*/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmGicArchLib
  FILE_GUID                      = cd67f41a-26e9-4482-90c9-a9aff803382a
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmGicArchLib|DXE_DRIVER UEFI_DRIVER UEFI_APPLICATION
  CONSTRUCTOR                    = ArmGicArchLibInitialize

[Sources.ARM]
  Arm/ArmGicArchLib.c

[Sources.AARCH64]
  AArch64/ArmGicArchLib.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmGicLib
