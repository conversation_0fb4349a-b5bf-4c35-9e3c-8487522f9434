#/** @file
#
#  Copyright (c) 2011 - 2014, ARM Limited. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmArchTimerLib
  FILE_GUID                      = 82da1b44-d2d6-4a7d-bbf0-a0cb67964034
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = TimerLib
  CONSTRUCTOR                    = TimerConstructor

[Sources.common]
  ArmArchTimerLib.c

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  DebugLib
  ArmLib
  BaseLib
  ArmGenericTimerCounterLib

[Pcd]
  gArmTokenSpaceGuid.PcdArmArchTimerFreqInHz
