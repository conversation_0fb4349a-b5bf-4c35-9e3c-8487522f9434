#/** @file
#
#  Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmDmaLib
  FILE_GUID                      = F1BD6B36-B705-43aa-8A28-33F58ED85EFB
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DmaLib
  CONSTRUCTOR                    = ArmDmaLibConstructor

[Sources.common]
  ArmDmaLib.c

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec


[LibraryClasses]
  DebugLib
  DxeServicesTableLib
  UefiBootServicesTableLib
  MemoryAllocationLib
  UncachedMemoryAllocationLib
  IoLib
  BaseMemoryLib

[Protocols]
  gEfiCpuArchProtocolGuid

[Guids]

[Pcd]
  gArmTokenSpaceGuid.PcdArmDmaDeviceOffset

[Depex]
  gEfiCpuArchProtocolGuid
