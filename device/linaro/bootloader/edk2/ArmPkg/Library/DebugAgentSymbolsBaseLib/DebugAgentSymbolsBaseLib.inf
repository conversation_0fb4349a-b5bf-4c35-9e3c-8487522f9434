#
#  Copyright (c) 2011-2012, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DebugAgentSymbolsBaseLib
  FILE_GUID                      = 9055e2e0-9b33-11e0-a7d7-0002a5d5c51b
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DebugAgentLib

[Sources.common]
  DebugAgentSymbolsBaseLib.c

[Sources.ARM]
  Arm/DebugAgentException.asm        | RVCT
  Arm/DebugAgentException.S          | GCC

[Sources.AARCH64]
  AArch64/DebugAgentException.S

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  DebugLib
  DefaultExceptionHandlerLib
  PcdLib
  PeCoffExtraActionLib
  PeCoffLib

[Pcd]
  gArmTokenSpaceGuid.PcdSecureFvBaseAddress
  gArmTokenSpaceGuid.PcdFvBaseAddress
