#/** @file
# PeCoff extra action libary for DXE phase that run Unix emulator.
#
# Lib to provide memory journal status code reporting Routines
# Copyright (c) 2007 - 2010, Intel Corporation. All rights reserved.<BR>
# Portions copyright (c) 2010, Apple Inc. All rights reserved.<BR>
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DebugUnixPeCoffExtraActionLib
  FILE_GUID                      = C3E9448E-1726-42fb-9368-41F75B038C0C
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PeCoffExtraActionLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM
#

[Sources.common]
  DebugPeCoffExtraActionLib.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  DebugLib
