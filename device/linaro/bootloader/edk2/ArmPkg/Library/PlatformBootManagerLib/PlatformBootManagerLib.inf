## @file
#  Implementation for PlatformBootManagerLib library class interfaces.
#
#  Copyright (C) 2015-2016, Red Hat, Inc.
#  Copyright (c) 2014, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2007 - 2014, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2016, Linaro Ltd. All rights reserved.<BR>
#
#  This program and the accompanying materials are licensed and made available
#  under the terms and conditions of the BSD License which accompanies this
#  distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR
#  IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformBootManagerLib
  FILE_GUID                      = 92FD2DE3-B9CB-4B35-8141-42AD34D73C9F
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformBootManagerLib|DXE_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM AARCH64
#

[Sources]
  PlatformBm.c
  QuietBoot.c

[Packages]
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  DevicePathLib
  DxeServicesLib
  MemoryAllocationLib
  PcdLib
  PrintLib
  UefiBootManagerLib
  UefiBootServicesTableLib
  UefiLib

[FeaturePcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdBootlogoOnlyEnable
  gEfiMdePkgTokenSpaceGuid.PcdUgaConsumeSupport

[FixedPcd]
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLogoFile
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdShellFile
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultBaudRate
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultDataBits
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultParity
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultStopBits
  gEfiMdePkgTokenSpaceGuid.PcdDefaultTerminalType

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPlatformBootTimeOut

[Guids]
  gEfiFileInfoGuid
  gEfiFileSystemInfoGuid
  gEfiFileSystemVolumeLabelInfoIdGuid
  gEfiEndOfDxeEventGroupGuid
  gEfiTtyTermGuid

[Protocols]
  gEfiDevicePathProtocolGuid
  gEfiGraphicsOutputProtocolGuid
  gEfiLoadedImageProtocolGuid
  gEfiOEMBadgingProtocolGuid
  gEfiPciRootBridgeIoProtocolGuid
  gEfiSimpleFileSystemProtocolGuid
