#/** @file
#
#  DXE CPU driver
#
#  Copyright (c) 2009, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2011-2013, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmCpuDxe
  FILE_GUID                      = B8D9777E-D72A-451F-9BDB-BAFB52A68415
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = CpuDxeInitialize

[Sources.Common]
  CpuDxe.c
  CpuDxe.h
  CpuMpCore.c
  CpuMmuCommon.c
  Exception.c

[Sources.ARM]
  Arm/Mmu.c

[Sources.AARCH64]
  AArch64/Mmu.c

[Packages]
  ArmPkg/ArmPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  ArmLib
  ArmMmuLib
  BaseMemoryLib
  CacheMaintenanceLib
  CpuLib
  CpuExceptionHandlerLib
  DebugLib
  DefaultExceptionHandlerLib
  DxeServicesTableLib
  HobLib
  PeCoffGetEntryPointLib
  UefiDriverEntryPoint
  UefiLib

[Protocols]
  gEfiCpuArchProtocolGuid
  gEfiDebugSupportPeriodicCallbackProtocolGuid
  gVirtualUncachedPagesProtocolGuid

[Guids]
  gEfiDebugImageInfoTableGuid
  gArmMpCoreInfoGuid
  gIdleLoopEventGuid
  gEfiVectorHandoffTableGuid

[Pcd.common]
  gArmTokenSpaceGuid.PcdVFPEnabled

[FeaturePcd.common]
  gArmTokenSpaceGuid.PcdCpuDxeProduceDebugSupport
  gArmTokenSpaceGuid.PcdDebuggerExceptionSupport

[Depex]
  TRUE
