## @file
# Component description file for BootMode module
#
# This module provides platform specific function to detect boot mode.
# Copyright (c) 2006 - 2010, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CpuPei
  FILE_GUID                      = 2FD8B7AD-F8FA-4021-9FC0-0AA572147CDC
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InitializeCpuPeim

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM
#

[Sources]
  CpuPei.c

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  HobLib
  ArmLib

[Ppis]
  gArmMpCoreInfoPpiGuid

[Guids]
  gArmMpCoreInfoGuid

[FixedPcd]
  gEmbeddedTokenSpaceGuid.PcdPrePiCpuMemorySize
  gEmbeddedTokenSpaceGuid.PcdPrePiCpuIoSize

[Depex]
  gEfiPeiMemoryDiscoveredPpiGuid

