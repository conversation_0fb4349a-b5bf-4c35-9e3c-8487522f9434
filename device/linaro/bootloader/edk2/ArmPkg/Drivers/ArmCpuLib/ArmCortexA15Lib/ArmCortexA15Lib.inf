#/* @file
#  Copyright (c) 2011-2014, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#*/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmCortexA15Lib
  FILE_GUID                      = 501b1c8f-21d5-4ef5-a565-435b7f0aae2d
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmCpuLib

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  ArmGenericTimerCounterLib
  PcdLib

[Sources.common]
  ArmCortexA15Lib.c

[FixedPcd]
  gArmTokenSpaceGuid.PcdArmArchTimerFreqInHz
