#/* @file
#  Copyright (c) 2011 - 2014, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#*/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmCortexAEMv8Lib
  FILE_GUID                      = 8ab5a7e3-86b1-4dd3-a092-09ee801e774b
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmCpuLib

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmGenericTimerCounterLib
  PcdLib

[Sources.common]
  ArmCortexAEMv8Lib.c

[FixedPcd]
  gArmTokenSpaceGuid.PcdArmArchTimerFreqInHz
