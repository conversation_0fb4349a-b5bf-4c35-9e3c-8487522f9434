#/* @file
#  Copyright (c) 2011-2013, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#*/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmCortexA9Lib
  FILE_GUID                      = c9709ea3-1beb-4806-889a-8a1d5e5e1697
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmCpuLib

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  ArmLib
  ArmPlatformLib
  IoLib
  PcdLib

[Sources.common]
  ArmCortexA9Lib.c
  ArmCortexA9Helper.asm     | RVCT
  ArmCortexA9Helper.S       | GCC

