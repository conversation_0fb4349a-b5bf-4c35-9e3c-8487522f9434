#
#  Copyright (c) 2013-2014, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#

[Defines]
  INF_VERSION                    = 0x00010016
  BASE_NAME                      = GenericWatchdogDxe
  FILE_GUID                      = 0619f5c2-4858-4caa-a86a-73a21a18df6b
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = GenericWatchdogEntry

[Sources.common]
  GenericWatchdogDxe.c

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  ArmGenericTimerCounterLib
  BaseLib
  BaseMemoryLib
  DebugLib
  IoLib
  PcdLib
  UefiLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiRuntimeServicesTableLib

[Pcd.common]
  gArmTokenSpaceGuid.PcdGenericWatchdogControlBase
  gArmTokenSpaceGuid.PcdGenericWatchdogRefreshBase
  gArmTokenSpaceGuid.PcdGenericWatchdogEl2IntrNum

[Protocols]
  gEfiWatchdogTimerArchProtocolGuid
  gHardwareInterruptProtocolGuid

[Depex]
  gHardwareInterruptProtocolGuid
