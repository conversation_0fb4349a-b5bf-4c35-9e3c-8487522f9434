#/** @file
#
#  Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2012 - 2015, ARM Ltd. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmGicDxe
  FILE_GUID                      = DE371F7C-DEC4-4D21-ADF1-593ABCC15882
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InterruptDxeInitialize

[Sources.common]
  ArmGicDxe.c
  ArmGicCommonDxe.c

  GicV2/ArmGicV2Dxe.c
  GicV3/ArmGicV3Dxe.c

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmGicLib
  BaseLib
  UefiLib
  UefiBootServicesTableLib
  DebugLib
  PrintLib
  MemoryAllocationLib
  UefiDriverEntryPoint
  IoLib
  PcdLib

[Protocols]
  gHardwareInterruptProtocolGuid
  gEfiCpuArchProtocolGuid

[Pcd.common]
  gArmTokenSpaceGuid.PcdGicDistributorBase
  gArmTokenSpaceGuid.PcdGicRedistributorsBase
  gArmTokenSpaceGuid.PcdGicInterruptInterfaceBase
  gArmTokenSpaceGuid.PcdArmGicV3WithV2Legacy

[Depex]
  gEfiCpuArchProtocolGuid
