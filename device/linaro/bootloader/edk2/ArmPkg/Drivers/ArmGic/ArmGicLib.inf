#/* @file
#  Copyright (c) 2011-2015, ARM Limited. All rights reserved.
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#*/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmGicLib
  FILE_GUID                      = 03d05ee4-cdeb-458c-9dfc-993f09bdf405
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmGicLib

[Sources]
  ArmGicLib.c
  ArmGicNonSecLib.c

  GicV2/ArmGicV2Lib.c
  GicV2/ArmGicV2NonSecLib.c

[Sources.ARM]
  GicV3/Arm/ArmGicV3.S     | GCC
  GicV3/Arm/ArmGicV3.asm   | RVCT

[Sources.AARCH64]
  GicV3/AArch64/ArmGicV3.S

[LibraryClasses]
  ArmLib
  DebugLib
  IoLib
  ArmGicArchLib

[Packages]
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec
  MdePkg/MdePkg.dec

[Pcd]
  gArmPlatformTokenSpaceGuid.PcdCoreCount

[FeaturePcd]
  gArmTokenSpaceGuid.PcdArmGicV3WithV2Legacy
