#/** @file
#
#    Component description file for Timer DXE module
#
#  Copyright (c) 2009 - 2010, Apple Inc. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmTimerDxe
  FILE_GUID                      = 49ea041e-6752-42ca-b0b1-7344fe2546b7
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = TimerInitialize

[Sources.common]
  TimerDxe.c

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
  UefiRuntimeServicesTableLib
  UefiLib
  UefiBootServicesTableLib
  BaseMemoryLib
  DebugLib
  UefiDriverEntryPoint
  IoLib
  ArmGenericTimerCounterLib

[Guids]

[Protocols]
  gEfiTimerArchProtocolGuid
  gHardwareInterruptProtocolGuid

[Pcd.common]
  gEmbeddedTokenSpaceGuid.PcdTimerPeriod
  gArmTokenSpaceGuid.PcdArmArchTimerSecIntrNum
  gArmTokenSpaceGuid.PcdArmArchTimerIntrNum
  gArmTokenSpaceGuid.PcdArmArchTimerVirtIntrNum
  gArmTokenSpaceGuid.PcdArmArchTimerHypIntrNum

[Depex]
  gHardwareInterruptProtocolGuid
