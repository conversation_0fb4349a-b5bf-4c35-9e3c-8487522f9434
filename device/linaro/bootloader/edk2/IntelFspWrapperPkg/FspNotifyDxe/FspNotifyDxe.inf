## @file
# FSP DXE Module
#
# This driver will register two callbacks to call fsp's notifies.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspNotifyDxe
  FILE_GUID                      = 8714C537-6D4B-4247-AA6C-29E8495F9100
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = FspDxeEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  FspNotifyDxe.c
  LoadBelow4G.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec
  IntelFspWrapperPkg/IntelFspWrapperPkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  BaseMemoryLib
  UefiLib
  FspApiLib
  PeCoffLib
  CacheMaintenanceLib
  DxeServicesLib

[Protocols]
  gEfiPciEnumerationCompleteProtocolGuid            ## CONSUMES

[Pcd]
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspBase       ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvSecondFspBase ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspSize       ## CONSUMES

[Depex]
  TRUE
