/** @file
  Provides the pointer to top of temporary ram.

  Copyright (c) 2014, Intel Corporation. All rights reserved.<BR>
  This program and the accompanying materials
  are licensed and made available under the terms and conditions of the BSD License
  which accompanies this distribution.  The full text of the license may be found at
  http://opensource.org/licenses/bsd-license.php.

  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.

**/

#ifndef _TOP_OF_TEMPORARY_RAM_H_
#define _TOP_OF_TEMPORARY_RAM_H_

extern EFI_GUID gTopOfTemporaryRamPpiGuid;

#endif
