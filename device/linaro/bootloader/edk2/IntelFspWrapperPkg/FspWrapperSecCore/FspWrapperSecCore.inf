## @file
# This is the first module taking control of the platform upon power-on/reset.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspWrapperSecCore
  FILE_GUID                      = 1BA0062E-C779-4582-8566-336AE8F78F09
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  SecMain.c
  SecMain.h
  FindPeiCore.c

[Sources.IA32]
  Ia32/ResetVec.asm16 | MSFT
  Ia32/ResetVec.asm16 | INTEL
  Ia32/Dummy.asm

[Binaries.Ia32]
  RAW|Vtf0/Bin/ResetVec.ia32.raw |GCC

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  IntelFspPkg/IntelFspPkg.dec
  IntelFspWrapperPkg/IntelFspWrapperPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  FspPlatformInfoLib
  FspPlatformSecLib
  DebugAgentLib
  UefiCpuLib
  PeCoffGetEntryPointLib
  PeCoffExtraActionLib

[Ppis]
  gTopOfTemporaryRamPpiGuid                                 ## CONSUMES

[FixedPcd]
  gFspWrapperTokenSpaceGuid.PcdSecCoreMaxPpiSupported       ## CONSUMES

[Pcd]
  gFspWrapperTokenSpaceGuid.PcdPeiTemporaryRamStackSize     ## CONSUMES
