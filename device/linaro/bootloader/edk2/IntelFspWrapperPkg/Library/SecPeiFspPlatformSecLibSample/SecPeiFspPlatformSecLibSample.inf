## @file
#  Sample to provide FSP wrapper platform sec related function.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SecPeiFspPlatformSecLibSample
  FILE_GUID                      = 4E1C4F95-90EA-47de-9ACC-B8920189A1F5
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspPlatformSecLib


#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FspPlatformSecLibSample.c
  SecRamInitData.c
  SaveSecContext.c
  SecPlatformInformation.c
  SecGetPerformance.c
  SecTempRamSupport.c
  PlatformInit.c

[Sources.IA32]
  Ia32/SecEntry.asm
  Ia32/PeiCoreEntry.asm
  Ia32/AsmSaveSecContext.asm
  Ia32/Stack.asm
  Ia32/Fsp.h
  Ia32/SecEntry.S
  Ia32/PeiCoreEntry.S
  Ia32/AsmSaveSecContext.S
  Ia32/Stack.S

################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  IntelFspPkg/IntelFspPkg.dec
  IntelFspWrapperPkg/IntelFspWrapperPkg.dec

[LibraryClasses]
  LocalApicLib

[Ppis]
  gEfiSecPlatformInformationPpiGuid       ## CONSUMES
  gPeiSecPerformancePpiGuid               ## CONSUMES
  gEfiTemporaryRamSupportPpiGuid          ## CONSUMES

[Pcd]
  gFspWrapperTokenSpaceGuid.PcdPeiTemporaryRamStackSize         ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspBase                   ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspSize                   ## CONSUMES

[FixedPcd]
  gFspWrapperTokenSpaceGuid.PcdCpuMicrocodePatchAddress         ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdCpuMicrocodePatchRegionSize      ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashMicroCodeOffset             ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashCodeCacheAddress            ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashCodeCacheSize               ## CONSUMES
