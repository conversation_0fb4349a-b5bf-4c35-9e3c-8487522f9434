## @file
#  Provide FSP API related function.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspApiLib
  FILE_GUID                      = 6E4CB8C5-6144-4ae3-BA52-B6AFBCB2B2F5
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspApiLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FspApiLib.c

[Sources.IA32]
  IA32/DispatchExecute.c

[Sources.X64]
  X64/DispatchExecute.c
  X64/Thunk64To32.asm
  X64/Thunk64To32.S

################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  IntelFspPkg/IntelFspPkg.dec
  IntelFspWrapperPkg/IntelFspWrapperPkg.dec

[LibraryClasses]
  BaseLib

[Guids]
  gFspHeaderFileGuid            ## CONSUMES ## GUID
