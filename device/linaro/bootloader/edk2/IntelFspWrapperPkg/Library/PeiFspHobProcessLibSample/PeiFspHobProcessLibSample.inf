## @file
#  Sample to provide FSP hob process related function.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PeiFspHobProcessLibSample
  FILE_GUID                      = C7B7070B-E5A8-4b86-9110-BDCA1095F496
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspHobProcessLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FspHobProcessLibSample.c


################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  IntelFspPkg/IntelFspPkg.dec
  IntelFspWrapperPkg/IntelFspWrapperPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  HobLib
  DebugLib
  FspPlatformInfoLib
  PeiServicesLib
  PeiServicesTablePointerLib

[Pcd]
  gFspWrapperTokenSpaceGuid.PcdPeiMinMemSize              ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdPeiRecoveryMinMemSize      ## CONSUMES

[Guids]
  gFspReservedMemoryResourceHobGuid                       ## CONSUMES ## HOB
  gEfiMemoryTypeInformationGuid                           ## CONSUMES ## GUID
  gPcdDataBaseHobGuid                                     ## CONSUMES ## HOB

[Ppis]
  gEfiPeiCapsulePpiGuid                                   ## CONSUMES
