## @file
# FSP PEI Module
#
# This PEIM initialize FSP.
# In FSP API V1 mode, it will be invoked twice by pei core. In 1st entry, it will
# call FspInit API. In 2nd entry, it will parse the hoblist from fsp and report
# them into pei core.
# In FSP API V2 mode, it will be invoked only once. It will call FspMemoryInit API,
# register TemporaryRamDonePpi to call TempRamExit API, and register MemoryDiscoveredPpi
# notify to call FspSiliconInit API.
#
#  Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspInitPeim
  FILE_GUID                      = BC59E2E1-7492-4031-806E-C48DCCC3A026
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = FspPeiEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  FspInitPei.c
  FspInitPei.h
  FspInitPeiV1.c
  FspInitPeiV2.c
  FspNotifyS3.c
  SecMain.c
  SecMain.h
  FindPeiCore.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  IntelFspPkg/IntelFspPkg.dec
  IntelFspWrapperPkg/IntelFspWrapperPkg.dec

[LibraryClasses]
  PeimEntryPoint
  PeiServicesLib
  PeiServicesTablePointerLib
  BaseLib
  BaseMemoryLib
  DebugLib
  HobLib
  FspPlatformInfoLib
  FspHobProcessLib
  FspPlatformSecLib
  DebugAgentLib
  UefiCpuLib
  PeCoffGetEntryPointLib
  PeCoffExtraActionLib
  FspApiLib

[Ppis]
  gTopOfTemporaryRamPpiGuid             ## PRODUCES
  gFspInitDonePpiGuid                   ## PRODUCES
  gEfiEndOfPeiSignalPpiGuid             ## PRODUCES
  gEfiTemporaryRamDonePpiGuid           ## PRODUCES
  gEfiPeiMemoryDiscoveredPpiGuid        ## PRODUCES

[FixedPcd]
  gFspWrapperTokenSpaceGuid.PcdSecCoreMaxPpiSupported         ## CONSUMES

[Pcd]
  gFspWrapperTokenSpaceGuid.PcdPeiTemporaryRamStackSize       ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspBase                 ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvSecondFspBase           ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspSize                 ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdMaxUpdRegionSize               ## CONSUMES
  gFspWrapperTokenSpaceGuid.PcdFspApiVersion                  ## CONSUMES

[Depex]
  gEfiPeiMasterBootModePpiGuid
