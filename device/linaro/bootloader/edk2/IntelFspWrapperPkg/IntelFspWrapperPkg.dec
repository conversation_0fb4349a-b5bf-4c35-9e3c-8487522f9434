## @file
# Provides drivers and definitions to support fsp in EDKII bios.
#
# Copyright (c) 2014 - 2015, Intel Corporation. All rights reserved.<BR>
# This program and the accompanying materials are licensed and made available under
# the terms and conditions of the BSD License that accompanies this distribution.
# The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php.
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = IntelFspWrapperPkg
  PACKAGE_GUID                   = 99101BB6-6DE1-4537-85A3-FD6B594F7468
  PACKAGE_VERSION                = 0.1

[Includes]
  Include

[LibraryClasses]
  ##  @libraryclass  Provide FSP API related function.
  FspApiLib|Include/Library/FspApiLib.h

  ##  @libraryclass  Provide FSP hob process related function.
  FspHobProcessLib|Include/Library/FspHobProcessLib.h

  ##  @libraryclass  Provide FSP platform information related function.
  FspPlatformInfoLib|Include/Library/FspPlatformInfoLib.h

  ##  @libraryclass  Provide FSP wrapper platform sec related function.
  FspPlatformSecLib|Include/Library/FspPlatformSecLib.h

[Guids]
  #
  # GUID defined in package
  #
  gFspWrapperTokenSpaceGuid             = {0x2bc1c74a, 0x122f, 0x40b2, { 0xb2, 0x23, 0x8, 0x2b, 0x74, 0x65, 0x22, 0x5d } }

[Ppis]
  gFspInitDonePpiGuid       = { 0xf5ef05e4, 0xd538, 0x4774, { 0x8f, 0x1b, 0xe9, 0x77, 0x30, 0x11, 0xe0, 0x38 } }
  gTopOfTemporaryRamPpiGuid = { 0x2f3962b2, 0x57c5, 0x44ec, { 0x9e, 0xfc, 0xa6, 0x9f, 0xd3, 0x02, 0x03, 0x2b } }

[Protocols]

################################################################################
#
# PCD Declarations section - list of all PCDs Declared by this Package
#                            Only this package should be providing the
#                            declaration, other packages should not.
#
################################################################################
[PcdsFixedAtBuild, PcdsPatchableInModule]
  ## Provides the memory mapped base address of the BIOS CodeCache Flash Device.
  gFspWrapperTokenSpaceGuid.PcdFlashCodeCacheAddress|0xFFE00000|UINT32|0x10000001
  ## Provides the size of the BIOS Flash Device.
  gFspWrapperTokenSpaceGuid.PcdFlashCodeCacheSize|0x00200000|UINT32|0x10000002

  ## Indicates the base address of the factory FSP binary.
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspBase|0xFFF80000|UINT32|0x10000003
  ## Indicates the base address of the updatable FSP binary to support Dual FSP.
  #  There could be two FSP images at different locations in a flash - 
  #  one factory version (default) and updatable version (updatable).
  #  TempRamInit, FspMemoryInit and TempRamExit are always executed from factory version.
  #  FspSiliconInit and NotifyPhase can be executed from updatable version if it is available,
  #  FspSiliconInit and NotifyPhase are executed from factory version if there is no updateable version,
  #  PcdFlashFvFspBase is base address of factory FSP, and PcdFlashFvSecondFspBase
  #  is base address of updatable FSP. If PcdFlashFvSecondFspBase is 0, that means
  #  there is no updatable FSP.
  gFspWrapperTokenSpaceGuid.PcdFlashFvSecondFspBase|0x00000000|UINT32|0x10000008
  ## Provides the size of the factory FSP binary.
  gFspWrapperTokenSpaceGuid.PcdFlashFvFspSize|0x00048000|UINT32|0x10000004
  ## Provides the size of the updatable FSP binary to support Dual FSP.
  gFspWrapperTokenSpaceGuid.PcdFlashFvSecondFspSize|0x00000000|UINT32|0x10000009

  ## Indicates the base address of the first Microcode Patch in the Microcode Region
  gFspWrapperTokenSpaceGuid.PcdCpuMicrocodePatchAddress|0x0|UINT64|0x10000005
  gFspWrapperTokenSpaceGuid.PcdCpuMicrocodePatchRegionSize|0x0|UINT64|0x10000006
  ## Indicates the offset of the Cpu Microcode.
  gFspWrapperTokenSpaceGuid.PcdFlashMicroCodeOffset|0x90|UINT32|0x10000007

  ##
  #  Maximum number of Ppi is provided by SecCore.
  ##
  gFspWrapperTokenSpaceGuid.PcdSecCoreMaxPpiSupported|0x6|UINT32|0x20000001

  # This is MAX UPD region size
  gFspWrapperTokenSpaceGuid.PcdMaxUpdRegionSize|0x200|UINT32|0x30000001

  ## Stack size in the temporary RAM.
  #   0 means half of TemporaryRamSize.
  gFspWrapperTokenSpaceGuid.PcdPeiTemporaryRamStackSize|0|UINT32|0x40000001

  # This is temporary DRAM base and size for StackTop in FspInit
  gFspWrapperTokenSpaceGuid.PcdTemporaryRamBase|0x00080000|UINT32|0x40000002
  gFspWrapperTokenSpaceGuid.PcdTemporaryRamSize|0x00010000|UINT32|0x40000003

  ## Indicate the PEI memory size platform want to report
  gFspWrapperTokenSpaceGuid.PcdPeiMinMemSize|0x1800000|UINT32|0x40000004
  ## Indicate the PEI memory size platform want to report
  gFspWrapperTokenSpaceGuid.PcdPeiRecoveryMinMemSize|0x3000000|UINT32|0x40000005

  ## PcdFspApiVersion is to determine wrapper calling mechanism
  # - FSP_API_REVISION_1     1
  # - FSP_API_REVISION_2     2
  gFspWrapperTokenSpaceGuid.PcdFspApiVersion|0x02|UINT8|0x00001000
