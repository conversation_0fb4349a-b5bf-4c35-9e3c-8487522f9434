/** @file
  Intel FSP definition from Intel Firmware Support Package External
  Architecture Specification v2.0.

  Copyright (c) 2014 - 2016, Intel Corporation. All rights reserved.<BR>
  This program and the accompanying materials
  are licensed and made available under the terms and conditions of the BSD License
  which accompanies this distribution.  The full text of the license may be found at
  http://opensource.org/licenses/bsd-license.php.

  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.

**/

#ifndef _FSP_EAS_H_
#define _FSP_EAS_H_

#include <Uefi/UefiBaseType.h>
#include <Guid/GuidHobFspEas.h>
#include <Guid/FspHeaderFile.h>
#include <FspEas/FspApi.h>

#endif
