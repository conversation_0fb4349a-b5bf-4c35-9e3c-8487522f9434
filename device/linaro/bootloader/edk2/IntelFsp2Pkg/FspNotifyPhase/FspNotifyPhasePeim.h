/** @file
  Header file for FSP notify phase PEI module

  Copyright (c) 2016 Intel Corporation. All rights reserved.
  This program and the accompanying materials
  are licensed and made available under the terms and conditions of the BSD License
  which accompanies this distribution.  The full text of the license may be found at
  http://opensource.org/licenses/bsd-license.php.

  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
**/

#ifndef _FSP_NOTIFY_PHASE_PEIM_H_
#define _FSP_NOTIFY_PHASE_PEIM_H_

#include <Library/PeiServicesLib.h>
#include <Ppi/DxeIpl.h>
#include <Library/DebugLib.h>
#include <Library/FspPlatformLib.h>
#include <Library/FspCommonLib.h>
#include <Library/FspSwitchStackLib.h>
#endif
