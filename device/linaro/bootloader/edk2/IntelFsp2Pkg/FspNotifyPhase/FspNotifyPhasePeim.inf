## @file
# Component information file for the FSP notify phase PEI module.
#
#@copyright
#  Copyright (c) 2016 Intel Corporation. All rights reserved.
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspNotifyPhasePeim
  FILE_GUID                      = 29CBB005-C972-49F3-960F-292E2202CECD
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = FspNotifyPhasePeimEntryPoint

[Sources]
  FspNotifyPhasePeim.h
  FspNotifyPhasePeim.c

[Packages]
  MdePkg/MdePkg.dec
  IntelFsp2Pkg/IntelFsp2Pkg.dec

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  FspPlatformLib
  FspCommonLib
  FspSwitchStackLib

[Ppis]
  gEfiDxeIplPpiGuid                       ## PRODUCES
  gEfiEndOfPeiSignalPpiGuid               ## PRODUCES

[Protocols]
  gEfiPciEnumerationCompleteProtocolGuid  ## PRODUCES

[Guids]
  gEfiEventReadyToBootGuid                ## PRODUCES ## Event

[Depex]
  gEfiDxeIplPpiGuid
