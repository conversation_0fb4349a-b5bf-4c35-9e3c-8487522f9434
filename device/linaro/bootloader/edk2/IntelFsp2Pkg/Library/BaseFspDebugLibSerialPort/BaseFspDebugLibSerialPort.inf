## @file
#  Instance of BaseFspDebugLib
#
#  Copyright (c) 2014 - 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspDebugLibSerialPort
  FILE_GUID                      = CEA4FF9C-D7BC-4F07-96F1-03F41F2B17AE
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DebugLib

#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  DebugLib.c

[Sources.Ia32]
  Ia32/FspDebug.nasm

[Packages]
  MdePkg/MdePkg.dec
  IntelFsp2Pkg/IntelFsp2Pkg.dec

[LibraryClasses]
  SerialPortLib
  BaseMemoryLib
  PcdLib
  PrintLib
  BaseLib
  DebugDeviceLib
  DebugPrintErrorLevelLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugClearMemoryValue       ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask           ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdFixedDebugPrintErrorLevel   ## CONSUMES

