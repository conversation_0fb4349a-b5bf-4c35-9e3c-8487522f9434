## @file
#  Instance of BaseFspSwitchStackLib
#
#  Copyright (c) 2014 - 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFspSwitchStackLib
  FILE_GUID                      = 68E79161-F7CE-4A61-8C72-F4DF6FF35CAA
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FspSwitchStackLib

[Sources.IA32]
  FspSwitchStackLib.c

[Sources.IA32]
  Ia32/Stack.nasm

[Packages]
  MdePkg/MdePkg.dec
  IntelFsp2Pkg/IntelFsp2Pkg.dec

[LibraryClasses]
  BaseLib
  IoLib

[FixedPcd]
  gIntelFsp2PkgTokenSpaceGuid.PcdFspMaxPatchEntry      ## CONSUMES
  gIntelFsp2PkgTokenSpaceGuid.PcdFspMaxPerfEntry       ## CONSUMES



