## @file
# Provides driver and definitions to build fsp in EDKII bios.
#
# Copyright (c) 2014 - 2016, Intel Corporation. All rights reserved.<BR>
# This program and the accompanying materials are licensed and made available under
# the terms and conditions of the BSD License that accompanies this distribution.
# The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php.
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = IntelFsp2Pkg
  PACKAGE_GUID                   = A8C53B5E-D556-4F3E-874D-0D6FA2CDC7BF
  PACKAGE_VERSION                = 0.1

[Includes]
  Include
  
[LibraryClasses]
  ##  @libraryclass  Provides cache-as-ram support.
  CacheAsRamLib|Include/Library/CacheAsRamLib.h

  ##  @libraryclass  Provides cache setting on MTRR.
  CacheLib|Include/Library/CacheLib.h

  ##  @libraryclass  Provides debug device abstraction.
  DebugDeviceLib|Include/Library/DebugDeviceLib.h

  ##  @libraryclass  Provides FSP related services.
  FspCommonLib|Include/Library/FspCommonLib.h

  ##  @libraryclass  Provides FSP platform related actions.
  FspPlatformLib|Include/Library/FspPlatformLib.h

  ##  @libraryclass  Provides FSP switch stack function.
  FspSwitchStackLib|Include/Library/FspSwitchStackLib.h
  
  ##  @libraryclass  Provides FSP platform sec related actions.
  FspSecPlatformLib|Include/Library/FspSecPlatformLib.h

[Guids]
  #
  # GUID defined in package
  #
  gIntelFsp2PkgTokenSpaceGuid           = { 0xed6e0531, 0xf715, 0x4a3d, { 0x9b, 0x12, 0xc1, 0xca, 0x5e, 0xf6, 0x98, 0xa2 } }

  # Guid define in FSP EAS
  gFspHeaderFileGuid                    = { 0x912740BE, 0x2284, 0x4734, { 0xB9, 0x71, 0x84, 0xB0, 0x27, 0x35, 0x3F, 0x0C } }
  gFspReservedMemoryResourceHobGuid     = { 0x69a79759, 0x1373, 0x4367, { 0xa6, 0xc4, 0xc7, 0xf5, 0x9e, 0xfd, 0x98, 0x6e } }
  gFspNonVolatileStorageHobGuid         = { 0x721acf02, 0x4d77, 0x4c2a, { 0xb3, 0xdc, 0x27, 0x0b, 0x7b, 0xa9, 0xe4, 0xb0 } }
  gFspBootLoaderTolumHobGuid            = { 0x73ff4f56, 0xaa8e, 0x4451, { 0xb3, 0x16, 0x36, 0x35, 0x36, 0x67, 0xad, 0x44 } } # FSP EAS v1.1

  gFspPerformanceDataGuid               = { 0x56ed21b6, 0xba23, 0x429e, { 0x89, 0x32, 0x37, 0x6d, 0x8e, 0x18, 0x2e, 0xe3 } }
  gFspEventEndOfFirmwareGuid            = { 0xbd44f629, 0xeae7, 0x4198, { 0x87, 0xf1, 0x39, 0xfa, 0xb0, 0xfd, 0x71, 0x7e } }

[PcdsFixedAtBuild]
  gIntelFsp2PkgTokenSpaceGuid.PcdGlobalDataPointerAddress |0xFED00108|UINT32|0x00000001
  gIntelFsp2PkgTokenSpaceGuid.PcdTemporaryRamBase         |0xFEF00000|UINT32|0x10001001
  gIntelFsp2PkgTokenSpaceGuid.PcdTemporaryRamSize         |    0x2000|UINT32|0x10001002
  gIntelFsp2PkgTokenSpaceGuid.PcdFspTemporaryRamSize      |    0x1000|UINT32|0x10001003
  gIntelFsp2PkgTokenSpaceGuid.PcdFspReservedBufferSize    |     0x100|UINT32|0x10001004
  gIntelFsp2PkgTokenSpaceGuid.PcdFspMaxPerfEntry          |        32|UINT32|0x00002001
  gIntelFsp2PkgTokenSpaceGuid.PcdFspMaxPatchEntry         |         6|UINT32|0x00002002
  gIntelFsp2PkgTokenSpaceGuid.PcdFspAreaBaseAddress       |0xFFF80000|UINT32|0x10000001
  gIntelFsp2PkgTokenSpaceGuid.PcdFspAreaSize              |0x00040000|UINT32|0x10000002
  gIntelFsp2PkgTokenSpaceGuid.PcdFspBootFirmwareVolumeBase|0xFFF80000|UINT32|0x10000003
  gIntelFsp2PkgTokenSpaceGuid.PcdFspHeaderSpecVersion     |      0x20| UINT8|0x00000002

  # x % of FSP temporary memory will be used for heap
  # (100 - x) % of FSP temporary memory will be used for stack
  gIntelFsp2PkgTokenSpaceGuid.PcdFspHeapSizePercentage    |        50| UINT8|0x10000004
  
[PcdsFixedAtBuild,PcdsDynamic,PcdsDynamicEx]
  gIntelFsp2PkgTokenSpaceGuid.PcdFspReservedMemoryLength |0x00100000|UINT32|0x46530000
  gIntelFsp2PkgTokenSpaceGuid.PcdBootLoaderEntry         |0xFFFFFFE4|UINT32|0x46530100
