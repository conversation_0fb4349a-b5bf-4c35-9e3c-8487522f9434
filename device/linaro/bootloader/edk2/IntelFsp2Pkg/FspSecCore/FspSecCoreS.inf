## @file
#  Sec Core for FSP
#
#  Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php.
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FspSecCoreS
  FILE_GUID                      = 53AB1ACD-EDB1-4E3A-A2C7-978D721D179D
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

[Sources]
  SecFspApiChk.c

[Sources.IA32]
  Ia32/Stack.nasm
  Ia32/FspApiEntryS.nasm
  Ia32/FspApiEntryCommon.nasm
  Ia32/FspHelper.nasm

[Binaries.Ia32]
  RAW|Vtf0/Bin/ResetVec.ia32.raw |GCC

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec  
  IntelFsp2Pkg/IntelFsp2Pkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  BaseLib
  PciCf8Lib
  SerialPortLib
  FspSwitchStackLib
  FspCommonLib
  FspSecPlatformLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress           ## UNDEFINED
  gIntelFsp2PkgTokenSpaceGuid.PcdGlobalDataPointerAddress      ## CONSUMES
  gIntelFsp2PkgTokenSpaceGuid.PcdTemporaryRamBase              ## CONSUMES
  gIntelFsp2PkgTokenSpaceGuid.PcdTemporaryRamSize              ## CONSUMES
  gIntelFsp2PkgTokenSpaceGuid.PcdFspTemporaryRamSize           ## CONSUMES

[FixedPcd]
  gIntelFsp2PkgTokenSpaceGuid.PcdFspMaxPatchEntry              ## CONSUMES
  gIntelFsp2PkgTokenSpaceGuid.PcdFspMaxPerfEntry               ## CONSUMES

[Ppis]
  gEfiTemporaryRamSupportPpiGuid                              ## PRODUCES

