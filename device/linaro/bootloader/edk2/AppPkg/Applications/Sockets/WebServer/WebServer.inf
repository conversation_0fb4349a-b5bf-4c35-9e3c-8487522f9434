## @file
#  Web Server Application
#
#  Copyright (c) 2011-2012, Intel Corporation
#  All rights reserved. This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##


[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = WebServer
  FILE_GUID                      = 99E87DCF-6162-40c5-9FA1-32111F5197F7
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = ShellCEntryLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  ACPI.c
  BootServicesTable.c
  ConfigurationTable.c
  DhcpOptions.c
  DxeServicesTable.c
  Exit.c
  Firmware.c
  Handles.c
  Hello.c
  HTTP.c
  Index.c
  MemoryMap.c
  PageList.c
  Ports.c
  Reboot.c
  RuntimeServicesTable.c
  SystemTable.c
  WebServer.c

[Sources.IA32]
  Mtrr.c

[Sources.X64]
  Mtrr.c


[Pcd]
  gAppPkgTokenSpaceGuid.WebServer_HttpPort

[Packages]
  AppPkg/AppPkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ShellPkg/ShellPkg.dec
  StdLib/StdLib.dec

[Packages.IA32]
  UefiCpuPkg/UefiCpuPkg.dec

[Packages.X64]
  UefiCpuPkg/UefiCpuPkg.dec


[LibraryClasses]
  BaseMemoryLib
  BsdSocketLib
  DebugLib
  DevMedia
  DxeServicesTableLib
  EfiSocketLib
  LibC
  ShellLib
  ShellCEntryLib
  UefiBootServicesTableLib
  UefiLib
  UefiRuntimeServicesTableLib
#  UseSocketDxe

[LibraryClasses.IA32]
  MtrrLib

[LibraryClasses.X64]
  MtrrLib


[Guids]
  gEfiAcpi10TableGuid
  gEfiAcpiTableGuid
  gEfiDebugImageInfoTableGuid
  gEfiDxeServicesTableGuid
  gEfiHobListGuid
  gEfiMemoryTypeInformationGuid
  gLoadFixedAddressConfigurationTableGuid

[Protocols]
  gEfiDhcp4ServiceBindingProtocolGuid           # PROTOCOL ALWAYS_CONSUMED
  gEfiDhcp4ProtocolGuid                         # PROTOCOL ALWAYS_CONSUMED

[BuildOptions]
  INTEL:*_*_*_CC_FLAGS = /Qdiag-disable:181,186
   MSFT:*_*_*_CC_FLAGS = /Od
    GCC:*_*_*_CC_FLAGS = -O0 -Wno-unused-variable

