## @file
#  GetAddrInfo Application
#
#  Copyright (c) 2011-2012, Intel Corporation
#  All rights reserved. This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##


[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = GetAddrInfo
  FILE_GUID                      = 4C26DF71-EBE7-4dea-B5E2-0B5980433908
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = ShellCEntryLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  GetAddrInfo.c


[Packages]
  MdePkg/MdePkg.dec
  ShellPkg/ShellPkg.dec
  StdLib/StdLib.dec


[LibraryClasses]
  BaseMemoryLib
  BsdSocketLib
  DevMedia
  EfiSocketLib
  LibC
  LibMath
  LibNetUtil
  ShellCEntryLib
  UefiBootServicesTableLib
#  UseSocketDxe

[BuildOptions]
  INTEL:*_*_*_CC_FLAGS = /Qdiag-disable:181,186
   MSFT:*_*_*_CC_FLAGS = /Od
    GCC:*_*_*_CC_FLAGS = -O0 -Wno-unused-variable

