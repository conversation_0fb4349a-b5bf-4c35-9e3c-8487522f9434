## @file
#  RawIp4Tx Application
#
#  Copyright (c) 2011-2012, Intel Corporation
#  All rights reserved. This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##


[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = RawIp4Tx
  FILE_GUID                      = 3DFE0FAB-70C7-4b53-9855-985F14DB2DDA
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = ShellCEntryLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  Main.c
  RawIp4Tx.c


[Packages]
  MdePkg/MdePkg.dec
  ShellPkg/ShellPkg.dec
  StdLib/StdLib.dec


[LibraryClasses]
  BaseMemoryLib
  BsdSocketLib
  DebugLib
  EfiSocketLib
  LibC
  LibMath
  ShellCEntryLib
  UefiBootServicesTableLib
  UefiLib
#  UseSocketDxe

[BuildOptions]
  INTEL:*_*_*_CC_FLAGS = /Qdiag-disable:181,186
   MSFT:*_*_*_CC_FLAGS = /Od
    GCC:*_*_*_CC_FLAGS = -O0 -Wno-unused-variable

