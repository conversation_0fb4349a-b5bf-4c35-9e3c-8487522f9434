This directory contains a collection of executable Python scripts.

See also the Tools/scripts directory!

beer.py			Print the classic 'bottles of beer' list
eqfix.py		Fix .py files to use the correct equality test operator
fact.py			Factorize numbers
find-uname.py		Search for Unicode characters using regexps
from.py			Summarize mailbox
lpwatch.py		Watch BSD line printer queues
makedir.py		Like mkdir -p
markov.py		Markov chain simulation of words or characters
mboxconvert.py		Convert MH or MMDF mailboxes to unix mailbox format
morse.py		Produce morse code (audible or on AIFF file)
newslist.py		List all newsgroups on a NNTP server as HTML pages
pi.py			Print all digits of pi -- given enough time and memory
pp.py			Emulate some Perl command line options
primes.py		Print prime numbers
queens.py		Dijkstra's solution to Wirth's "N Queens problem"
script.py		Equivalent to BSD script(1) -- by <PERSON><PERSON> Lumholt
unbirthday.py		Print unbirthday count
update.py		Update a bunch of files according to a script.
