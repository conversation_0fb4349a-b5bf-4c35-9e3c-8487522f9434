world -- Print mappings between country names and DNS country codes.

Contact: Barry Warsaw
Email:   <EMAIL>

This script will take a list of Internet addresses and print out where in the
world those addresses originate from, based on the top-level domain country
code found in the address.  Addresses can be in any of the following forms:

    xx                -- just the country code or top-level domain identifier
    host.domain.xx    -- any Internet host or network name
    <EMAIL> -- an Internet email address

If no match is found, the address is interpreted as a regular expression [*]
and a reverse lookup is attempted.  This script will search the country names
and print a list of matching entries.  You can force reverse mappings with the
`-r' flag (see below).

For example:

    %% world tz us
    tz originated from Tanzania, United Republic of
    us originated from United States

    %% world united
    united matches 6 countries:
        ae: United Arab Emirates
        uk: United Kingdom (common practice)
        um: United States Minor Outlying Islands
        us: United States
        tz: Tanzania, United Republic of
        gb: United Kingdom


 [*] Note that regular expressions must conform to Python 1.5's re.py module
 syntax.  The comparison is done with the search() method.

Country codes are maintained by the RIPE Network Coordination Centre,
in coordination with the ISO 3166 Maintenance Agency at DIN Berlin.  The
authoritative source of counry code mappings is:

    <url:ftp://info.ripe.net/iso3166-countrycodes>

The latest known change to this information was:

    Thu Aug  7 17:59:51 MET DST 1997

This script also knows about non-geographic top-level domains.

Usage: world [-d] [-p file] [-o] [-h] addr [addr ...]

    --dump
    -d
        Print mapping of all top-level domains.

    --parse file
    -p file
        Parse an iso3166-countrycodes file extracting the two letter country
        code followed by the country name.  Note that the three letter country
        codes and numbers, which are also provided in the standard format
        file, are ignored.

    --outputdict
    -o
        When used in conjunction with the `-p' option, output is in the form
        of a Python dictionary, and country names are normalized
        w.r.t. capitalization.  This makes it appropriate for cutting and
        pasting back into this file.

    --reverse
    -r
        Force reverse lookup.  In this mode the address can be any Python
        regular expression; this is matched against all country names and a
        list of matching mappings is printed.  In normal mode (e.g. without
        this flag), reverse lookup is performed on addresses if no matching
        country code is found.

    -h
    --help
        Print this message.


Local Variables:
indent-tabs-mode: nil
End:
