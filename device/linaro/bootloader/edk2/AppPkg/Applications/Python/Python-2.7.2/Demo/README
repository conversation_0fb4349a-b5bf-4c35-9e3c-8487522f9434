This directory contains various demonstrations of what you can do with
Python.  They were all written by me except where explicitly stated
otherwise -- in general, demos contributed by others ends up in the
../Contrib directory, unless I think they're of utmost general
importance (like <PERSON>'s Tk demos).

A fair number of utilities that are useful when while developing
Python code can be found in the ../Tools directory -- some of these
can also be considered good examples of how to write Python code.

Finally, in order to save disk space and net bandwidth, not all
subdirectories listed here are distributed.  They are listed just
in case I change my mind about them.


cgi             CGI examples (see also ../Tools/faqwiz/.)

classes		Some examples of how to use classes.

comparisons	A set of responses to a really old language-comparison
		challenge.

md5test		Test program for the optional md5 module.

metaclasses	The code from the 1.5 metaclasses paper on the web.

parser		Example using the parser module.

pdist		Old, unfinished code messing with CVS, RCS and remote
		files.

scripts		Some useful Python scripts that I put in my bin
		directory.  No optional built-in modules needed.

sockets		Examples for the new built-in module 'socket'.

xml		Some XML demos.

zlib		Some demos for the zlib module (see also the standard
		library module gzip.py).
