This directory contains a collection of executable Python scripts that
are useful while building, extending or managing Python.  Some (e.g.,
dutree or lll) are also generally useful UNIX tools.

See also the Demo/scripts directory!

analyze_dxp.py		Analyzes the result of sys.getdxp()
byext.py		Print lines/words/chars stats of files by extension
byteyears.py		Print product of a file's size and age
checkappend.py		Search for multi-argument .append() calls
checkpyc.py		Check presence and validity of ".pyc" files
classfix.py		Convert old class syntax to new
cleanfuture.py		Fix reduntant Python __future__ statements
combinerefs.py		A helper for analyzing PYTHONDUMPREFS output.
copytime.py		Copy one file's atime and mtime to another
crlf.py			Change CRLF line endings to LF (Windows to Unix)
cvsfiles.py		Print a list of files that are under CVS
db2pickle.py		Dump a database file to a pickle
diff.py			Print file diffs in context, unified, or ndiff formats
dutree.py		Format du(1) output as a tree sorted by size
eptags.py		Create Emacs TAGS file for Python modules
find_recursionlimit.py  Find the maximum recursion limit on this machine 
finddiv.py		A grep-like tool that looks for division operators
findlinksto.py		Recursively find symbolic links to a given path prefix
findnocoding.py		Find source files which need an encoding declaration
fixcid.py		Massive identifier substitution on C source files
fixdiv.py		Tool to fix division operators.
fixheader.py		Add some cpp magic to a C include file
fixnotice.py		Fix the copyright notice in source files
fixps.py		Fix Python scripts' first line (if #!)
ftpmirror.py		FTP mirror script
google.py		Open a webbrowser with Google
gprof2html.py		Transform gprof(1) output into useful HTML
h2py.py			Translate #define's into Python assignments
hotshotmain.py		Main program to run script under control of hotshot
idle			Main program to start IDLE
ifdef.py		Remove #if(n)def groups from C sources
lfcr.py			Change LF line endings to CRLF (Unix to Windows)
linktree.py		Make a copy of a tree with links to original files
lll.py			Find and list symbolic links in current directory
logmerge.py		Consolidate CVS/RCS logs read from stdin
mailerdaemon.py		parse error messages from mailer daemons (Sjoerd&Jack)
md5sum.py		Print MD5 checksums of argument files.
methfix.py		Fix old method syntax def f(self, (a1, ..., aN)):
mkreal.py		Turn a symbolic link into a real file or directory
ndiff.py		Intelligent diff between text files (Tim Peters)
nm2def.py		Create a template for PC/python_nt.def (Marc Lemburg)
objgraph.py		Print object graph from nm output on a library
parseentities.py	Utility for parsing HTML entity definitions
pathfix.py		Change #!/usr/local/bin/python into something else
pdeps.py		Print dependencies between Python modules
pickle2db.py		Load a pickle generated by db2pickle.py to a database
pindent.py		Indent Python code, giving block-closing comments
ptags.py		Create vi tags file for Python modules
pydoc			Python documentation browser.
pysource.py		Find Python source files
redemo.py		Basic regular expression demonstration facility
reindent.py		Change .py files to use 4-space indents.
rgrep.py		Reverse grep through a file (useful for big logfiles)
serve.py		Small wsgiref-based web server, used in make serve in Doc
setup.py		Install all scripts listed here
suff.py			Sort a list of files by suffix
svneol.py		Sets svn:eol-style on all files in directory
texcheck.py             Validate Python LaTeX formatting (Raymond Hettinger)
texi2html.py		Convert GNU texinfo files into HTML
treesync.py		Synchronize source trees (very ideosyncratic)
untabify.py		Replace tabs with spaces in argument files
which.py		Find a program in $PATH
xxci.py			Wrapper for rcsdiff and ci
