Python（派森）语言是一种功能强大而完善的通用型计算机程序设计语言，
已经具有十多年的发展历史，成熟且稳定。这种语言具有非常简捷而清晰
的语法特点，适合完成各种高层任务，几乎可以在所有的操作系统中
运行。这种语言简单而强大，适合各种人士学习使用。目前，基于这
种语言的相关技术正在飞速的发展，用户数量急剧扩大，相关的资源非常多。
如何在 Python 中使用既有的 C library?
　在資訊科技快速發展的今天, 開發及測試軟體的速度是不容忽視的
課題. 為加快開發及測試的速度, 我們便常希望能利用一些已開發好的
library, 並有一個 fast prototyping 的 programming language 可
供使用. 目前有許許多多的 library 是以 C 寫成, 而 Python 是一個
fast prototyping 的 programming language. 故我們希望能將既有的
C library 拿到 Python 的環境中測試及整合. 其中最主要也是我們所
要討論的問題就是:

