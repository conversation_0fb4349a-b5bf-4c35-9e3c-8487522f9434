Webchecker
----------

This is a simple web tree checker, useful to find bad links in a web
tree.  It currently checks links pointing within the same subweb for
validity.  The main program is "webchecker.py".  See its doc string
(or invoke it with the option "-?") for more defails.

History:

- Jan 1997.  First release.  The module robotparser.py was written by
<PERSON><PERSON>; the rest is original work by <PERSON>.

- May 1999.  <PERSON> contributed a new version, wcnew.py, which
supports checking internal links (#spam fragments in URLs) and some
other options.

- Nov 1999.  <PERSON> contributed patches to reintegrate wcnew.py
into webchecker.py, and corresponding mods to wcgui.py and
websucker.py.

- Mar 2004.  <PERSON> contributed a patch to let webchecker.py
handle XHTML's 'id' attribute.
