#!/usr/bin/env python
# -*- python -*-
#
# guido's version, from rcsbump,v 1.2 1995/06/22 21:27:27 bwarsaw Exp
#
# Python script for bumping up an RCS major revision number.

import sys
import re
import rcslib
import string

WITHLOCK = 1
majorrev_re = re.compile('^[0-9]+')

dir = rcslib.RCS()

if sys.argv[1:]:
    files = sys.argv[1:]
else:
    files = dir.listfiles()

for file in files:
    # get the major revnumber of the file
    headbranch = dir.info(file)['head']
    majorrev_re.match(headbranch)
    majorrev = string.atoi(majorrev_re.group(0)) + 1

    if not dir.islocked(file):
        dir.checkout(file, WITHLOCK)

    msg = "Bumping major revision number (to %d)" % majorrev
    dir.checkin((file, "%s.0" % majorrev), msg, "-f")
