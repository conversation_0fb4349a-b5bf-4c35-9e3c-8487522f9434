This directory contains a number of Python programs that are useful
while building or extending Python.

bgen		Generate complete extension modules from a
		description.  Still under development!
		WARNING: bgen has been removed in 3.0.

compiler	Tools used to maintain the compiler package in the
		standard library.

faqwiz		FAQ Wizard.
		See http://www.python.org/cgi-bin/faqw.py
		for a live example.

freeze		Create a stand-alone executable from a Python program.

gdb             Python code to be run inside gdb, to make it easier to
                debug Python itself (by <PERSON>).

i18n		Tools for internationalization. pygettext.py 
		parses Python source code and generates .pot files,
		and msgfmt.py generates a binary message catalog 
		from a catalog in text format.

scripts         A number of useful single-file programs, e.g. tabnanny.py
                by <PERSON>, which checks for inconsistent mixing of
                tabs and spaces, and 2to3, which converts Python 2 code
                to Python 3 code.

unicode		Tools used to generate unicode database files for
		Python 2.0 (by <PERSON><PERSON>).

versioncheck	A tool to automate checking whether you have the latest
		version of a package (by <PERSON>).

webchecker	A link checker for web sites.

world		Script to take a list of Internet addresses and print
		out where in the world those addresses originate from,
		based on the top-level domain country code found in
		the address. 
