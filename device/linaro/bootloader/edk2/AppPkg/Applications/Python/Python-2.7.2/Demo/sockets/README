This directory contains some demonstrations of the socket module:

broadcast.py	 	Broadcast the time to radio.py.
echosvr.py		About the simplest TCP server possible.
finger.py		Client for the 'finger' protocol.
ftp.py			A very simple ftp client.
gopher.py		A simple gopher client.
mcast.py		IPv4/v6 multicast example
radio.py		Receive time broadcasts from broadcast.py.
telnet.py		Client for the 'telnet' protocol.
throughput.py		Client and server to measure TCP throughput.
unixclient.py		Unix socket example, client side
unixserver.py		Unix socket example, server side
udpecho.py		Client and server for the UDP echo protocol.
