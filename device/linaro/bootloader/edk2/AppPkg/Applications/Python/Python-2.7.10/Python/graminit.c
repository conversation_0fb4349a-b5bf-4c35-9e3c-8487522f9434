/* Generated by <PERSON>rser/pgen */

#include "pgenheaders.h"
#include "grammar.h"
PyAPI_DATA(grammar) _PyParser_Grammar;
static arc arcs_0_0[3] = {
    {2, 1},
    {3, 1},
    {4, 2},
};
static arc arcs_0_1[1] = {
    {0, 1},
};
static arc arcs_0_2[1] = {
    {2, 1},
};
static state states_0[3] = {
    {3, arcs_0_0},
    {1, arcs_0_1},
    {1, arcs_0_2},
};
static arc arcs_1_0[3] = {
    {2, 0},
    {6, 0},
    {7, 1},
};
static arc arcs_1_1[1] = {
    {0, 1},
};
static state states_1[2] = {
    {3, arcs_1_0},
    {1, arcs_1_1},
};
static arc arcs_2_0[1] = {
    {9, 1},
};
static arc arcs_2_1[2] = {
    {2, 1},
    {7, 2},
};
static arc arcs_2_2[1] = {
    {0, 2},
};
static state states_2[3] = {
    {1, arcs_2_0},
    {2, arcs_2_1},
    {1, arcs_2_2},
};
static arc arcs_3_0[1] = {
    {11, 1},
};
static arc arcs_3_1[1] = {
    {12, 2},
};
static arc arcs_3_2[2] = {
    {13, 3},
    {2, 4},
};
static arc arcs_3_3[2] = {
    {14, 5},
    {15, 6},
};
static arc arcs_3_4[1] = {
    {0, 4},
};
static arc arcs_3_5[1] = {
    {15, 6},
};
static arc arcs_3_6[1] = {
    {2, 4},
};
static state states_3[7] = {
    {1, arcs_3_0},
    {1, arcs_3_1},
    {2, arcs_3_2},
    {2, arcs_3_3},
    {1, arcs_3_4},
    {1, arcs_3_5},
    {1, arcs_3_6},
};
static arc arcs_4_0[1] = {
    {10, 1},
};
static arc arcs_4_1[2] = {
    {10, 1},
    {0, 1},
};
static state states_4[2] = {
    {1, arcs_4_0},
    {2, arcs_4_1},
};
static arc arcs_5_0[1] = {
    {16, 1},
};
static arc arcs_5_1[2] = {
    {18, 2},
    {19, 2},
};
static arc arcs_5_2[1] = {
    {0, 2},
};
static state states_5[3] = {
    {1, arcs_5_0},
    {2, arcs_5_1},
    {1, arcs_5_2},
};
static arc arcs_6_0[1] = {
    {20, 1},
};
static arc arcs_6_1[1] = {
    {21, 2},
};
static arc arcs_6_2[1] = {
    {22, 3},
};
static arc arcs_6_3[1] = {
    {23, 4},
};
static arc arcs_6_4[1] = {
    {24, 5},
};
static arc arcs_6_5[1] = {
    {0, 5},
};
static state states_6[6] = {
    {1, arcs_6_0},
    {1, arcs_6_1},
    {1, arcs_6_2},
    {1, arcs_6_3},
    {1, arcs_6_4},
    {1, arcs_6_5},
};
static arc arcs_7_0[1] = {
    {13, 1},
};
static arc arcs_7_1[2] = {
    {25, 2},
    {15, 3},
};
static arc arcs_7_2[1] = {
    {15, 3},
};
static arc arcs_7_3[1] = {
    {0, 3},
};
static state states_7[4] = {
    {1, arcs_7_0},
    {2, arcs_7_1},
    {1, arcs_7_2},
    {1, arcs_7_3},
};
static arc arcs_8_0[3] = {
    {26, 1},
    {30, 2},
    {31, 3},
};
static arc arcs_8_1[3] = {
    {27, 4},
    {29, 5},
    {0, 1},
};
static arc arcs_8_2[1] = {
    {21, 6},
};
static arc arcs_8_3[1] = {
    {21, 7},
};
static arc arcs_8_4[1] = {
    {28, 8},
};
static arc arcs_8_5[4] = {
    {26, 1},
    {30, 2},
    {31, 3},
    {0, 5},
};
static arc arcs_8_6[2] = {
    {29, 9},
    {0, 6},
};
static arc arcs_8_7[1] = {
    {0, 7},
};
static arc arcs_8_8[2] = {
    {29, 5},
    {0, 8},
};
static arc arcs_8_9[1] = {
    {31, 3},
};
static state states_8[10] = {
    {3, arcs_8_0},
    {3, arcs_8_1},
    {1, arcs_8_2},
    {1, arcs_8_3},
    {1, arcs_8_4},
    {4, arcs_8_5},
    {2, arcs_8_6},
    {1, arcs_8_7},
    {2, arcs_8_8},
    {1, arcs_8_9},
};
static arc arcs_9_0[2] = {
    {21, 1},
    {13, 2},
};
static arc arcs_9_1[1] = {
    {0, 1},
};
static arc arcs_9_2[1] = {
    {32, 3},
};
static arc arcs_9_3[1] = {
    {15, 1},
};
static state states_9[4] = {
    {2, arcs_9_0},
    {1, arcs_9_1},
    {1, arcs_9_2},
    {1, arcs_9_3},
};
static arc arcs_10_0[1] = {
    {26, 1},
};
static arc arcs_10_1[2] = {
    {29, 2},
    {0, 1},
};
static arc arcs_10_2[2] = {
    {26, 1},
    {0, 2},
};
static state states_10[3] = {
    {1, arcs_10_0},
    {2, arcs_10_1},
    {2, arcs_10_2},
};
static arc arcs_11_0[2] = {
    {3, 1},
    {4, 1},
};
static arc arcs_11_1[1] = {
    {0, 1},
};
static state states_11[2] = {
    {2, arcs_11_0},
    {1, arcs_11_1},
};
static arc arcs_12_0[1] = {
    {33, 1},
};
static arc arcs_12_1[2] = {
    {34, 2},
    {2, 3},
};
static arc arcs_12_2[2] = {
    {33, 1},
    {2, 3},
};
static arc arcs_12_3[1] = {
    {0, 3},
};
static state states_12[4] = {
    {1, arcs_12_0},
    {2, arcs_12_1},
    {2, arcs_12_2},
    {1, arcs_12_3},
};
static arc arcs_13_0[9] = {
    {35, 1},
    {36, 1},
    {37, 1},
    {38, 1},
    {39, 1},
    {40, 1},
    {41, 1},
    {42, 1},
    {43, 1},
};
static arc arcs_13_1[1] = {
    {0, 1},
};
static state states_13[2] = {
    {9, arcs_13_0},
    {1, arcs_13_1},
};
static arc arcs_14_0[1] = {
    {9, 1},
};
static arc arcs_14_1[3] = {
    {44, 2},
    {27, 3},
    {0, 1},
};
static arc arcs_14_2[2] = {
    {45, 4},
    {9, 4},
};
static arc arcs_14_3[2] = {
    {45, 5},
    {9, 5},
};
static arc arcs_14_4[1] = {
    {0, 4},
};
static arc arcs_14_5[2] = {
    {27, 3},
    {0, 5},
};
static state states_14[6] = {
    {1, arcs_14_0},
    {3, arcs_14_1},
    {2, arcs_14_2},
    {2, arcs_14_3},
    {1, arcs_14_4},
    {2, arcs_14_5},
};
static arc arcs_15_0[12] = {
    {46, 1},
    {47, 1},
    {48, 1},
    {49, 1},
    {50, 1},
    {51, 1},
    {52, 1},
    {53, 1},
    {54, 1},
    {55, 1},
    {56, 1},
    {57, 1},
};
static arc arcs_15_1[1] = {
    {0, 1},
};
static state states_15[2] = {
    {12, arcs_15_0},
    {1, arcs_15_1},
};
static arc arcs_16_0[1] = {
    {58, 1},
};
static arc arcs_16_1[3] = {
    {28, 2},
    {59, 3},
    {0, 1},
};
static arc arcs_16_2[2] = {
    {29, 4},
    {0, 2},
};
static arc arcs_16_3[1] = {
    {28, 5},
};
static arc arcs_16_4[2] = {
    {28, 2},
    {0, 4},
};
static arc arcs_16_5[2] = {
    {29, 6},
    {0, 5},
};
static arc arcs_16_6[1] = {
    {28, 7},
};
static arc arcs_16_7[2] = {
    {29, 8},
    {0, 7},
};
static arc arcs_16_8[2] = {
    {28, 7},
    {0, 8},
};
static state states_16[9] = {
    {1, arcs_16_0},
    {3, arcs_16_1},
    {2, arcs_16_2},
    {1, arcs_16_3},
    {2, arcs_16_4},
    {2, arcs_16_5},
    {1, arcs_16_6},
    {2, arcs_16_7},
    {2, arcs_16_8},
};
static arc arcs_17_0[1] = {
    {60, 1},
};
static arc arcs_17_1[1] = {
    {61, 2},
};
static arc arcs_17_2[1] = {
    {0, 2},
};
static state states_17[3] = {
    {1, arcs_17_0},
    {1, arcs_17_1},
    {1, arcs_17_2},
};
static arc arcs_18_0[1] = {
    {62, 1},
};
static arc arcs_18_1[1] = {
    {0, 1},
};
static state states_18[2] = {
    {1, arcs_18_0},
    {1, arcs_18_1},
};
static arc arcs_19_0[5] = {
    {63, 1},
    {64, 1},
    {65, 1},
    {66, 1},
    {67, 1},
};
static arc arcs_19_1[1] = {
    {0, 1},
};
static state states_19[2] = {
    {5, arcs_19_0},
    {1, arcs_19_1},
};
static arc arcs_20_0[1] = {
    {68, 1},
};
static arc arcs_20_1[1] = {
    {0, 1},
};
static state states_20[2] = {
    {1, arcs_20_0},
    {1, arcs_20_1},
};
static arc arcs_21_0[1] = {
    {69, 1},
};
static arc arcs_21_1[1] = {
    {0, 1},
};
static state states_21[2] = {
    {1, arcs_21_0},
    {1, arcs_21_1},
};
static arc arcs_22_0[1] = {
    {70, 1},
};
static arc arcs_22_1[2] = {
    {9, 2},
    {0, 1},
};
static arc arcs_22_2[1] = {
    {0, 2},
};
static state states_22[3] = {
    {1, arcs_22_0},
    {2, arcs_22_1},
    {1, arcs_22_2},
};
static arc arcs_23_0[1] = {
    {45, 1},
};
static arc arcs_23_1[1] = {
    {0, 1},
};
static state states_23[2] = {
    {1, arcs_23_0},
    {1, arcs_23_1},
};
static arc arcs_24_0[1] = {
    {71, 1},
};
static arc arcs_24_1[2] = {
    {28, 2},
    {0, 1},
};
static arc arcs_24_2[2] = {
    {29, 3},
    {0, 2},
};
static arc arcs_24_3[1] = {
    {28, 4},
};
static arc arcs_24_4[2] = {
    {29, 5},
    {0, 4},
};
static arc arcs_24_5[1] = {
    {28, 6},
};
static arc arcs_24_6[1] = {
    {0, 6},
};
static state states_24[7] = {
    {1, arcs_24_0},
    {2, arcs_24_1},
    {2, arcs_24_2},
    {1, arcs_24_3},
    {2, arcs_24_4},
    {1, arcs_24_5},
    {1, arcs_24_6},
};
static arc arcs_25_0[2] = {
    {72, 1},
    {73, 1},
};
static arc arcs_25_1[1] = {
    {0, 1},
};
static state states_25[2] = {
    {2, arcs_25_0},
    {1, arcs_25_1},
};
static arc arcs_26_0[1] = {
    {74, 1},
};
static arc arcs_26_1[1] = {
    {75, 2},
};
static arc arcs_26_2[1] = {
    {0, 2},
};
static state states_26[3] = {
    {1, arcs_26_0},
    {1, arcs_26_1},
    {1, arcs_26_2},
};
static arc arcs_27_0[1] = {
    {76, 1},
};
static arc arcs_27_1[2] = {
    {77, 2},
    {12, 3},
};
static arc arcs_27_2[3] = {
    {77, 2},
    {12, 3},
    {74, 4},
};
static arc arcs_27_3[1] = {
    {74, 4},
};
static arc arcs_27_4[3] = {
    {30, 5},
    {13, 6},
    {78, 5},
};
static arc arcs_27_5[1] = {
    {0, 5},
};
static arc arcs_27_6[1] = {
    {78, 7},
};
static arc arcs_27_7[1] = {
    {15, 5},
};
static state states_27[8] = {
    {1, arcs_27_0},
    {2, arcs_27_1},
    {3, arcs_27_2},
    {1, arcs_27_3},
    {3, arcs_27_4},
    {1, arcs_27_5},
    {1, arcs_27_6},
    {1, arcs_27_7},
};
static arc arcs_28_0[1] = {
    {21, 1},
};
static arc arcs_28_1[2] = {
    {80, 2},
    {0, 1},
};
static arc arcs_28_2[1] = {
    {21, 3},
};
static arc arcs_28_3[1] = {
    {0, 3},
};
static state states_28[4] = {
    {1, arcs_28_0},
    {2, arcs_28_1},
    {1, arcs_28_2},
    {1, arcs_28_3},
};
static arc arcs_29_0[1] = {
    {12, 1},
};
static arc arcs_29_1[2] = {
    {80, 2},
    {0, 1},
};
static arc arcs_29_2[1] = {
    {21, 3},
};
static arc arcs_29_3[1] = {
    {0, 3},
};
static state states_29[4] = {
    {1, arcs_29_0},
    {2, arcs_29_1},
    {1, arcs_29_2},
    {1, arcs_29_3},
};
static arc arcs_30_0[1] = {
    {79, 1},
};
static arc arcs_30_1[2] = {
    {29, 2},
    {0, 1},
};
static arc arcs_30_2[2] = {
    {79, 1},
    {0, 2},
};
static state states_30[3] = {
    {1, arcs_30_0},
    {2, arcs_30_1},
    {2, arcs_30_2},
};
static arc arcs_31_0[1] = {
    {81, 1},
};
static arc arcs_31_1[2] = {
    {29, 0},
    {0, 1},
};
static state states_31[2] = {
    {1, arcs_31_0},
    {2, arcs_31_1},
};
static arc arcs_32_0[1] = {
    {21, 1},
};
static arc arcs_32_1[2] = {
    {77, 0},
    {0, 1},
};
static state states_32[2] = {
    {1, arcs_32_0},
    {2, arcs_32_1},
};
static arc arcs_33_0[1] = {
    {82, 1},
};
static arc arcs_33_1[1] = {
    {21, 2},
};
static arc arcs_33_2[2] = {
    {29, 1},
    {0, 2},
};
static state states_33[3] = {
    {1, arcs_33_0},
    {1, arcs_33_1},
    {2, arcs_33_2},
};
static arc arcs_34_0[1] = {
    {83, 1},
};
static arc arcs_34_1[1] = {
    {84, 2},
};
static arc arcs_34_2[2] = {
    {85, 3},
    {0, 2},
};
static arc arcs_34_3[1] = {
    {28, 4},
};
static arc arcs_34_4[2] = {
    {29, 5},
    {0, 4},
};
static arc arcs_34_5[1] = {
    {28, 6},
};
static arc arcs_34_6[1] = {
    {0, 6},
};
static state states_34[7] = {
    {1, arcs_34_0},
    {1, arcs_34_1},
    {2, arcs_34_2},
    {1, arcs_34_3},
    {2, arcs_34_4},
    {1, arcs_34_5},
    {1, arcs_34_6},
};
static arc arcs_35_0[1] = {
    {86, 1},
};
static arc arcs_35_1[1] = {
    {28, 2},
};
static arc arcs_35_2[2] = {
    {29, 3},
    {0, 2},
};
static arc arcs_35_3[1] = {
    {28, 4},
};
static arc arcs_35_4[1] = {
    {0, 4},
};
static state states_35[5] = {
    {1, arcs_35_0},
    {1, arcs_35_1},
    {2, arcs_35_2},
    {1, arcs_35_3},
    {1, arcs_35_4},
};
static arc arcs_36_0[8] = {
    {87, 1},
    {88, 1},
    {89, 1},
    {90, 1},
    {91, 1},
    {19, 1},
    {18, 1},
    {17, 1},
};
static arc arcs_36_1[1] = {
    {0, 1},
};
static state states_36[2] = {
    {8, arcs_36_0},
    {1, arcs_36_1},
};
static arc arcs_37_0[1] = {
    {92, 1},
};
static arc arcs_37_1[1] = {
    {28, 2},
};
static arc arcs_37_2[1] = {
    {23, 3},
};
static arc arcs_37_3[1] = {
    {24, 4},
};
static arc arcs_37_4[3] = {
    {93, 1},
    {94, 5},
    {0, 4},
};
static arc arcs_37_5[1] = {
    {23, 6},
};
static arc arcs_37_6[1] = {
    {24, 7},
};
static arc arcs_37_7[1] = {
    {0, 7},
};
static state states_37[8] = {
    {1, arcs_37_0},
    {1, arcs_37_1},
    {1, arcs_37_2},
    {1, arcs_37_3},
    {3, arcs_37_4},
    {1, arcs_37_5},
    {1, arcs_37_6},
    {1, arcs_37_7},
};
static arc arcs_38_0[1] = {
    {95, 1},
};
static arc arcs_38_1[1] = {
    {28, 2},
};
static arc arcs_38_2[1] = {
    {23, 3},
};
static arc arcs_38_3[1] = {
    {24, 4},
};
static arc arcs_38_4[2] = {
    {94, 5},
    {0, 4},
};
static arc arcs_38_5[1] = {
    {23, 6},
};
static arc arcs_38_6[1] = {
    {24, 7},
};
static arc arcs_38_7[1] = {
    {0, 7},
};
static state states_38[8] = {
    {1, arcs_38_0},
    {1, arcs_38_1},
    {1, arcs_38_2},
    {1, arcs_38_3},
    {2, arcs_38_4},
    {1, arcs_38_5},
    {1, arcs_38_6},
    {1, arcs_38_7},
};
static arc arcs_39_0[1] = {
    {96, 1},
};
static arc arcs_39_1[1] = {
    {61, 2},
};
static arc arcs_39_2[1] = {
    {85, 3},
};
static arc arcs_39_3[1] = {
    {9, 4},
};
static arc arcs_39_4[1] = {
    {23, 5},
};
static arc arcs_39_5[1] = {
    {24, 6},
};
static arc arcs_39_6[2] = {
    {94, 7},
    {0, 6},
};
static arc arcs_39_7[1] = {
    {23, 8},
};
static arc arcs_39_8[1] = {
    {24, 9},
};
static arc arcs_39_9[1] = {
    {0, 9},
};
static state states_39[10] = {
    {1, arcs_39_0},
    {1, arcs_39_1},
    {1, arcs_39_2},
    {1, arcs_39_3},
    {1, arcs_39_4},
    {1, arcs_39_5},
    {2, arcs_39_6},
    {1, arcs_39_7},
    {1, arcs_39_8},
    {1, arcs_39_9},
};
static arc arcs_40_0[1] = {
    {97, 1},
};
static arc arcs_40_1[1] = {
    {23, 2},
};
static arc arcs_40_2[1] = {
    {24, 3},
};
static arc arcs_40_3[2] = {
    {98, 4},
    {99, 5},
};
static arc arcs_40_4[1] = {
    {23, 6},
};
static arc arcs_40_5[1] = {
    {23, 7},
};
static arc arcs_40_6[1] = {
    {24, 8},
};
static arc arcs_40_7[1] = {
    {24, 9},
};
static arc arcs_40_8[4] = {
    {98, 4},
    {94, 10},
    {99, 5},
    {0, 8},
};
static arc arcs_40_9[1] = {
    {0, 9},
};
static arc arcs_40_10[1] = {
    {23, 11},
};
static arc arcs_40_11[1] = {
    {24, 12},
};
static arc arcs_40_12[2] = {
    {99, 5},
    {0, 12},
};
static state states_40[13] = {
    {1, arcs_40_0},
    {1, arcs_40_1},
    {1, arcs_40_2},
    {2, arcs_40_3},
    {1, arcs_40_4},
    {1, arcs_40_5},
    {1, arcs_40_6},
    {1, arcs_40_7},
    {4, arcs_40_8},
    {1, arcs_40_9},
    {1, arcs_40_10},
    {1, arcs_40_11},
    {2, arcs_40_12},
};
static arc arcs_41_0[1] = {
    {100, 1},
};
static arc arcs_41_1[1] = {
    {101, 2},
};
static arc arcs_41_2[2] = {
    {29, 1},
    {23, 3},
};
static arc arcs_41_3[1] = {
    {24, 4},
};
static arc arcs_41_4[1] = {
    {0, 4},
};
static state states_41[5] = {
    {1, arcs_41_0},
    {1, arcs_41_1},
    {2, arcs_41_2},
    {1, arcs_41_3},
    {1, arcs_41_4},
};
static arc arcs_42_0[1] = {
    {28, 1},
};
static arc arcs_42_1[2] = {
    {80, 2},
    {0, 1},
};
static arc arcs_42_2[1] = {
    {84, 3},
};
static arc arcs_42_3[1] = {
    {0, 3},
};
static state states_42[4] = {
    {1, arcs_42_0},
    {2, arcs_42_1},
    {1, arcs_42_2},
    {1, arcs_42_3},
};
static arc arcs_43_0[1] = {
    {102, 1},
};
static arc arcs_43_1[2] = {
    {28, 2},
    {0, 1},
};
static arc arcs_43_2[3] = {
    {80, 3},
    {29, 3},
    {0, 2},
};
static arc arcs_43_3[1] = {
    {28, 4},
};
static arc arcs_43_4[1] = {
    {0, 4},
};
static state states_43[5] = {
    {1, arcs_43_0},
    {2, arcs_43_1},
    {3, arcs_43_2},
    {1, arcs_43_3},
    {1, arcs_43_4},
};
static arc arcs_44_0[2] = {
    {3, 1},
    {2, 2},
};
static arc arcs_44_1[1] = {
    {0, 1},
};
static arc arcs_44_2[1] = {
    {103, 3},
};
static arc arcs_44_3[1] = {
    {6, 4},
};
static arc arcs_44_4[2] = {
    {6, 4},
    {104, 1},
};
static state states_44[5] = {
    {2, arcs_44_0},
    {1, arcs_44_1},
    {1, arcs_44_2},
    {1, arcs_44_3},
    {2, arcs_44_4},
};
static arc arcs_45_0[1] = {
    {106, 1},
};
static arc arcs_45_1[2] = {
    {29, 2},
    {0, 1},
};
static arc arcs_45_2[1] = {
    {106, 3},
};
static arc arcs_45_3[2] = {
    {29, 4},
    {0, 3},
};
static arc arcs_45_4[2] = {
    {106, 3},
    {0, 4},
};
static state states_45[5] = {
    {1, arcs_45_0},
    {2, arcs_45_1},
    {1, arcs_45_2},
    {2, arcs_45_3},
    {2, arcs_45_4},
};
static arc arcs_46_0[2] = {
    {107, 1},
    {108, 1},
};
static arc arcs_46_1[1] = {
    {0, 1},
};
static state states_46[2] = {
    {2, arcs_46_0},
    {1, arcs_46_1},
};
static arc arcs_47_0[1] = {
    {109, 1},
};
static arc arcs_47_1[2] = {
    {25, 2},
    {23, 3},
};
static arc arcs_47_2[1] = {
    {23, 3},
};
static arc arcs_47_3[1] = {
    {106, 4},
};
static arc arcs_47_4[1] = {
    {0, 4},
};
static state states_47[5] = {
    {1, arcs_47_0},
    {2, arcs_47_1},
    {1, arcs_47_2},
    {1, arcs_47_3},
    {1, arcs_47_4},
};
static arc arcs_48_0[2] = {
    {107, 1},
    {110, 2},
};
static arc arcs_48_1[2] = {
    {92, 3},
    {0, 1},
};
static arc arcs_48_2[1] = {
    {0, 2},
};
static arc arcs_48_3[1] = {
    {107, 4},
};
static arc arcs_48_4[1] = {
    {94, 5},
};
static arc arcs_48_5[1] = {
    {28, 2},
};
static state states_48[6] = {
    {2, arcs_48_0},
    {2, arcs_48_1},
    {1, arcs_48_2},
    {1, arcs_48_3},
    {1, arcs_48_4},
    {1, arcs_48_5},
};
static arc arcs_49_0[1] = {
    {111, 1},
};
static arc arcs_49_1[2] = {
    {112, 0},
    {0, 1},
};
static state states_49[2] = {
    {1, arcs_49_0},
    {2, arcs_49_1},
};
static arc arcs_50_0[1] = {
    {113, 1},
};
static arc arcs_50_1[2] = {
    {114, 0},
    {0, 1},
};
static state states_50[2] = {
    {1, arcs_50_0},
    {2, arcs_50_1},
};
static arc arcs_51_0[2] = {
    {115, 1},
    {116, 2},
};
static arc arcs_51_1[1] = {
    {113, 2},
};
static arc arcs_51_2[1] = {
    {0, 2},
};
static state states_51[3] = {
    {2, arcs_51_0},
    {1, arcs_51_1},
    {1, arcs_51_2},
};
static arc arcs_52_0[1] = {
    {84, 1},
};
static arc arcs_52_1[2] = {
    {117, 0},
    {0, 1},
};
static state states_52[2] = {
    {1, arcs_52_0},
    {2, arcs_52_1},
};
static arc arcs_53_0[10] = {
    {118, 1},
    {119, 1},
    {120, 1},
    {121, 1},
    {122, 1},
    {123, 1},
    {124, 1},
    {85, 1},
    {115, 2},
    {125, 3},
};
static arc arcs_53_1[1] = {
    {0, 1},
};
static arc arcs_53_2[1] = {
    {85, 1},
};
static arc arcs_53_3[2] = {
    {115, 1},
    {0, 3},
};
static state states_53[4] = {
    {10, arcs_53_0},
    {1, arcs_53_1},
    {1, arcs_53_2},
    {2, arcs_53_3},
};
static arc arcs_54_0[1] = {
    {126, 1},
};
static arc arcs_54_1[2] = {
    {127, 0},
    {0, 1},
};
static state states_54[2] = {
    {1, arcs_54_0},
    {2, arcs_54_1},
};
static arc arcs_55_0[1] = {
    {128, 1},
};
static arc arcs_55_1[2] = {
    {129, 0},
    {0, 1},
};
static state states_55[2] = {
    {1, arcs_55_0},
    {2, arcs_55_1},
};
static arc arcs_56_0[1] = {
    {130, 1},
};
static arc arcs_56_1[2] = {
    {131, 0},
    {0, 1},
};
static state states_56[2] = {
    {1, arcs_56_0},
    {2, arcs_56_1},
};
static arc arcs_57_0[1] = {
    {132, 1},
};
static arc arcs_57_1[3] = {
    {133, 0},
    {59, 0},
    {0, 1},
};
static state states_57[2] = {
    {1, arcs_57_0},
    {3, arcs_57_1},
};
static arc arcs_58_0[1] = {
    {134, 1},
};
static arc arcs_58_1[3] = {
    {135, 0},
    {136, 0},
    {0, 1},
};
static state states_58[2] = {
    {1, arcs_58_0},
    {3, arcs_58_1},
};
static arc arcs_59_0[1] = {
    {137, 1},
};
static arc arcs_59_1[5] = {
    {30, 0},
    {138, 0},
    {139, 0},
    {140, 0},
    {0, 1},
};
static state states_59[2] = {
    {1, arcs_59_0},
    {5, arcs_59_1},
};
static arc arcs_60_0[4] = {
    {135, 1},
    {136, 1},
    {141, 1},
    {142, 2},
};
static arc arcs_60_1[1] = {
    {137, 2},
};
static arc arcs_60_2[1] = {
    {0, 2},
};
static state states_60[3] = {
    {4, arcs_60_0},
    {1, arcs_60_1},
    {1, arcs_60_2},
};
static arc arcs_61_0[1] = {
    {143, 1},
};
static arc arcs_61_1[3] = {
    {144, 1},
    {31, 2},
    {0, 1},
};
static arc arcs_61_2[1] = {
    {137, 3},
};
static arc arcs_61_3[1] = {
    {0, 3},
};
static state states_61[4] = {
    {1, arcs_61_0},
    {3, arcs_61_1},
    {1, arcs_61_2},
    {1, arcs_61_3},
};
static arc arcs_62_0[7] = {
    {13, 1},
    {146, 2},
    {149, 3},
    {152, 4},
    {21, 5},
    {154, 5},
    {155, 6},
};
static arc arcs_62_1[3] = {
    {45, 7},
    {145, 7},
    {15, 5},
};
static arc arcs_62_2[2] = {
    {147, 8},
    {148, 5},
};
static arc arcs_62_3[2] = {
    {150, 9},
    {151, 5},
};
static arc arcs_62_4[1] = {
    {153, 10},
};
static arc arcs_62_5[1] = {
    {0, 5},
};
static arc arcs_62_6[2] = {
    {155, 6},
    {0, 6},
};
static arc arcs_62_7[1] = {
    {15, 5},
};
static arc arcs_62_8[1] = {
    {148, 5},
};
static arc arcs_62_9[1] = {
    {151, 5},
};
static arc arcs_62_10[1] = {
    {152, 5},
};
static state states_62[11] = {
    {7, arcs_62_0},
    {3, arcs_62_1},
    {2, arcs_62_2},
    {2, arcs_62_3},
    {1, arcs_62_4},
    {1, arcs_62_5},
    {2, arcs_62_6},
    {1, arcs_62_7},
    {1, arcs_62_8},
    {1, arcs_62_9},
    {1, arcs_62_10},
};
static arc arcs_63_0[1] = {
    {28, 1},
};
static arc arcs_63_1[3] = {
    {156, 2},
    {29, 3},
    {0, 1},
};
static arc arcs_63_2[1] = {
    {0, 2},
};
static arc arcs_63_3[2] = {
    {28, 4},
    {0, 3},
};
static arc arcs_63_4[2] = {
    {29, 3},
    {0, 4},
};
static state states_63[5] = {
    {1, arcs_63_0},
    {3, arcs_63_1},
    {1, arcs_63_2},
    {2, arcs_63_3},
    {2, arcs_63_4},
};
static arc arcs_64_0[1] = {
    {28, 1},
};
static arc arcs_64_1[3] = {
    {157, 2},
    {29, 3},
    {0, 1},
};
static arc arcs_64_2[1] = {
    {0, 2},
};
static arc arcs_64_3[2] = {
    {28, 4},
    {0, 3},
};
static arc arcs_64_4[2] = {
    {29, 3},
    {0, 4},
};
static state states_64[5] = {
    {1, arcs_64_0},
    {3, arcs_64_1},
    {1, arcs_64_2},
    {2, arcs_64_3},
    {2, arcs_64_4},
};
static arc arcs_65_0[1] = {
    {109, 1},
};
static arc arcs_65_1[2] = {
    {25, 2},
    {23, 3},
};
static arc arcs_65_2[1] = {
    {23, 3},
};
static arc arcs_65_3[1] = {
    {28, 4},
};
static arc arcs_65_4[1] = {
    {0, 4},
};
static state states_65[5] = {
    {1, arcs_65_0},
    {2, arcs_65_1},
    {1, arcs_65_2},
    {1, arcs_65_3},
    {1, arcs_65_4},
};
static arc arcs_66_0[3] = {
    {13, 1},
    {146, 2},
    {77, 3},
};
static arc arcs_66_1[2] = {
    {14, 4},
    {15, 5},
};
static arc arcs_66_2[1] = {
    {158, 6},
};
static arc arcs_66_3[1] = {
    {21, 5},
};
static arc arcs_66_4[1] = {
    {15, 5},
};
static arc arcs_66_5[1] = {
    {0, 5},
};
static arc arcs_66_6[1] = {
    {148, 5},
};
static state states_66[7] = {
    {3, arcs_66_0},
    {2, arcs_66_1},
    {1, arcs_66_2},
    {1, arcs_66_3},
    {1, arcs_66_4},
    {1, arcs_66_5},
    {1, arcs_66_6},
};
static arc arcs_67_0[1] = {
    {159, 1},
};
static arc arcs_67_1[2] = {
    {29, 2},
    {0, 1},
};
static arc arcs_67_2[2] = {
    {159, 1},
    {0, 2},
};
static state states_67[3] = {
    {1, arcs_67_0},
    {2, arcs_67_1},
    {2, arcs_67_2},
};
static arc arcs_68_0[3] = {
    {77, 1},
    {28, 2},
    {23, 3},
};
static arc arcs_68_1[1] = {
    {77, 4},
};
static arc arcs_68_2[2] = {
    {23, 3},
    {0, 2},
};
static arc arcs_68_3[3] = {
    {28, 5},
    {160, 6},
    {0, 3},
};
static arc arcs_68_4[1] = {
    {77, 6},
};
static arc arcs_68_5[2] = {
    {160, 6},
    {0, 5},
};
static arc arcs_68_6[1] = {
    {0, 6},
};
static state states_68[7] = {
    {3, arcs_68_0},
    {1, arcs_68_1},
    {2, arcs_68_2},
    {3, arcs_68_3},
    {1, arcs_68_4},
    {2, arcs_68_5},
    {1, arcs_68_6},
};
static arc arcs_69_0[1] = {
    {23, 1},
};
static arc arcs_69_1[2] = {
    {28, 2},
    {0, 1},
};
static arc arcs_69_2[1] = {
    {0, 2},
};
static state states_69[3] = {
    {1, arcs_69_0},
    {2, arcs_69_1},
    {1, arcs_69_2},
};
static arc arcs_70_0[1] = {
    {84, 1},
};
static arc arcs_70_1[2] = {
    {29, 2},
    {0, 1},
};
static arc arcs_70_2[2] = {
    {84, 1},
    {0, 2},
};
static state states_70[3] = {
    {1, arcs_70_0},
    {2, arcs_70_1},
    {2, arcs_70_2},
};
static arc arcs_71_0[1] = {
    {28, 1},
};
static arc arcs_71_1[2] = {
    {29, 2},
    {0, 1},
};
static arc arcs_71_2[2] = {
    {28, 1},
    {0, 2},
};
static state states_71[3] = {
    {1, arcs_71_0},
    {2, arcs_71_1},
    {2, arcs_71_2},
};
static arc arcs_72_0[1] = {
    {28, 1},
};
static arc arcs_72_1[4] = {
    {23, 2},
    {157, 3},
    {29, 4},
    {0, 1},
};
static arc arcs_72_2[1] = {
    {28, 5},
};
static arc arcs_72_3[1] = {
    {0, 3},
};
static arc arcs_72_4[2] = {
    {28, 6},
    {0, 4},
};
static arc arcs_72_5[3] = {
    {157, 3},
    {29, 7},
    {0, 5},
};
static arc arcs_72_6[2] = {
    {29, 4},
    {0, 6},
};
static arc arcs_72_7[2] = {
    {28, 8},
    {0, 7},
};
static arc arcs_72_8[1] = {
    {23, 9},
};
static arc arcs_72_9[1] = {
    {28, 10},
};
static arc arcs_72_10[2] = {
    {29, 7},
    {0, 10},
};
static state states_72[11] = {
    {1, arcs_72_0},
    {4, arcs_72_1},
    {1, arcs_72_2},
    {1, arcs_72_3},
    {2, arcs_72_4},
    {3, arcs_72_5},
    {2, arcs_72_6},
    {2, arcs_72_7},
    {1, arcs_72_8},
    {1, arcs_72_9},
    {2, arcs_72_10},
};
static arc arcs_73_0[1] = {
    {161, 1},
};
static arc arcs_73_1[1] = {
    {21, 2},
};
static arc arcs_73_2[2] = {
    {13, 3},
    {23, 4},
};
static arc arcs_73_3[2] = {
    {9, 5},
    {15, 6},
};
static arc arcs_73_4[1] = {
    {24, 7},
};
static arc arcs_73_5[1] = {
    {15, 6},
};
static arc arcs_73_6[1] = {
    {23, 4},
};
static arc arcs_73_7[1] = {
    {0, 7},
};
static state states_73[8] = {
    {1, arcs_73_0},
    {1, arcs_73_1},
    {2, arcs_73_2},
    {2, arcs_73_3},
    {1, arcs_73_4},
    {1, arcs_73_5},
    {1, arcs_73_6},
    {1, arcs_73_7},
};
static arc arcs_74_0[3] = {
    {162, 1},
    {30, 2},
    {31, 3},
};
static arc arcs_74_1[2] = {
    {29, 4},
    {0, 1},
};
static arc arcs_74_2[1] = {
    {28, 5},
};
static arc arcs_74_3[1] = {
    {28, 6},
};
static arc arcs_74_4[4] = {
    {162, 1},
    {30, 2},
    {31, 3},
    {0, 4},
};
static arc arcs_74_5[2] = {
    {29, 7},
    {0, 5},
};
static arc arcs_74_6[1] = {
    {0, 6},
};
static arc arcs_74_7[2] = {
    {162, 5},
    {31, 3},
};
static state states_74[8] = {
    {3, arcs_74_0},
    {2, arcs_74_1},
    {1, arcs_74_2},
    {1, arcs_74_3},
    {4, arcs_74_4},
    {2, arcs_74_5},
    {1, arcs_74_6},
    {2, arcs_74_7},
};
static arc arcs_75_0[1] = {
    {28, 1},
};
static arc arcs_75_1[3] = {
    {157, 2},
    {27, 3},
    {0, 1},
};
static arc arcs_75_2[1] = {
    {0, 2},
};
static arc arcs_75_3[1] = {
    {28, 2},
};
static state states_75[4] = {
    {1, arcs_75_0},
    {3, arcs_75_1},
    {1, arcs_75_2},
    {1, arcs_75_3},
};
static arc arcs_76_0[2] = {
    {156, 1},
    {164, 1},
};
static arc arcs_76_1[1] = {
    {0, 1},
};
static state states_76[2] = {
    {2, arcs_76_0},
    {1, arcs_76_1},
};
static arc arcs_77_0[1] = {
    {96, 1},
};
static arc arcs_77_1[1] = {
    {61, 2},
};
static arc arcs_77_2[1] = {
    {85, 3},
};
static arc arcs_77_3[1] = {
    {105, 4},
};
static arc arcs_77_4[2] = {
    {163, 5},
    {0, 4},
};
static arc arcs_77_5[1] = {
    {0, 5},
};
static state states_77[6] = {
    {1, arcs_77_0},
    {1, arcs_77_1},
    {1, arcs_77_2},
    {1, arcs_77_3},
    {2, arcs_77_4},
    {1, arcs_77_5},
};
static arc arcs_78_0[1] = {
    {92, 1},
};
static arc arcs_78_1[1] = {
    {106, 2},
};
static arc arcs_78_2[2] = {
    {163, 3},
    {0, 2},
};
static arc arcs_78_3[1] = {
    {0, 3},
};
static state states_78[4] = {
    {1, arcs_78_0},
    {1, arcs_78_1},
    {2, arcs_78_2},
    {1, arcs_78_3},
};
static arc arcs_79_0[2] = {
    {157, 1},
    {166, 1},
};
static arc arcs_79_1[1] = {
    {0, 1},
};
static state states_79[2] = {
    {2, arcs_79_0},
    {1, arcs_79_1},
};
static arc arcs_80_0[1] = {
    {96, 1},
};
static arc arcs_80_1[1] = {
    {61, 2},
};
static arc arcs_80_2[1] = {
    {85, 3},
};
static arc arcs_80_3[1] = {
    {107, 4},
};
static arc arcs_80_4[2] = {
    {165, 5},
    {0, 4},
};
static arc arcs_80_5[1] = {
    {0, 5},
};
static state states_80[6] = {
    {1, arcs_80_0},
    {1, arcs_80_1},
    {1, arcs_80_2},
    {1, arcs_80_3},
    {2, arcs_80_4},
    {1, arcs_80_5},
};
static arc arcs_81_0[1] = {
    {92, 1},
};
static arc arcs_81_1[1] = {
    {106, 2},
};
static arc arcs_81_2[2] = {
    {165, 3},
    {0, 2},
};
static arc arcs_81_3[1] = {
    {0, 3},
};
static state states_81[4] = {
    {1, arcs_81_0},
    {1, arcs_81_1},
    {2, arcs_81_2},
    {1, arcs_81_3},
};
static arc arcs_82_0[1] = {
    {28, 1},
};
static arc arcs_82_1[2] = {
    {29, 0},
    {0, 1},
};
static state states_82[2] = {
    {1, arcs_82_0},
    {2, arcs_82_1},
};
static arc arcs_83_0[1] = {
    {21, 1},
};
static arc arcs_83_1[1] = {
    {0, 1},
};
static state states_83[2] = {
    {1, arcs_83_0},
    {1, arcs_83_1},
};
static arc arcs_84_0[1] = {
    {168, 1},
};
static arc arcs_84_1[2] = {
    {9, 2},
    {0, 1},
};
static arc arcs_84_2[1] = {
    {0, 2},
};
static state states_84[3] = {
    {1, arcs_84_0},
    {2, arcs_84_1},
    {1, arcs_84_2},
};
static dfa dfas[85] = {
    {256, "single_input", 0, 3, states_0,
     "\004\050\060\000\000\000\000\124\360\024\114\220\023\040\010\000\200\041\044\015\002\001"},
    {257, "file_input", 0, 2, states_1,
     "\204\050\060\000\000\000\000\124\360\024\114\220\023\040\010\000\200\041\044\015\002\001"},
    {258, "eval_input", 0, 3, states_2,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {259, "decorator", 0, 7, states_3,
     "\000\010\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {260, "decorators", 0, 2, states_4,
     "\000\010\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {261, "decorated", 0, 3, states_5,
     "\000\010\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {262, "funcdef", 0, 6, states_6,
     "\000\000\020\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {263, "parameters", 0, 4, states_7,
     "\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {264, "varargslist", 0, 10, states_8,
     "\000\040\040\300\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {265, "fpdef", 0, 4, states_9,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {266, "fplist", 0, 3, states_10,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {267, "stmt", 0, 2, states_11,
     "\000\050\060\000\000\000\000\124\360\024\114\220\023\040\010\000\200\041\044\015\002\001"},
    {268, "simple_stmt", 0, 4, states_12,
     "\000\040\040\000\000\000\000\124\360\024\114\000\000\040\010\000\200\041\044\015\000\001"},
    {269, "small_stmt", 0, 2, states_13,
     "\000\040\040\000\000\000\000\124\360\024\114\000\000\040\010\000\200\041\044\015\000\001"},
    {270, "expr_stmt", 0, 6, states_14,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {271, "augassign", 0, 2, states_15,
     "\000\000\000\000\000\300\377\003\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {272, "print_stmt", 0, 9, states_16,
     "\000\000\000\000\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {273, "del_stmt", 0, 3, states_17,
     "\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {274, "pass_stmt", 0, 2, states_18,
     "\000\000\000\000\000\000\000\100\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {275, "flow_stmt", 0, 2, states_19,
     "\000\000\000\000\000\000\000\000\360\000\000\000\000\000\000\000\000\000\000\000\000\001"},
    {276, "break_stmt", 0, 2, states_20,
     "\000\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {277, "continue_stmt", 0, 2, states_21,
     "\000\000\000\000\000\000\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {278, "return_stmt", 0, 3, states_22,
     "\000\000\000\000\000\000\000\000\100\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {279, "yield_stmt", 0, 2, states_23,
     "\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001"},
    {280, "raise_stmt", 0, 7, states_24,
     "\000\000\000\000\000\000\000\000\200\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {281, "import_stmt", 0, 2, states_25,
     "\000\000\000\000\000\000\000\000\000\024\000\000\000\000\000\000\000\000\000\000\000\000"},
    {282, "import_name", 0, 3, states_26,
     "\000\000\000\000\000\000\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000\000"},
    {283, "import_from", 0, 8, states_27,
     "\000\000\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000\000\000\000"},
    {284, "import_as_name", 0, 4, states_28,
     "\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {285, "dotted_as_name", 0, 4, states_29,
     "\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {286, "import_as_names", 0, 3, states_30,
     "\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {287, "dotted_as_names", 0, 2, states_31,
     "\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {288, "dotted_name", 0, 2, states_32,
     "\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {289, "global_stmt", 0, 3, states_33,
     "\000\000\000\000\000\000\000\000\000\000\004\000\000\000\000\000\000\000\000\000\000\000"},
    {290, "exec_stmt", 0, 7, states_34,
     "\000\000\000\000\000\000\000\000\000\000\010\000\000\000\000\000\000\000\000\000\000\000"},
    {291, "assert_stmt", 0, 5, states_35,
     "\000\000\000\000\000\000\000\000\000\000\100\000\000\000\000\000\000\000\000\000\000\000"},
    {292, "compound_stmt", 0, 2, states_36,
     "\000\010\020\000\000\000\000\000\000\000\000\220\023\000\000\000\000\000\000\000\002\000"},
    {293, "if_stmt", 0, 8, states_37,
     "\000\000\000\000\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000\000"},
    {294, "while_stmt", 0, 8, states_38,
     "\000\000\000\000\000\000\000\000\000\000\000\200\000\000\000\000\000\000\000\000\000\000"},
    {295, "for_stmt", 0, 10, states_39,
     "\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\000\000"},
    {296, "try_stmt", 0, 13, states_40,
     "\000\000\000\000\000\000\000\000\000\000\000\000\002\000\000\000\000\000\000\000\000\000"},
    {297, "with_stmt", 0, 5, states_41,
     "\000\000\000\000\000\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000"},
    {298, "with_item", 0, 4, states_42,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {299, "except_clause", 0, 5, states_43,
     "\000\000\000\000\000\000\000\000\000\000\000\000\100\000\000\000\000\000\000\000\000\000"},
    {300, "suite", 0, 5, states_44,
     "\004\040\040\000\000\000\000\124\360\024\114\000\000\040\010\000\200\041\044\015\000\001"},
    {301, "testlist_safe", 0, 5, states_45,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {302, "old_test", 0, 2, states_46,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {303, "old_lambdef", 0, 5, states_47,
     "\000\000\000\000\000\000\000\000\000\000\000\000\000\040\000\000\000\000\000\000\000\000"},
    {304, "test", 0, 6, states_48,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {305, "or_test", 0, 2, states_49,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\010\000\200\041\044\015\000\000"},
    {306, "and_test", 0, 2, states_50,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\010\000\200\041\044\015\000\000"},
    {307, "not_test", 0, 3, states_51,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\010\000\200\041\044\015\000\000"},
    {308, "comparison", 0, 2, states_52,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {309, "comp_op", 0, 4, states_53,
     "\000\000\000\000\000\000\000\000\000\000\040\000\000\000\310\077\000\000\000\000\000\000"},
    {310, "expr", 0, 2, states_54,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {311, "xor_expr", 0, 2, states_55,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {312, "and_expr", 0, 2, states_56,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {313, "shift_expr", 0, 2, states_57,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {314, "arith_expr", 0, 2, states_58,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {315, "term", 0, 2, states_59,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {316, "factor", 0, 3, states_60,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {317, "power", 0, 4, states_61,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\044\015\000\000"},
    {318, "atom", 0, 11, states_62,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\044\015\000\000"},
    {319, "listmaker", 0, 5, states_63,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {320, "testlist_comp", 0, 5, states_64,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {321, "lambdef", 0, 5, states_65,
     "\000\000\000\000\000\000\000\000\000\000\000\000\000\040\000\000\000\000\000\000\000\000"},
    {322, "trailer", 0, 7, states_66,
     "\000\040\000\000\000\000\000\000\000\040\000\000\000\000\000\000\000\000\004\000\000\000"},
    {323, "subscriptlist", 0, 3, states_67,
     "\000\040\240\000\000\000\000\000\000\040\000\000\000\040\010\000\200\041\044\015\000\000"},
    {324, "subscript", 0, 7, states_68,
     "\000\040\240\000\000\000\000\000\000\040\000\000\000\040\010\000\200\041\044\015\000\000"},
    {325, "sliceop", 0, 3, states_69,
     "\000\000\200\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {326, "exprlist", 0, 3, states_70,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\000\000\000\200\041\044\015\000\000"},
    {327, "testlist", 0, 3, states_71,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {328, "dictorsetmaker", 0, 11, states_72,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {329, "classdef", 0, 8, states_73,
     "\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\002\000"},
    {330, "arglist", 0, 8, states_74,
     "\000\040\040\300\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {331, "argument", 0, 4, states_75,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {332, "list_iter", 0, 2, states_76,
     "\000\000\000\000\000\000\000\000\000\000\000\020\001\000\000\000\000\000\000\000\000\000"},
    {333, "list_for", 0, 6, states_77,
     "\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\000\000"},
    {334, "list_if", 0, 4, states_78,
     "\000\000\000\000\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000\000"},
    {335, "comp_iter", 0, 2, states_79,
     "\000\000\000\000\000\000\000\000\000\000\000\020\001\000\000\000\000\000\000\000\000\000"},
    {336, "comp_for", 0, 6, states_80,
     "\000\000\000\000\000\000\000\000\000\000\000\000\001\000\000\000\000\000\000\000\000\000"},
    {337, "comp_if", 0, 4, states_81,
     "\000\000\000\000\000\000\000\000\000\000\000\020\000\000\000\000\000\000\000\000\000\000"},
    {338, "testlist1", 0, 2, states_82,
     "\000\040\040\000\000\000\000\000\000\000\000\000\000\040\010\000\200\041\044\015\000\000"},
    {339, "encoding_decl", 0, 2, states_83,
     "\000\000\040\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000"},
    {340, "yield_expr", 0, 3, states_84,
     "\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\000\001"},
};
static label labels[169] = {
    {0, "EMPTY"},
    {256, 0},
    {4, 0},
    {268, 0},
    {292, 0},
    {257, 0},
    {267, 0},
    {0, 0},
    {258, 0},
    {327, 0},
    {259, 0},
    {50, 0},
    {288, 0},
    {7, 0},
    {330, 0},
    {8, 0},
    {260, 0},
    {261, 0},
    {329, 0},
    {262, 0},
    {1, "def"},
    {1, 0},
    {263, 0},
    {11, 0},
    {300, 0},
    {264, 0},
    {265, 0},
    {22, 0},
    {304, 0},
    {12, 0},
    {16, 0},
    {36, 0},
    {266, 0},
    {269, 0},
    {13, 0},
    {270, 0},
    {272, 0},
    {273, 0},
    {274, 0},
    {275, 0},
    {281, 0},
    {289, 0},
    {290, 0},
    {291, 0},
    {271, 0},
    {340, 0},
    {37, 0},
    {38, 0},
    {39, 0},
    {40, 0},
    {41, 0},
    {42, 0},
    {43, 0},
    {44, 0},
    {45, 0},
    {46, 0},
    {47, 0},
    {49, 0},
    {1, "print"},
    {35, 0},
    {1, "del"},
    {326, 0},
    {1, "pass"},
    {276, 0},
    {277, 0},
    {278, 0},
    {280, 0},
    {279, 0},
    {1, "break"},
    {1, "continue"},
    {1, "return"},
    {1, "raise"},
    {282, 0},
    {283, 0},
    {1, "import"},
    {287, 0},
    {1, "from"},
    {23, 0},
    {286, 0},
    {284, 0},
    {1, "as"},
    {285, 0},
    {1, "global"},
    {1, "exec"},
    {310, 0},
    {1, "in"},
    {1, "assert"},
    {293, 0},
    {294, 0},
    {295, 0},
    {296, 0},
    {297, 0},
    {1, "if"},
    {1, "elif"},
    {1, "else"},
    {1, "while"},
    {1, "for"},
    {1, "try"},
    {299, 0},
    {1, "finally"},
    {1, "with"},
    {298, 0},
    {1, "except"},
    {5, 0},
    {6, 0},
    {301, 0},
    {302, 0},
    {305, 0},
    {303, 0},
    {1, "lambda"},
    {321, 0},
    {306, 0},
    {1, "or"},
    {307, 0},
    {1, "and"},
    {1, "not"},
    {308, 0},
    {309, 0},
    {20, 0},
    {21, 0},
    {28, 0},
    {31, 0},
    {30, 0},
    {29, 0},
    {29, 0},
    {1, "is"},
    {311, 0},
    {18, 0},
    {312, 0},
    {33, 0},
    {313, 0},
    {19, 0},
    {314, 0},
    {34, 0},
    {315, 0},
    {14, 0},
    {15, 0},
    {316, 0},
    {17, 0},
    {24, 0},
    {48, 0},
    {32, 0},
    {317, 0},
    {318, 0},
    {322, 0},
    {320, 0},
    {9, 0},
    {319, 0},
    {10, 0},
    {26, 0},
    {328, 0},
    {27, 0},
    {25, 0},
    {338, 0},
    {2, 0},
    {3, 0},
    {333, 0},
    {336, 0},
    {323, 0},
    {324, 0},
    {325, 0},
    {1, "class"},
    {331, 0},
    {332, 0},
    {334, 0},
    {335, 0},
    {337, 0},
    {339, 0},
    {1, "yield"},
};
grammar _PyParser_Grammar = {
    85,
    dfas,
    {169, labels},
    256
};
