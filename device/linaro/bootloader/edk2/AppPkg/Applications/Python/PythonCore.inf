## @file
# PythonCore.inf
#
#  Copyright (c) 2015, <PERSON>. All rights reserved.<BR>
#  Copyright (c) 2011-2012, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.
#
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                 = 0x00010016
  BASE_NAME                   = Python
  FILE_GUID                   = ca5627c4-51ba-4dcb-ac62-c076ebd37ddb
  MODULE_TYPE                 = UEFI_APPLICATION
  VERSION_STRING              = 0.8
  ENTRY_POINT                 = ShellCEntryLib

  DEFINE PYTHON_VERSION       = 2.7.2

#
#  VALID_ARCHITECTURES        = IA32 X64 IPF
#

[Packages]
  StdLib/StdLib.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiLib
  DebugLib
  LibC
  LibString
  LibStdio
  LibGdtoa
  LibMath
  LibWchar
  LibGen
  LibNetUtil
  DevMedia
  #
  # Comment out the following two library classes if socket support is
  # NOT being built in to Python.
  #BsdSocketLib
  #EfiSocketLib

[FixedPcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask|0x0F
  gEfiMdePkgTokenSpaceGuid.PcdDebugPrintErrorLevel|0x80000040

[Sources]
#EFI -- EFI specific code
  Efi/config.c
  Efi/edk2module.c
  Efi/getpath.c

#Parser
  Python-$(PYTHON_VERSION)/Parser/acceler.c
  Python-$(PYTHON_VERSION)/Parser/bitset.c
  Python-$(PYTHON_VERSION)/Parser/firstsets.c
  Python-$(PYTHON_VERSION)/Parser/grammar.c
  Python-$(PYTHON_VERSION)/Parser/grammar1.c
  Python-$(PYTHON_VERSION)/Parser/listnode.c
  Python-$(PYTHON_VERSION)/Parser/metagrammar.c
  Python-$(PYTHON_VERSION)/Parser/myreadline.c
  Python-$(PYTHON_VERSION)/Parser/node.c
  Python-$(PYTHON_VERSION)/Parser/parser.c
  Python-$(PYTHON_VERSION)/Parser/parsetok.c
  Python-$(PYTHON_VERSION)/Parser/tokenizer.c

#Python
  PyMod-$(PYTHON_VERSION)/Python/getcopyright.c
  PyMod-$(PYTHON_VERSION)/Python/marshal.c
  PyMod-$(PYTHON_VERSION)/Python/import.c

  Python-$(PYTHON_VERSION)/Python/_warnings.c
  Python-$(PYTHON_VERSION)/Python/Python-ast.c
  Python-$(PYTHON_VERSION)/Python/asdl.c
  Python-$(PYTHON_VERSION)/Python/ast.c
  Python-$(PYTHON_VERSION)/Python/bltinmodule.c
  Python-$(PYTHON_VERSION)/Python/ceval.c
  Python-$(PYTHON_VERSION)/Python/codecs.c
  Python-$(PYTHON_VERSION)/Python/compile.c
  Python-$(PYTHON_VERSION)/Python/dtoa.c
  Python-$(PYTHON_VERSION)/Python/dynload_stub.c
  Python-$(PYTHON_VERSION)/Python/errors.c
  Python-$(PYTHON_VERSION)/Python/formatter_string.c
  Python-$(PYTHON_VERSION)/Python/formatter_unicode.c
  Python-$(PYTHON_VERSION)/Python/frozen.c
  Python-$(PYTHON_VERSION)/Python/future.c
  Python-$(PYTHON_VERSION)/Python/getargs.c
  Python-$(PYTHON_VERSION)/Python/getcompiler.c
  Python-$(PYTHON_VERSION)/Python/getopt.c
  Python-$(PYTHON_VERSION)/Python/getplatform.c
  Python-$(PYTHON_VERSION)/Python/getversion.c
  Python-$(PYTHON_VERSION)/Python/graminit.c
  Python-$(PYTHON_VERSION)/Python/importdl.c
  Python-$(PYTHON_VERSION)/Python/modsupport.c
  Python-$(PYTHON_VERSION)/Python/mysnprintf.c
  Python-$(PYTHON_VERSION)/Python/mystrtoul.c
  Python-$(PYTHON_VERSION)/Python/peephole.c
  Python-$(PYTHON_VERSION)/Python/pyarena.c
  Python-$(PYTHON_VERSION)/Python/pyctype.c
  Python-$(PYTHON_VERSION)/Python/pyfpe.c
  Python-$(PYTHON_VERSION)/Python/pymath.c
  Python-$(PYTHON_VERSION)/Python/pystate.c
  Python-$(PYTHON_VERSION)/Python/pystrcmp.c
  Python-$(PYTHON_VERSION)/Python/pystrtod.c
  Python-$(PYTHON_VERSION)/Python/pythonrun.c
  Python-$(PYTHON_VERSION)/Python/structmember.c
  Python-$(PYTHON_VERSION)/Python/symtable.c
  Python-$(PYTHON_VERSION)/Python/sysmodule.c
  Python-$(PYTHON_VERSION)/Python/traceback.c

#Objects
  PyMod-$(PYTHON_VERSION)/Objects/longobject.c

  Python-$(PYTHON_VERSION)/Objects/abstract.c
  Python-$(PYTHON_VERSION)/Objects/boolobject.c
  Python-$(PYTHON_VERSION)/Objects/bufferobject.c
  Python-$(PYTHON_VERSION)/Objects/bytearrayobject.c
  Python-$(PYTHON_VERSION)/Objects/bytes_methods.c
  Python-$(PYTHON_VERSION)/Objects/capsule.c
  Python-$(PYTHON_VERSION)/Objects/cellobject.c
  Python-$(PYTHON_VERSION)/Objects/classobject.c
  Python-$(PYTHON_VERSION)/Objects/cobject.c
  Python-$(PYTHON_VERSION)/Objects/codeobject.c
  Python-$(PYTHON_VERSION)/Objects/complexobject.c
  Python-$(PYTHON_VERSION)/Objects/descrobject.c
  Python-$(PYTHON_VERSION)/Objects/dictobject.c
  Python-$(PYTHON_VERSION)/Objects/enumobject.c
  Python-$(PYTHON_VERSION)/Objects/exceptions.c
  Python-$(PYTHON_VERSION)/Objects/fileobject.c
  Python-$(PYTHON_VERSION)/Objects/floatobject.c
  Python-$(PYTHON_VERSION)/Objects/frameobject.c
  Python-$(PYTHON_VERSION)/Objects/funcobject.c
  Python-$(PYTHON_VERSION)/Objects/genobject.c
  Python-$(PYTHON_VERSION)/Objects/intobject.c
  Python-$(PYTHON_VERSION)/Objects/iterobject.c
  Python-$(PYTHON_VERSION)/Objects/listobject.c
  Python-$(PYTHON_VERSION)/Objects/memoryobject.c
  Python-$(PYTHON_VERSION)/Objects/methodobject.c
  Python-$(PYTHON_VERSION)/Objects/moduleobject.c
  Python-$(PYTHON_VERSION)/Objects/object.c
  Python-$(PYTHON_VERSION)/Objects/obmalloc.c
  Python-$(PYTHON_VERSION)/Objects/rangeobject.c
  Python-$(PYTHON_VERSION)/Objects/setobject.c
  Python-$(PYTHON_VERSION)/Objects/sliceobject.c
  Python-$(PYTHON_VERSION)/Objects/stringobject.c
  Python-$(PYTHON_VERSION)/Objects/structseq.c
  Python-$(PYTHON_VERSION)/Objects/tupleobject.c
  Python-$(PYTHON_VERSION)/Objects/typeobject.c
  Python-$(PYTHON_VERSION)/Objects/unicodectype.c
  Python-$(PYTHON_VERSION)/Objects/unicodeobject.c
  Python-$(PYTHON_VERSION)/Objects/weakrefobject.c

#Modules -- See Efi/config.c
  # Mandatory Modules -- These must always be built in.
  PyMod-$(PYTHON_VERSION)/Modules/errnomodule.c
  Python-$(PYTHON_VERSION)/Modules/_functoolsmodule.c
  Python-$(PYTHON_VERSION)/Modules/gcmodule.c
  Python-$(PYTHON_VERSION)/Modules/getbuildinfo.c
  PyMod-$(PYTHON_VERSION)/Modules/main.c
  Python-$(PYTHON_VERSION)/Modules/python.c

  # Optional Modules -- See Python/Efi/config.c
  PyMod-$(PYTHON_VERSION)/Modules/_sre.c                        #
  PyMod-$(PYTHON_VERSION)/Modules/selectmodule.c                #
  #
  Python-$(PYTHON_VERSION)/Modules/_bisectmodule.c              #
  Python-$(PYTHON_VERSION)/Modules/_codecsmodule.c              #
  Python-$(PYTHON_VERSION)/Modules/_collectionsmodule.c         #
  Python-$(PYTHON_VERSION)/Modules/_csv.c                       #
  Python-$(PYTHON_VERSION)/Modules/_heapqmodule.c               #
  Python-$(PYTHON_VERSION)/Modules/_json.c                      #
  Python-$(PYTHON_VERSION)/Modules/_localemodule.c              #
  Python-$(PYTHON_VERSION)/Modules/_math.c                      #
  Python-$(PYTHON_VERSION)/Modules/_randommodule.c              #
  Python-$(PYTHON_VERSION)/Modules/_struct.c                    #
  Python-$(PYTHON_VERSION)/Modules/_weakref.c                   #
  Python-$(PYTHON_VERSION)/Modules/arraymodule.c                #
  Python-$(PYTHON_VERSION)/Modules/binascii.c                   #
  Python-$(PYTHON_VERSION)/Modules/cmathmodule.c                #
  Python-$(PYTHON_VERSION)/Modules/cPickle.c                    #
  Python-$(PYTHON_VERSION)/Modules/cStringIO.c                  #
  Python-$(PYTHON_VERSION)/Modules/datetimemodule.c             #
  Python-$(PYTHON_VERSION)/Modules/future_builtins.c            #
  Python-$(PYTHON_VERSION)/Modules/itertoolsmodule.c            #
  Python-$(PYTHON_VERSION)/Modules/mathmodule.c                 #
  Python-$(PYTHON_VERSION)/Modules/md5.c                        #
  Python-$(PYTHON_VERSION)/Modules/md5module.c                  #
  Python-$(PYTHON_VERSION)/Modules/operator.c                   #
  Python-$(PYTHON_VERSION)/Modules/parsermodule.c               #
  Python-$(PYTHON_VERSION)/Modules/sha256module.c               #
  Python-$(PYTHON_VERSION)/Modules/sha512module.c               #
  Python-$(PYTHON_VERSION)/Modules/shamodule.c                  #
  Python-$(PYTHON_VERSION)/Modules/signalmodule.c               #
  Python-$(PYTHON_VERSION)/Modules/socketmodule.c               #
  Python-$(PYTHON_VERSION)/Modules/stropmodule.c                #
  Python-$(PYTHON_VERSION)/Modules/symtablemodule.c             #
  Python-$(PYTHON_VERSION)/Modules/timemodule.c                 #
  Python-$(PYTHON_VERSION)/Modules/unicodedata.c                #
  Python-$(PYTHON_VERSION)/Modules/xxsubtype.c                  #
  Python-$(PYTHON_VERSION)/Modules/zipimport.c                  #
  Python-$(PYTHON_VERSION)/Modules/zlibmodule.c                 #

#Modules/_io
  Python-$(PYTHON_VERSION)/Modules/_io/_iomodule.c              #
  Python-$(PYTHON_VERSION)/Modules/_io/bufferedio.c             #
  Python-$(PYTHON_VERSION)/Modules/_io/bytesio.c                #
  Python-$(PYTHON_VERSION)/Modules/_io/fileio.c                 #
  Python-$(PYTHON_VERSION)/Modules/_io/iobase.c                 #
  Python-$(PYTHON_VERSION)/Modules/_io/stringio.c               #
  Python-$(PYTHON_VERSION)/Modules/_io/textio.c                 #

#Modules/cjkcodecs
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/multibytecodec.c   #
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/_codecs_cn.c       #
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/_codecs_hk.c       #
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/_codecs_iso2022.c  #
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/_codecs_jp.c       #
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/_codecs_kr.c       #
  Python-$(PYTHON_VERSION)/Modules/cjkcodecs/_codecs_tw.c       #

#Modules/expat
  Python-$(PYTHON_VERSION)/Modules/pyexpat.c                    #
  PyMod-$(PYTHON_VERSION)/Modules/expat/xmlparse.c              #
  Python-$(PYTHON_VERSION)/Modules/expat/xmlrole.c              #
  Python-$(PYTHON_VERSION)/Modules/expat/xmltok.c               #

#Modules/zlib
  Python-$(PYTHON_VERSION)/Modules/zlib/adler32.c               #
  Python-$(PYTHON_VERSION)/Modules/zlib/compress.c              #
  Python-$(PYTHON_VERSION)/Modules/zlib/crc32.c                 #
  Python-$(PYTHON_VERSION)/Modules/zlib/deflate.c               #
  Python-$(PYTHON_VERSION)/Modules/zlib/gzio.c                  #
  Python-$(PYTHON_VERSION)/Modules/zlib/infback.c               #
  Python-$(PYTHON_VERSION)/Modules/zlib/inffast.c               #
  Python-$(PYTHON_VERSION)/Modules/zlib/inflate.c               #
  Python-$(PYTHON_VERSION)/Modules/zlib/inftrees.c              #
  Python-$(PYTHON_VERSION)/Modules/zlib/trees.c                 #
  Python-$(PYTHON_VERSION)/Modules/zlib/uncompr.c               #
  Python-$(PYTHON_VERSION)/Modules/zlib/zutil.c                 #

[BuildOptions]
   MSFT:*_*_IA32_CC_FLAGS         = /Oi- /wd4018 /wd4054 /wd4055 /wd4101 /wd4131 /wd4152 /wd4204 /wd4210 /wd4244 /wd4267 /wd4305 /wd4310 /wd4389 /wd4701 /wd4702 /wd4706 /I$(WORKSPACE)\AppPkg\Applications\Python\Ia32 /I$(WORKSPACE)\AppPkg\Applications\Python\Efi /I$(WORKSPACE)\AppPkg\Applications\Python\Python-$(PYTHON_VERSION)\Include /DHAVE_MEMMOVE /DUSE_PYEXPAT_CAPI /DXML_STATIC
   MSFT:*_*_X64_CC_FLAGS          = /Oi- /wd4018 /wd4054 /wd4055 /wd4101 /wd4131 /wd4152 /wd4204 /wd4210 /wd4244 /wd4267 /wd4305 /wd4310 /wd4389 /wd4701 /wd4702 /wd4706 /I$(WORKSPACE)\AppPkg\Applications\Python\X64 /I$(WORKSPACE)\AppPkg\Applications\Python\Efi /I$(WORKSPACE)\AppPkg\Applications\Python\Python-$(PYTHON_VERSION)\Include /DHAVE_MEMMOVE /DUSE_PYEXPAT_CAPI /DXML_STATIC
    GCC:*_*_IA32_CC_FLAGS         = -fno-builtin -Wno-format -I$(WORKSPACE)/AppPkg/Applications/Python/Ia32 -I$(WORKSPACE)/AppPkg/Applications/Python/Python-$(PYTHON_VERSION)/Include -DHAVE_MEMMOVE -DUSE_PYEXPAT_CAPI -DXML_STATIC
    GCC:*_*_X64_CC_FLAGS          = -Wno-format -I$(WORKSPACE)/AppPkg/Applications/Python/X64 -I$(WORKSPACE)/AppPkg/Applications/Python/Python-$(PYTHON_VERSION)/Include -DHAVE_MEMMOVE -DUSE_PYEXPAT_CAPI -DXML_STATIC
    GCC:*_*_IPF_SYMRENAME_FLAGS   = --redefine-syms=$(WORKSPACE)/StdLib/GccSymRename.txt
