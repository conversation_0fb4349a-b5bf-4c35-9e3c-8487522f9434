<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2008 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
  
          http://www.apache.org/licenses/LICENSE-2.0
  
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- This file contains fake APNs that are necessary for the emulator
     to talk to the network.  It should only be installed for SDK builds.

     This file is not installed by the local Android.mk, it's installed using
     a PRODUCT_COPY_FILES line in the sdk section of the toplevel Makefile.
-->

<!-- use empty string to specify no proxy or port -->
<!-- This version must agree with that in apps/common/res/apns.xml -->
<apns version="8">
    <apn carrier="Android"
        mcc="310"
        mnc="995"
        apn="internet"
        user="*"
        server="*"
        password="*"
        mmsc="null"
    />
    <apn carrier="TelKila"
        mcc="310"
        mnc="260"
        apn="internet"
        user="*"
        server="*"
        password="*"
        mmsc="null"
    />
</apns>
