cc_binary {
    name: "<EMAIL>",
    init_rc: ["<EMAIL>"],
    proprietary: true,
    relative_install_path: "hw",
    srcs: [
        "HealthService.cpp",
    ],
    cflags: [
        "-Wall",
        "-Werror",
    ],
    static_libs: [
        "android.hardware.health@2.0-impl",
        "android.hardware.health@1.0-convert",
        "libhealthservice",
        "libbatterymonitor",
        "libhealthstoragedefault",
    ],
    shared_libs: [
        "libbase",
        "libcutils",
        "libhidlbase",
        "libhidltransport",
        "libhwbinder",
        "libutils",
        "android.hardware.health@2.0",
    ],
    header_libs: ["libhealthd_headers"],
}
