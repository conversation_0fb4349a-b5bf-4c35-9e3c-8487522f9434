//
// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

cc_binary {
    name: "netmgr",
    vendor: true,
    cflags: [
             "-Wall",
             "-Werror",
            ],
    srcs: [
           "address_assigner.cpp",
           "commander.cpp",
           "fork.cpp",
           "interface_state.cpp",
           "log.cpp",
           "main.cpp",
           "monitor.cpp",
           "poller.cpp",
           "wifi_forwarder.cpp",
           "commands/wifi_command.cpp",
          ],
    shared_libs: [
        "libcutils",
        "liblog",
        "libpcap",
    ],
    header_libs: [
        "goldfish_headers",
    ],
}
