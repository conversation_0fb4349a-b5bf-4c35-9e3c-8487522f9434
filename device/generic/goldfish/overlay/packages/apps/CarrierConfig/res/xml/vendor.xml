<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<!-- This is a place for vendor-specific config values. The format and
     filtering rules are the same as those in carrier_config*.xml files. This
     file is read after any specific config file from the assets folder, so it
     must use explicit filters for MCC ad MNC if that is desired. -->

<carrier_config_list>
<carrier_config>
<boolean name="enabledMMS" value="false"/>
<boolean name="show_4g_for_lte_data_icon_bool" value="false"/>
</carrier_config>
</carrier_config_list>
