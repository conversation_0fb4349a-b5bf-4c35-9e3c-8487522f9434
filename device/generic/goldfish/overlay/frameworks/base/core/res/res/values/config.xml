<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Make this 'true' to allow the Emulator to control
         the state of the headphone/microphone jack -->
    <bool name="config_useDevInputEventForAudioJack">true</bool>

    <!--  Maximum number of supported users -->
    <integer name="config_multiuserMaximumUsers">4</integer>
    <!--  Whether Multiuser UI should be shown -->
    <bool name="config_enableMultiUserUI">true</bool>

    <!-- Set to true to add links to Cell Broadcast app from Settings and MMS app. -->
    <bool name="config_cellBroadcastAppLinks">true</bool>

    <!-- MMS user agent string -->
    <string name="config_mms_user_agent" translatable="false">GoldfishNexus</string>

    <!-- MMS user agent prolfile url -->
    <string name="config_mms_user_agent_profile_url" translatable="false">http://gsm.lge.com/html/gsm/Nexus5-M3.xml</string>

    <!-- Indicate whether closing the lid causes the device to enter the folded state which means
         to get a smaller screen and opening the lid causes the device to enter the unfolded state
         which means to get a larger screen. -->
    <bool name="config_lidControlsDisplayFold">true</bool>
</resources>
