/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef TRUSTY_KEYMASTER_TRUSTY_KEYMASTER_IPC_H_
#define TRUSTY_KEYMASTER_TRUSTY_KEYMASTER_IPC_H_

__BEGIN_DECLS

int trusty_keymaster_connect(void);
int trusty_keymaster_call(uint32_t cmd, void* in, uint32_t in_size, uint8_t* out,
                          uint32_t* out_size);
void trusty_keymaster_disconnect(void);

__END_DECLS

#endif  // TRUSTY_KEYMASTER_TRUSTY_KEYMASTER_IPC_H_
