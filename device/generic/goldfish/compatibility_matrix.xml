<compatibility-matrix version="1.0" type="device">
    <hal format="hidl" optional="false">
        <name>android.frameworks.schedulerservice</name>
        <version>1.0</version>
        <interface>
            <name>ISchedulingPolicyService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.frameworks.sensorservice</name>
        <version>1.0</version>
        <interface>
            <name>ISensorManager</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.allocator</name>
        <version>1.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>ashmem</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.manager</name>
        <version>1.0</version>
        <interface>
            <name>IServiceManager</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.memory</name>
        <version>1.0</version>
        <interface>
            <name>IMapper</name>
            <instance>ashmem</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.token</name>
        <version>1.0</version>
        <interface>
            <name>ITokenManager</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.system.wifi.keystore</name>
        <version>1.0</version>
        <interface>
            <name>IKeystore</name>
            <instance>default</instance>
        </interface>
    </hal>

</compatibility-matrix>
