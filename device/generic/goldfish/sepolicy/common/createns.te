# Network namespace creation
type createns, domain;
type createns_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(createns)

allow createns self:capability { sys_admin net_raw setuid setgid };
allow createns varrun_file:dir { add_name search write };
allow createns varrun_file:file { create mounton open read write };

#Allow createns itself to be run by init in its own domain
domain_auto_trans(goldfish_setup, createns_exec, createns);
allow createns goldfish_setup:fd use;

