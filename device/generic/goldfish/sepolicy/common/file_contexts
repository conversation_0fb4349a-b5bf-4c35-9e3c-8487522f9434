# goldfish
/dev/block/mtdblock0         u:object_r:system_block_device:s0
/dev/block/mtdblock1         u:object_r:userdata_block_device:s0
/dev/block/mtdblock2         u:object_r:cache_block_device:s0

# ranchu
/dev/block/vda               u:object_r:system_block_device:s0
/dev/block/vdb               u:object_r:cache_block_device:s0
/dev/block/vdc               u:object_r:userdata_block_device:s0
/dev/block/vdd               u:object_r:metadata_block_device:s0
/dev/block/vde               u:object_r:system_block_device:s0
/dev/block/zram0             u:object_r:swap_block_device:s0

/dev/goldfish_pipe           u:object_r:qemu_device:s0
/dev/goldfish_sync           u:object_r:qemu_device:s0
/dev/goldfish_address_space  u:object_r:qemu_device:s0
/dev/qemu_.*                 u:object_r:qemu_device:s0
/dev/ttyGF[0-9]*             u:object_r:serial_device:s0
/dev/ttyS2                   u:object_r:console_device:s0
/vendor/bin/init\.ranchu-core\.sh u:object_r:goldfish_setup_exec:s0
/vendor/bin/init\.ranchu-net\.sh u:object_r:goldfish_setup_exec:s0
/vendor/bin/init\.wifi\.sh   u:object_r:goldfish_setup_exec:s0
/vendor/bin/qemu-props       u:object_r:qemu_props_exec:s0
/vendor/bin/createns         u:object_r:createns_exec:s0
/vendor/bin/execns           u:object_r:execns_exec:s0
/vendor/bin/ipv6proxy        u:object_r:ipv6proxy_exec:s0
/vendor/bin/iw               u:object_r:goldfish_iw_exec:s0
/vendor/bin/dhcpclient       u:object_r:dhcpclient_exec:s0
/vendor/bin/dhcpserver       u:object_r:dhcpserver_exec:s0
/vendor/bin/hostapd_nohidl   u:object_r:hostapd_nohidl_exec:s0
/vendor/bin/netmgr           u:object_r:netmgr_exec:s0

/vendor/bin/hw/android\.hardware\.drm@1\.0-service\.widevine          u:object_r:hal_drm_widevine_exec:s0
/vendor/bin/hw/android\.hardware\.drm@1\.2-service\.widevine          u:object_r:hal_drm_widevine_exec:s0
/vendor/bin/hw/android\.hardware\.drm@1\.2-service\.clearkey          u:object_r:hal_drm_clearkey_exec:s0
/vendor/bin/hw/android\.hardware\.keymaster@4\.0-strongbox-service u:object_r:hal_keymaster_default_exec:s0
/vendor/bin/hw/android\.hardware\.health@2\.0-service.goldfish        u:object_r:hal_health_default_exec:s0
/vendor/bin/hw/android\.hardware\.power@1\.1-service.ranchu           u:object_r:hal_power_default_exec:s0
/vendor/bin/hw/android\.hardware\.thermal@2\.0-service.mock           u:object_r:hal_thermal_default_exec:s0

/vendor/lib(64)?/hw/vulkan\.ranchu\.so   u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/gralloc\.ranchu\.so   u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/gralloc\.goldfish\.default\.so   u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libEGL_emulation\.so          u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_CM_emulation\.so    u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_emulation\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libEGL_swiftshader\.so          u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_CM_swiftshader\.so    u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_swiftshader\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libOpenglCodecCommon\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libOpenglSystemCommon\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/lib_renderControl_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libvulkan_enc\.so       u:object_r:same_process_hal_file:s0

# data
/data/vendor/mediadrm(/.*)?            u:object_r:mediadrm_vendor_data_file:s0
/data/vendor/var/run(/.*)?             u:object_r:varrun_file:s0

