# IPv6 proxying
type ipv6proxy, domain;
type ipv6proxy_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(ipv6proxy)
net_domain(ipv6proxy)

# Allow ipv6proxy to be run by execns in its own domain
domain_auto_trans(execns, ipv6proxy_exec, ipv6proxy);
allow ipv6proxy execns:fd use;

set_prop(ipv6proxy, net_eth0_prop);
dontaudit ipv6proxy kernel:system module_request;
allow ipv6proxy self:capability { sys_admin sys_module net_admin net_raw };
allow ipv6proxy self:packet_socket { bind create read };
allow ipv6proxy self:netlink_route_socket nlmsg_write;
allow ipv6proxy varrun_file:dir search;
allowxperm ipv6proxy self:udp_socket ioctl { SIOCSIFFLAGS SIOCGIFHWADDR };
