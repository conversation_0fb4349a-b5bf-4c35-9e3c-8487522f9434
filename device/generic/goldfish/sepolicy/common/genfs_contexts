# On the emulator, device tree dir is configured to be
# /sys/bus/platform/devices/ANDR0001:00/properties/android/ which is a symlink to
# /sys/devices/platform/ANDR0001:00/properties/android/
genfscon sysfs /devices/platform/ANDR0001:00/properties/android u:object_r:sysfs_dt_firmware_android:s0

# We expect /sys/class/power_supply/* and everything it links to to be labeled
# as sysfs_batteryinfo.
genfscon sysfs /devices/platform/GFSH0001:00/power_supply u:object_r:sysfs_batteryinfo:s0

# /sys/class/rtc
genfscon sysfs /devices/pnp0/00:00/rtc u:object_r:sysfs_rtc:s0
genfscon sysfs /devices/platform/GFSH0007:00/rtc u:object_r:sysfs_rtc:s0

# /sys/class/net
genfscon sysfs /devices/pci0000:00/0000:00:08.0/virtio5/net u:object_r:sysfs_net:s0
genfscon sysfs /devices/virtual/mac80211_hwsim/hwsim0/net u:object_r:sysfs_net:s0
genfscon sysfs /devices/virtual/mac80211_hwsim/hwsim1/net u:object_r:sysfs_net:s0

# block devices
genfscon sysfs /devices/virtual/block/ram u:object_r:sysfs_devices_block:s0
genfscon sysfs /devices/virtual/block/md u:object_r:sysfs_devices_block:s0

genfscon sysfs /devices/pci0000:00/0000:00:03.0/virtio0/block/vda u:object_r:sysfs_virtio_block:s0
genfscon sysfs /devices/pci0000:00/0000:00:04.0/virtio1/block/vdb u:object_r:sysfs_virtio_block:s0
genfscon sysfs /devices/pci0000:00/0000:00:05.0/virtio2/block/vdc u:object_r:sysfs_virtio_block:s0
genfscon sysfs /devices/pci0000:00/0000:00:06.0/virtio3/block/vdd u:object_r:sysfs_virtio_block:s0
genfscon sysfs /devices/pci0000:00/0000:00:07.0/virtio4/block/vde u:object_r:sysfs_virtio_block:s0
genfscon sysfs /devices/pci0000:00/0000:00:08.0/virtio5/block/vdf u:object_r:sysfs_virtio_block:s0

# /sys/class/power_supply
genfscon sysfs /devices/platform/9020000.goldfish_battery/power_supply u:object_r:sysfs_batteryinfo:s0

# /proc/<pid>/ns
genfscon nsfs / u:object_r:nsfs:s0
