# goldfish-setup service: runs init.goldfish.sh script
type goldfish_setup, domain;
type goldfish_setup_exec, vendor_file_type, exec_type, file_type;

init_daemon_domain(goldfish_setup)

# TODO(b/79502552): Invalid property access from emulator vendor
#set_prop(goldfish_setup, debug_prop);
allow goldfish_setup self:capability { fowner chown net_admin net_raw };
allow goldfish_setup self:udp_socket { create ioctl };
allow goldfish_setup vendor_toolbox_exec:file execute_no_trans;
allow goldfish_setup vendor_file:file execute_no_trans;
allowxperm goldfish_setup self:udp_socket ioctl priv_sock_ioctls;
wakelock_use(goldfish_setup);
allow goldfish_setup vendor_shell_exec:file { rx_file_perms };
#============= goldfish_setup ==============
allow goldfish_setup goldfish_iw_exec:file execute_no_trans;

# Set system properties to start services
set_prop(goldfish_setup, ctl_default_prop);

# Set up WiFi
allow goldfish_setup self:netlink_route_socket { create nlmsg_write setopt bind getattr read write nlmsg_read };
allow goldfish_setup self:netlink_generic_socket create_socket_perms_no_ioctl;
allow goldfish_setup self:capability { sys_module sys_admin };
allow goldfish_setup varrun_file:dir { mounton open read write add_name search remove_name };
allow goldfish_setup varrun_file:file { mounton getattr create read write open unlink };
allow goldfish_setup execns_exec:file rx_file_perms;
allow goldfish_setup proc_net:file rw_file_perms;
allow goldfish_setup proc:file r_file_perms;
allow goldfish_setup nsfs:file r_file_perms;
allow goldfish_setup system_data_file:dir getattr;
allow goldfish_setup kernel:system module_request;
set_prop(goldfish_setup, qemu_prop);
get_prop(goldfish_setup, net_share_prop);
# Allow goldfish_setup to run /system/bin/ip and /system/bin/iw
# TODO(b/113124961): clean up this Treble violation.
typeattribute goldfish_setup vendor_executes_system_violators;
allow goldfish_setup system_file:file rx_file_perms;
# Allow goldfish_setup to run init.wifi.sh
allow goldfish_setup goldfish_setup_exec:file execute_no_trans;
#Allow goldfish_setup to run createns in its own domain
domain_auto_trans(goldfish_setup, createns_exec, createns);
# iw
allow goldfish_setup sysfs:file { read open };
# iptables
allow goldfish_setup self:rawip_socket { create getopt setopt };
# Allow goldfish_setup to read createns proc file to get the namespace file
allow goldfish_setup createns:file { read };
allow goldfish_setup createns:dir { search };
allow goldfish_setup createns:lnk_file { read };
# Allow goldfish_setup to copy the hostapd conf template to the vendor data dir
allow goldfish_setup hostapd_data_file:file create_file_perms;
allow goldfish_setup hostapd_data_file:dir rw_dir_perms;
allow goldfish_setup system_file:file { execute getattr open read };
