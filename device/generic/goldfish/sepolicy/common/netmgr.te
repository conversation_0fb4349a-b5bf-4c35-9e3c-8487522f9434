# Wifi manager
type netmgr, domain;
type netmgr_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(netmgr)
net_domain(netmgr)

allow netmgr execns:fd use;

# Set ctrl.restart property to restart hostapd when config changes
set_prop(netmgr, ctl_default_prop);
# Modify hostapd config file
allow netmgr hostapd_data_file:file rw_file_perms;
allow netmgr hostapd_data_file:dir rw_dir_perms;
# Assign addresses to new interfaces as hostapd brings them up
allow netmgr self:capability { net_raw net_admin };
allow netmgr self:socket { create ioctl };
allow netmgr self:packet_socket { ioctl getopt };
allow netmgr self:udp_socket { ioctl };
allow netmgr proc_net:file { read getattr open };
allowxperm netmgr self:socket ioctl { SIOCETHTOOL };
allowxperm netmgr self:udp_socket ioctl { SIOCSIFADDR SIOCSIFNETMASK SIOCSIFBRDADDR };
allowxperm netmgr self:packet_socket ioctl { SIOCGIFINDEX SIOCGIFHWADDR };

# Allow netmgr to run iptables to block and unblock network traffic
# TODO(b/113124961): clean up this Treble violation.
typeattribute netmgr vendor_executes_system_violators;
allow netmgr system_file:file execute_no_trans;
allow netmgr system_file:file lock;
# Packet socket for wifi forwarding
allow netmgr self:packet_socket { bind create read setopt write };
