// Generated Code - DO NOT EDIT !!
// generated by 'emugen'

#ifndef GUARD_gl_encoder_context_t
#define GUARD_gl_encoder_context_t

#include "IOStream.h"
#include "ChecksumCalculator.h"
#include "gl_client_context.h"


#include "glUtils.h"
#include "GLEncoderUtils.h"

struct gl_encoder_context_t : public gl_client_context_t {

	IOStream *m_stream;
	ChecksumCalculator *m_checksumCalculator;

	gl_encoder_context_t(IOStream *stream, ChecksumCalculator *checksumCalculator);
	virtual uint64_t lockAndWriteDma(void* data, uint32_t sz) { return 0; }
};

#endif  // GUARD_gl_encoder_context_t
