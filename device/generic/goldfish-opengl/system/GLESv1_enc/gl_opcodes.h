// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __GUARD_gl_opcodes_h_
#define __GUARD_gl_opcodes_h_

#define OP_glAlphaFunc 					1024
#define OP_glClearColor 					1025
#define OP_glClearDepthf 					1026
#define OP_glClipPlanef 					1027
#define OP_glColor4f 					1028
#define OP_glDepthRangef 					1029
#define OP_glFogf 					1030
#define OP_glFogfv 					1031
#define OP_glFrustumf 					1032
#define OP_glGetClipPlanef 					1033
#define OP_glGetFloatv 					1034
#define OP_glGetLightfv 					1035
#define OP_glGetMaterialfv 					1036
#define OP_glGetTexEnvfv 					1037
#define OP_glGetTexParameterfv 					1038
#define OP_glLightModelf 					1039
#define OP_glLightModelfv 					1040
#define OP_glLightf 					1041
#define OP_glLightfv 					1042
#define OP_glLineWidth 					1043
#define OP_glLoadMatrixf 					1044
#define OP_glMaterialf 					1045
#define OP_glMaterialfv 					1046
#define OP_glMultMatrixf 					1047
#define OP_glMultiTexCoord4f 					1048
#define OP_glNormal3f 					1049
#define OP_glOrthof 					1050
#define OP_glPointParameterf 					1051
#define OP_glPointParameterfv 					1052
#define OP_glPointSize 					1053
#define OP_glPolygonOffset 					1054
#define OP_glRotatef 					1055
#define OP_glScalef 					1056
#define OP_glTexEnvf 					1057
#define OP_glTexEnvfv 					1058
#define OP_glTexParameterf 					1059
#define OP_glTexParameterfv 					1060
#define OP_glTranslatef 					1061
#define OP_glActiveTexture 					1062
#define OP_glAlphaFuncx 					1063
#define OP_glBindBuffer 					1064
#define OP_glBindTexture 					1065
#define OP_glBlendFunc 					1066
#define OP_glBufferData 					1067
#define OP_glBufferSubData 					1068
#define OP_glClear 					1069
#define OP_glClearColorx 					1070
#define OP_glClearDepthx 					1071
#define OP_glClearStencil 					1072
#define OP_glClientActiveTexture 					1073
#define OP_glColor4ub 					1074
#define OP_glColor4x 					1075
#define OP_glColorMask 					1076
#define OP_glColorPointer 					1077
#define OP_glCompressedTexImage2D 					1078
#define OP_glCompressedTexSubImage2D 					1079
#define OP_glCopyTexImage2D 					1080
#define OP_glCopyTexSubImage2D 					1081
#define OP_glCullFace 					1082
#define OP_glDeleteBuffers 					1083
#define OP_glDeleteTextures 					1084
#define OP_glDepthFunc 					1085
#define OP_glDepthMask 					1086
#define OP_glDepthRangex 					1087
#define OP_glDisable 					1088
#define OP_glDisableClientState 					1089
#define OP_glDrawArrays 					1090
#define OP_glDrawElements 					1091
#define OP_glEnable 					1092
#define OP_glEnableClientState 					1093
#define OP_glFinish 					1094
#define OP_glFlush 					1095
#define OP_glFogx 					1096
#define OP_glFogxv 					1097
#define OP_glFrontFace 					1098
#define OP_glFrustumx 					1099
#define OP_glGetBooleanv 					1100
#define OP_glGetBufferParameteriv 					1101
#define OP_glClipPlanex 					1102
#define OP_glGenBuffers 					1103
#define OP_glGenTextures 					1104
#define OP_glGetError 					1105
#define OP_glGetFixedv 					1106
#define OP_glGetIntegerv 					1107
#define OP_glGetLightxv 					1108
#define OP_glGetMaterialxv 					1109
#define OP_glGetPointerv 					1110
#define OP_glGetString 					1111
#define OP_glGetTexEnviv 					1112
#define OP_glGetTexEnvxv 					1113
#define OP_glGetTexParameteriv 					1114
#define OP_glGetTexParameterxv 					1115
#define OP_glHint 					1116
#define OP_glIsBuffer 					1117
#define OP_glIsEnabled 					1118
#define OP_glIsTexture 					1119
#define OP_glLightModelx 					1120
#define OP_glLightModelxv 					1121
#define OP_glLightx 					1122
#define OP_glLightxv 					1123
#define OP_glLineWidthx 					1124
#define OP_glLoadIdentity 					1125
#define OP_glLoadMatrixx 					1126
#define OP_glLogicOp 					1127
#define OP_glMaterialx 					1128
#define OP_glMaterialxv 					1129
#define OP_glMatrixMode 					1130
#define OP_glMultMatrixx 					1131
#define OP_glMultiTexCoord4x 					1132
#define OP_glNormal3x 					1133
#define OP_glNormalPointer 					1134
#define OP_glOrthox 					1135
#define OP_glPixelStorei 					1136
#define OP_glPointParameterx 					1137
#define OP_glPointParameterxv 					1138
#define OP_glPointSizex 					1139
#define OP_glPolygonOffsetx 					1140
#define OP_glPopMatrix 					1141
#define OP_glPushMatrix 					1142
#define OP_glReadPixels 					1143
#define OP_glRotatex 					1144
#define OP_glSampleCoverage 					1145
#define OP_glSampleCoveragex 					1146
#define OP_glScalex 					1147
#define OP_glScissor 					1148
#define OP_glShadeModel 					1149
#define OP_glStencilFunc 					1150
#define OP_glStencilMask 					1151
#define OP_glStencilOp 					1152
#define OP_glTexCoordPointer 					1153
#define OP_glTexEnvi 					1154
#define OP_glTexEnvx 					1155
#define OP_glTexEnviv 					1156
#define OP_glTexEnvxv 					1157
#define OP_glTexImage2D 					1158
#define OP_glTexParameteri 					1159
#define OP_glTexParameterx 					1160
#define OP_glTexParameteriv 					1161
#define OP_glTexParameterxv 					1162
#define OP_glTexSubImage2D 					1163
#define OP_glTranslatex 					1164
#define OP_glVertexPointer 					1165
#define OP_glViewport 					1166
#define OP_glPointSizePointerOES 					1167
#define OP_glVertexPointerOffset 					1168
#define OP_glColorPointerOffset 					1169
#define OP_glNormalPointerOffset 					1170
#define OP_glPointSizePointerOffset 					1171
#define OP_glTexCoordPointerOffset 					1172
#define OP_glWeightPointerOffset 					1173
#define OP_glMatrixIndexPointerOffset 					1174
#define OP_glVertexPointerData 					1175
#define OP_glColorPointerData 					1176
#define OP_glNormalPointerData 					1177
#define OP_glTexCoordPointerData 					1178
#define OP_glPointSizePointerData 					1179
#define OP_glWeightPointerData 					1180
#define OP_glMatrixIndexPointerData 					1181
#define OP_glDrawElementsOffset 					1182
#define OP_glDrawElementsData 					1183
#define OP_glGetCompressedTextureFormats 					1184
#define OP_glFinishRoundTrip 					1185
#define OP_glBlendEquationSeparateOES 					1186
#define OP_glBlendFuncSeparateOES 					1187
#define OP_glBlendEquationOES 					1188
#define OP_glDrawTexsOES 					1189
#define OP_glDrawTexiOES 					1190
#define OP_glDrawTexxOES 					1191
#define OP_glDrawTexsvOES 					1192
#define OP_glDrawTexivOES 					1193
#define OP_glDrawTexxvOES 					1194
#define OP_glDrawTexfOES 					1195
#define OP_glDrawTexfvOES 					1196
#define OP_glEGLImageTargetTexture2DOES 					1197
#define OP_glEGLImageTargetRenderbufferStorageOES 					1198
#define OP_glAlphaFuncxOES 					1199
#define OP_glClearColorxOES 					1200
#define OP_glClearDepthxOES 					1201
#define OP_glClipPlanexOES 					1202
#define OP_glClipPlanexIMG 					1203
#define OP_glColor4xOES 					1204
#define OP_glDepthRangexOES 					1205
#define OP_glFogxOES 					1206
#define OP_glFogxvOES 					1207
#define OP_glFrustumxOES 					1208
#define OP_glGetClipPlanexOES 					1209
#define OP_glGetClipPlanex 					1210
#define OP_glGetFixedvOES 					1211
#define OP_glGetLightxvOES 					1212
#define OP_glGetMaterialxvOES 					1213
#define OP_glGetTexEnvxvOES 					1214
#define OP_glGetTexParameterxvOES 					1215
#define OP_glLightModelxOES 					1216
#define OP_glLightModelxvOES 					1217
#define OP_glLightxOES 					1218
#define OP_glLightxvOES 					1219
#define OP_glLineWidthxOES 					1220
#define OP_glLoadMatrixxOES 					1221
#define OP_glMaterialxOES 					1222
#define OP_glMaterialxvOES 					1223
#define OP_glMultMatrixxOES 					1224
#define OP_glMultiTexCoord4xOES 					1225
#define OP_glNormal3xOES 					1226
#define OP_glOrthoxOES 					1227
#define OP_glPointParameterxOES 					1228
#define OP_glPointParameterxvOES 					1229
#define OP_glPointSizexOES 					1230
#define OP_glPolygonOffsetxOES 					1231
#define OP_glRotatexOES 					1232
#define OP_glSampleCoveragexOES 					1233
#define OP_glScalexOES 					1234
#define OP_glTexEnvxOES 					1235
#define OP_glTexEnvxvOES 					1236
#define OP_glTexParameterxOES 					1237
#define OP_glTexParameterxvOES 					1238
#define OP_glTranslatexOES 					1239
#define OP_glIsRenderbufferOES 					1240
#define OP_glBindRenderbufferOES 					1241
#define OP_glDeleteRenderbuffersOES 					1242
#define OP_glGenRenderbuffersOES 					1243
#define OP_glRenderbufferStorageOES 					1244
#define OP_glGetRenderbufferParameterivOES 					1245
#define OP_glIsFramebufferOES 					1246
#define OP_glBindFramebufferOES 					1247
#define OP_glDeleteFramebuffersOES 					1248
#define OP_glGenFramebuffersOES 					1249
#define OP_glCheckFramebufferStatusOES 					1250
#define OP_glFramebufferRenderbufferOES 					1251
#define OP_glFramebufferTexture2DOES 					1252
#define OP_glGetFramebufferAttachmentParameterivOES 					1253
#define OP_glGenerateMipmapOES 					1254
#define OP_glMapBufferOES 					1255
#define OP_glUnmapBufferOES 					1256
#define OP_glGetBufferPointervOES 					1257
#define OP_glCurrentPaletteMatrixOES 					1258
#define OP_glLoadPaletteFromModelViewMatrixOES 					1259
#define OP_glMatrixIndexPointerOES 					1260
#define OP_glWeightPointerOES 					1261
#define OP_glQueryMatrixxOES 					1262
#define OP_glDepthRangefOES 					1263
#define OP_glFrustumfOES 					1264
#define OP_glOrthofOES 					1265
#define OP_glClipPlanefOES 					1266
#define OP_glClipPlanefIMG 					1267
#define OP_glGetClipPlanefOES 					1268
#define OP_glClearDepthfOES 					1269
#define OP_glTexGenfOES 					1270
#define OP_glTexGenfvOES 					1271
#define OP_glTexGeniOES 					1272
#define OP_glTexGenivOES 					1273
#define OP_glTexGenxOES 					1274
#define OP_glTexGenxvOES 					1275
#define OP_glGetTexGenfvOES 					1276
#define OP_glGetTexGenivOES 					1277
#define OP_glGetTexGenxvOES 					1278
#define OP_glBindVertexArrayOES 					1279
#define OP_glDeleteVertexArraysOES 					1280
#define OP_glGenVertexArraysOES 					1281
#define OP_glIsVertexArrayOES 					1282
#define OP_glDiscardFramebufferEXT 					1283
#define OP_glMultiDrawArraysEXT 					1284
#define OP_glMultiDrawElementsEXT 					1285
#define OP_glMultiDrawArraysSUN 					1286
#define OP_glMultiDrawElementsSUN 					1287
#define OP_glRenderbufferStorageMultisampleIMG 					1288
#define OP_glFramebufferTexture2DMultisampleIMG 					1289
#define OP_glDeleteFencesNV 					1290
#define OP_glGenFencesNV 					1291
#define OP_glIsFenceNV 					1292
#define OP_glTestFenceNV 					1293
#define OP_glGetFenceivNV 					1294
#define OP_glFinishFenceNV 					1295
#define OP_glSetFenceNV 					1296
#define OP_glGetDriverControlsQCOM 					1297
#define OP_glGetDriverControlStringQCOM 					1298
#define OP_glEnableDriverControlQCOM 					1299
#define OP_glDisableDriverControlQCOM 					1300
#define OP_glExtGetTexturesQCOM 					1301
#define OP_glExtGetBuffersQCOM 					1302
#define OP_glExtGetRenderbuffersQCOM 					1303
#define OP_glExtGetFramebuffersQCOM 					1304
#define OP_glExtGetTexLevelParameterivQCOM 					1305
#define OP_glExtTexObjectStateOverrideiQCOM 					1306
#define OP_glExtGetTexSubImageQCOM 					1307
#define OP_glExtGetBufferPointervQCOM 					1308
#define OP_glExtGetShadersQCOM 					1309
#define OP_glExtGetProgramsQCOM 					1310
#define OP_glExtIsProgramBinaryQCOM 					1311
#define OP_glExtGetProgramBinarySourceQCOM 					1312
#define OP_glStartTilingQCOM 					1313
#define OP_glEndTilingQCOM 					1314
#define OP_glGetGraphicsResetStatusEXT 					1315
#define OP_glReadnPixelsEXT 					1316
#define OP_last 					1317


#endif
