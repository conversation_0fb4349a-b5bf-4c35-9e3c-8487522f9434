// Generated Code - DO NOT EDIT !!
// generated by 'emugen'


#include <string.h>
#include "gl_opcodes.h"

#include "gl_enc.h"


#include <vector>

#include <stdio.h>

namespace {

void enc_unsupported()
{
	ALOGE("Function is unsupported\n");
}

void glAlphaFunc_enc(void *self , GLenum func, GLclampf ref)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glAlphaFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;
		memcpy(ptr, &ref, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearColor_enc(void *self , GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearColor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearDepthf_enc(void *self , GLclampf depth)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearDepthf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &depth, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClipPlanef_enc(void *self , GLenum plane, const GLfloat* equation)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_equation =  (4 * sizeof(float));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_equation + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClipPlanef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &plane, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_equation; ptr += 4;
	memcpy(ptr, equation, __size_equation);ptr += __size_equation;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColor4f_enc(void *self , GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColor4f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthRangef_enc(void *self , GLclampf zNear, GLclampf zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthRangef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFogf_enc(void *self , GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFogf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFogfv_enc(void *self , GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFogfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFrustumf_enc(void *self , GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFrustumf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetClipPlanef_enc(void *self , GLenum pname, GLfloat* eqn)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_eqn =  (4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetClipPlanef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_eqn; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(eqn, __size_eqn);
	if (useChecksum) checksumCalculator->addBuffer(eqn, __size_eqn);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetClipPlanef: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetFloatv_enc(void *self , GLenum pname, GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFloatv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFloatv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetLightfv_enc(void *self , GLenum light, GLenum pname, GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetLightfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetLightfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetMaterialfv_enc(void *self , GLenum face, GLenum pname, GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetMaterialfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetMaterialfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexEnvfv_enc(void *self , GLenum env, GLenum pname, GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexEnvfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &env, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexEnvfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexParameterfv_enc(void *self , GLenum target, GLenum pname, GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexParameterfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glLightModelf_enc(void *self , GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightModelf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightModelfv_enc(void *self , GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightModelfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightf_enc(void *self , GLenum light, GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightfv_enc(void *self , GLenum light, GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLineWidth_enc(void *self , GLfloat width)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLineWidth;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &width, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLoadMatrixf_enc(void *self , const GLfloat* m)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_m =  (16 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_m + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLoadMatrixf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_m; ptr += 4;
	memcpy(ptr, m, __size_m);ptr += __size_m;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMaterialf_enc(void *self , GLenum face, GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMaterialf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMaterialfv_enc(void *self , GLenum face, GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMaterialfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMultMatrixf_enc(void *self , const GLfloat* m)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_m =  (16 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_m + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMultMatrixf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_m; ptr += 4;
	memcpy(ptr, m, __size_m);ptr += __size_m;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMultiTexCoord4f_enc(void *self , GLenum target, GLfloat s, GLfloat t, GLfloat r, GLfloat q)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMultiTexCoord4f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &s, 4); ptr += 4;
		memcpy(ptr, &t, 4); ptr += 4;
		memcpy(ptr, &r, 4); ptr += 4;
		memcpy(ptr, &q, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glNormal3f_enc(void *self , GLfloat nx, GLfloat ny, GLfloat nz)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glNormal3f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &nx, 4); ptr += 4;
		memcpy(ptr, &ny, 4); ptr += 4;
		memcpy(ptr, &nz, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glOrthof_enc(void *self , GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glOrthof;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointParameterf_enc(void *self , GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointParameterf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointParameterfv_enc(void *self , GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointSize_enc(void *self , GLfloat size)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointSize;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPolygonOffset_enc(void *self , GLfloat factor, GLfloat units)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPolygonOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &factor, 4); ptr += 4;
		memcpy(ptr, &units, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glRotatef_enc(void *self , GLfloat angle, GLfloat x, GLfloat y, GLfloat z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRotatef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &angle, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glScalef_enc(void *self , GLfloat x, GLfloat y, GLfloat z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glScalef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvf_enc(void *self , GLenum target, GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvfv_enc(void *self , GLenum target, GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterf_enc(void *self , GLenum target, GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterfv_enc(void *self , GLenum target, GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTranslatef_enc(void *self , GLfloat x, GLfloat y, GLfloat z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTranslatef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glActiveTexture_enc(void *self , GLenum texture)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glActiveTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glAlphaFuncx_enc(void *self , GLenum func, GLclampx ref)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glAlphaFuncx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;
		memcpy(ptr, &ref, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindBuffer_enc(void *self , GLenum target, GLuint buffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &buffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindTexture_enc(void *self , GLenum target, GLuint texture)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendFunc_enc(void *self , GLenum sfactor, GLenum dfactor)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sfactor, 4); ptr += 4;
		memcpy(ptr, &dfactor, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBufferData_enc(void *self , GLenum target, GLsizeiptr size, const GLvoid* data, GLenum usage)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  size : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBufferData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	if (data != NULL) memcpy(ptr, data, __size_data);ptr += __size_data;
		memcpy(ptr, &usage, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBufferSubData_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr size, const GLvoid* data)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  size : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBufferSubData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	if (data != NULL) memcpy(ptr, data, __size_data);ptr += __size_data;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClear_enc(void *self , GLbitfield mask)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClear;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearColorx_enc(void *self , GLclampx red, GLclampx green, GLclampx blue, GLclampx alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearColorx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearDepthx_enc(void *self , GLclampx depth)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearDepthx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &depth, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearStencil_enc(void *self , GLint s)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearStencil;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &s, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClientActiveTexture_enc(void *self , GLenum texture)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClientActiveTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColor4ub_enc(void *self , GLubyte red, GLubyte green, GLubyte blue, GLubyte alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 1 + 1 + 1 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColor4ub;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 1); ptr += 1;
		memcpy(ptr, &green, 1); ptr += 1;
		memcpy(ptr, &blue, 1); ptr += 1;
		memcpy(ptr, &alpha, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColor4x_enc(void *self , GLfixed red, GLfixed green, GLfixed blue, GLfixed alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColor4x;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColorMask_enc(void *self , GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 1 + 1 + 1 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColorMask;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 1); ptr += 1;
		memcpy(ptr, &green, 1); ptr += 1;
		memcpy(ptr, &blue, 1); ptr += 1;
		memcpy(ptr, &alpha, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexImage2D_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, const GLvoid* data)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompressedTexImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	if (data != NULL) memcpy(ptr, data, __size_data);ptr += __size_data;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexSubImage2D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, const GLvoid* data)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompressedTexSubImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	if (data != NULL) memcpy(ptr, data, __size_data);ptr += __size_data;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCopyTexImage2D_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyTexImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCopyTexSubImage2D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyTexSubImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCullFace_enc(void *self , GLenum mode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCullFace;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteBuffers_enc(void *self , GLsizei n, const GLuint* buffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_buffers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteBuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffers; ptr += 4;
	memcpy(ptr, buffers, __size_buffers);ptr += __size_buffers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteTextures_enc(void *self , GLsizei n, const GLuint* textures)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_textures =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_textures + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteTextures;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_textures; ptr += 4;
	memcpy(ptr, textures, __size_textures);ptr += __size_textures;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthFunc_enc(void *self , GLenum func)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthMask_enc(void *self , GLboolean flag)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthMask;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &flag, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthRangex_enc(void *self , GLclampx zNear, GLclampx zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthRangex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDisable_enc(void *self , GLenum cap)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDisable;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &cap, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDisableClientState_enc(void *self , GLenum array)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDisableClientState;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawArrays_enc(void *self , GLenum mode, GLint first, GLsizei count)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawArrays;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &first, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEnable_enc(void *self , GLenum cap)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEnable;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &cap, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEnableClientState_enc(void *self , GLenum array)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEnableClientState;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFinish_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFinish;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFlush_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFlush;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFogx_enc(void *self , GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFogx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFogxv_enc(void *self , GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFogxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFrontFace_enc(void *self , GLenum mode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFrontFace;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFrustumx_enc(void *self , GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFrustumx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetBooleanv_enc(void *self , GLenum pname, GLboolean* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetBooleanv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetBooleanv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetBufferParameteriv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetBufferParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetBufferParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glClipPlanex_enc(void *self , GLenum pname, const GLfixed* eqn)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_eqn =  (4 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_eqn + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClipPlanex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_eqn; ptr += 4;
	memcpy(ptr, eqn, __size_eqn);ptr += __size_eqn;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenBuffers_enc(void *self , GLsizei n, GLuint* buffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenBuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(buffers, __size_buffers);
	if (useChecksum) checksumCalculator->addBuffer(buffers, __size_buffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenBuffers: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGenTextures_enc(void *self , GLsizei n, GLuint* textures)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_textures =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenTextures;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_textures; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(textures, __size_textures);
	if (useChecksum) checksumCalculator->addBuffer(textures, __size_textures);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenTextures: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLenum glGetError_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetError;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetError: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetFixedv_enc(void *self , GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFixedv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFixedv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetIntegerv_enc(void *self , GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetIntegerv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetIntegerv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetLightxv_enc(void *self , GLenum light, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetLightxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetLightxv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetMaterialxv_enc(void *self , GLenum face, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetMaterialxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetMaterialxv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexEnviv_enc(void *self , GLenum env, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexEnviv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &env, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexEnviv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexEnvxv_enc(void *self , GLenum env, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexEnvxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &env, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexEnvxv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexParameteriv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexParameterxv_enc(void *self , GLenum target, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexParameterxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexParameterxv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glHint_enc(void *self , GLenum target, GLenum mode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glHint;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsBuffer_enc(void *self , GLuint buffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &buffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsEnabled_enc(void *self , GLenum cap)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsEnabled;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &cap, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsEnabled: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsTexture_enc(void *self , GLuint texture)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsTexture: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glLightModelx_enc(void *self , GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightModelx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightModelxv_enc(void *self , GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightModelxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightx_enc(void *self , GLenum light, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightxv_enc(void *self , GLenum light, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLineWidthx_enc(void *self , GLfixed width)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLineWidthx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &width, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLoadIdentity_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLoadIdentity;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLoadMatrixx_enc(void *self , const GLfixed* m)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_m =  (16 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_m + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLoadMatrixx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_m; ptr += 4;
	memcpy(ptr, m, __size_m);ptr += __size_m;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLogicOp_enc(void *self , GLenum opcode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLogicOp;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &opcode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMaterialx_enc(void *self , GLenum face, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMaterialx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMaterialxv_enc(void *self , GLenum face, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMaterialxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMatrixMode_enc(void *self , GLenum mode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMatrixMode;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMultMatrixx_enc(void *self , const GLfixed* m)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_m =  (16 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_m + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMultMatrixx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_m; ptr += 4;
	memcpy(ptr, m, __size_m);ptr += __size_m;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMultiTexCoord4x_enc(void *self , GLenum target, GLfixed s, GLfixed t, GLfixed r, GLfixed q)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMultiTexCoord4x;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &s, 4); ptr += 4;
		memcpy(ptr, &t, 4); ptr += 4;
		memcpy(ptr, &r, 4); ptr += 4;
		memcpy(ptr, &q, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glNormal3x_enc(void *self , GLfixed nx, GLfixed ny, GLfixed nz)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glNormal3x;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &nx, 4); ptr += 4;
		memcpy(ptr, &ny, 4); ptr += 4;
		memcpy(ptr, &nz, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glOrthox_enc(void *self , GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glOrthox;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPixelStorei_enc(void *self , GLenum pname, GLint param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPixelStorei;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointParameterx_enc(void *self , GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointParameterx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointParameterxv_enc(void *self , GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointParameterxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointSizex_enc(void *self , GLfixed size)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointSizex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPolygonOffsetx_enc(void *self , GLfixed factor, GLfixed units)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPolygonOffsetx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &factor, 4); ptr += 4;
		memcpy(ptr, &units, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPopMatrix_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPopMatrix;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPushMatrix_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPushMatrix;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glReadPixels_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLvoid* pixels)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels =  glesv1_enc::pixelDataSize(self, width, height, format, type, 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReadPixels;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_pixels; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(pixels, __size_pixels);
	if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glReadPixels: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glRotatex_enc(void *self , GLfixed angle, GLfixed x, GLfixed y, GLfixed z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRotatex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &angle, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSampleCoverage_enc(void *self , GLclampf value, GLboolean invert)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSampleCoverage;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &value, 4); ptr += 4;
		memcpy(ptr, &invert, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSampleCoveragex_enc(void *self , GLclampx value, GLboolean invert)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSampleCoveragex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &value, 4); ptr += 4;
		memcpy(ptr, &invert, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glScalex_enc(void *self , GLfixed x, GLfixed y, GLfixed z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glScalex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glScissor_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glScissor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glShadeModel_enc(void *self , GLenum mode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glShadeModel;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilFunc_enc(void *self , GLenum func, GLint ref, GLuint mask)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;
		memcpy(ptr, &ref, 4); ptr += 4;
		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilMask_enc(void *self , GLuint mask)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilMask;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilOp_enc(void *self , GLenum fail, GLenum zfail, GLenum zpass)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilOp;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fail, 4); ptr += 4;
		memcpy(ptr, &zfail, 4); ptr += 4;
		memcpy(ptr, &zpass, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvi_enc(void *self , GLenum target, GLenum pname, GLint param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvi;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvx_enc(void *self , GLenum target, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnviv_enc(void *self , GLenum target, GLenum pname, const GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnviv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvxv_enc(void *self , GLenum target, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexImage2D_enc(void *self , GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, const GLvoid* pixels)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels = ((pixels != NULL) ?  glesv1_enc::pixelDataSize(self, width, height, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
	if (pixels != NULL) {
		stream->writeFully(pixels, __size_pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glTexParameteri_enc(void *self , GLenum target, GLenum pname, GLint param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameteri;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterx_enc(void *self , GLenum target, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterx;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameteriv_enc(void *self , GLenum target, GLenum pname, const GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterxv_enc(void *self , GLenum target, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterxv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexSubImage2D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, const GLvoid* pixels)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels = ((pixels != NULL) ?  glesv1_enc::pixelDataSize(self, width, height, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexSubImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
	if (pixels != NULL) {
		stream->writeFully(pixels, __size_pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glTranslatex_enc(void *self , GLfixed x, GLfixed y, GLfixed z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTranslatex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glViewport_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glViewport;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexPointerOffset_enc(void *self , GLint size, GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColorPointerOffset_enc(void *self , GLint size, GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColorPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glNormalPointerOffset_enc(void *self , GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glNormalPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointSizePointerOffset_enc(void *self , GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointSizePointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexCoordPointerOffset_enc(void *self , GLint size, GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexCoordPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glWeightPointerOffset_enc(void *self , GLint size, GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glWeightPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMatrixIndexPointerOffset_enc(void *self , GLint size, GLenum type, GLsizei stride, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMatrixIndexPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexPointerData_enc(void *self , GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColorPointerData_enc(void *self , GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColorPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glNormalPointerData_enc(void *self , GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glNormalPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, 3, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexCoordPointerData_enc(void *self , GLint unit, GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexCoordPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &unit, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointSizePointerData_enc(void *self , GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointSizePointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, 1, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glWeightPointerData_enc(void *self , GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glWeightPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char*)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMatrixIndexPointerData_enc(void *self , GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMatrixIndexPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char*)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsOffset_enc(void *self , GLenum mode, GLsizei count, GLenum type, GLuint offset)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsData_enc(void *self , GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	memcpy(ptr, data, __size_data);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetCompressedTextureFormats_enc(void *self , int count, GLint* formats)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_formats =  (count * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetCompressedTextureFormats;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_formats; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(formats, __size_formats);
	if (useChecksum) checksumCalculator->addBuffer(formats, __size_formats);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetCompressedTextureFormats: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

int glFinishRoundTrip_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFinishRoundTrip;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glFinishRoundTrip: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glBlendEquationSeparateOES_enc(void *self , GLenum modeRGB, GLenum modeAlpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendEquationSeparateOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &modeRGB, 4); ptr += 4;
		memcpy(ptr, &modeAlpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendFuncSeparateOES_enc(void *self , GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendFuncSeparateOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &srcRGB, 4); ptr += 4;
		memcpy(ptr, &dstRGB, 4); ptr += 4;
		memcpy(ptr, &srcAlpha, 4); ptr += 4;
		memcpy(ptr, &dstAlpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendEquationOES_enc(void *self , GLenum mode)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendEquationOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexsOES_enc(void *self , GLshort x, GLshort y, GLshort z, GLshort width, GLshort height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 2 + 2 + 2 + 2 + 2;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexsOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 2); ptr += 2;
		memcpy(ptr, &y, 2); ptr += 2;
		memcpy(ptr, &z, 2); ptr += 2;
		memcpy(ptr, &width, 2); ptr += 2;
		memcpy(ptr, &height, 2); ptr += 2;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexiOES_enc(void *self , GLint x, GLint y, GLint z, GLint width, GLint height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexiOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexxOES_enc(void *self , GLfixed x, GLfixed y, GLfixed z, GLfixed width, GLfixed height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexsvOES_enc(void *self , const GLshort* coords)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_coords =  (5 * sizeof(GLshort));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_coords + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexsvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_coords; ptr += 4;
	memcpy(ptr, coords, __size_coords);ptr += __size_coords;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexivOES_enc(void *self , const GLint* coords)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_coords =  (5 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_coords + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexivOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_coords; ptr += 4;
	memcpy(ptr, coords, __size_coords);ptr += __size_coords;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexxvOES_enc(void *self , const GLfixed* coords)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_coords =  (5 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_coords + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_coords; ptr += 4;
	memcpy(ptr, coords, __size_coords);ptr += __size_coords;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexfOES_enc(void *self , GLfloat x, GLfloat y, GLfloat z, GLfloat width, GLfloat height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexfOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawTexfvOES_enc(void *self , const GLfloat* coords)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_coords =  (5 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_coords + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawTexfvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_coords; ptr += 4;
	memcpy(ptr, coords, __size_coords);ptr += __size_coords;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEGLImageTargetTexture2DOES_enc(void *self , GLenum target, GLeglImageOES image)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEGLImageTargetTexture2DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &image, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEGLImageTargetRenderbufferStorageOES_enc(void *self , GLenum target, GLeglImageOES image)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEGLImageTargetRenderbufferStorageOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &image, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glAlphaFuncxOES_enc(void *self , GLenum func, GLclampx ref)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glAlphaFuncxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;
		memcpy(ptr, &ref, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearColorxOES_enc(void *self , GLclampx red, GLclampx green, GLclampx blue, GLclampx alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearColorxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearDepthxOES_enc(void *self , GLclampx depth)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearDepthxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &depth, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClipPlanexOES_enc(void *self , GLenum plane, const GLfixed* equation)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_equation =  (4 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_equation + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClipPlanexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &plane, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_equation; ptr += 4;
	memcpy(ptr, equation, __size_equation);ptr += __size_equation;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClipPlanexIMG_enc(void *self , GLenum plane, const GLfixed* equation)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_equation =  (4 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_equation + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClipPlanexIMG;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &plane, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_equation; ptr += 4;
	memcpy(ptr, equation, __size_equation);ptr += __size_equation;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColor4xOES_enc(void *self , GLfixed red, GLfixed green, GLfixed blue, GLfixed alpha)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColor4xOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthRangexOES_enc(void *self , GLclampx zNear, GLclampx zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthRangexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFogxOES_enc(void *self , GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFogxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFogxvOES_enc(void *self , GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFogxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFrustumxOES_enc(void *self , GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFrustumxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetClipPlanexOES_enc(void *self , GLenum pname, GLfixed* eqn)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_eqn =  (4 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetClipPlanexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_eqn; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(eqn, __size_eqn);
	if (useChecksum) checksumCalculator->addBuffer(eqn, __size_eqn);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetClipPlanexOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetClipPlanex_enc(void *self , GLenum pname, GLfixed* eqn)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_eqn =  (4 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetClipPlanex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_eqn; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(eqn, __size_eqn);
	if (useChecksum) checksumCalculator->addBuffer(eqn, __size_eqn);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetClipPlanex: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetFixedvOES_enc(void *self , GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFixedvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFixedvOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetLightxvOES_enc(void *self , GLenum light, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetLightxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetLightxvOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetMaterialxvOES_enc(void *self , GLenum face, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetMaterialxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetMaterialxvOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexEnvxvOES_enc(void *self , GLenum env, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexEnvxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &env, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexEnvxvOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexParameterxvOES_enc(void *self , GLenum target, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexParameterxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexParameterxvOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glLightModelxOES_enc(void *self , GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightModelxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightModelxvOES_enc(void *self , GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightModelxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightxOES_enc(void *self , GLenum light, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLightxvOES_enc(void *self , GLenum light, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLightxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &light, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLineWidthxOES_enc(void *self , GLfixed width)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLineWidthxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &width, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLoadMatrixxOES_enc(void *self , const GLfixed* m)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_m =  (16 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_m + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLoadMatrixxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_m; ptr += 4;
	memcpy(ptr, m, __size_m);ptr += __size_m;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMaterialxOES_enc(void *self , GLenum face, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMaterialxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMaterialxvOES_enc(void *self , GLenum face, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMaterialxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMultMatrixxOES_enc(void *self , const GLfixed* m)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_m =  (16 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_m + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMultMatrixxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_m; ptr += 4;
	memcpy(ptr, m, __size_m);ptr += __size_m;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMultiTexCoord4xOES_enc(void *self , GLenum target, GLfixed s, GLfixed t, GLfixed r, GLfixed q)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMultiTexCoord4xOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &s, 4); ptr += 4;
		memcpy(ptr, &t, 4); ptr += 4;
		memcpy(ptr, &r, 4); ptr += 4;
		memcpy(ptr, &q, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glNormal3xOES_enc(void *self , GLfixed nx, GLfixed ny, GLfixed nz)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glNormal3xOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &nx, 4); ptr += 4;
		memcpy(ptr, &ny, 4); ptr += 4;
		memcpy(ptr, &nz, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glOrthoxOES_enc(void *self , GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glOrthoxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointParameterxOES_enc(void *self , GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointParameterxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointParameterxvOES_enc(void *self , GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointParameterxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPointSizexOES_enc(void *self , GLfixed size)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPointSizexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPolygonOffsetxOES_enc(void *self , GLfixed factor, GLfixed units)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPolygonOffsetxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &factor, 4); ptr += 4;
		memcpy(ptr, &units, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glRotatexOES_enc(void *self , GLfixed angle, GLfixed x, GLfixed y, GLfixed z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRotatexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &angle, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSampleCoveragexOES_enc(void *self , GLclampx value, GLboolean invert)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSampleCoveragexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &value, 4); ptr += 4;
		memcpy(ptr, &invert, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glScalexOES_enc(void *self , GLfixed x, GLfixed y, GLfixed z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glScalexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvxOES_enc(void *self , GLenum target, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexEnvxvOES_enc(void *self , GLenum target, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexEnvxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterxOES_enc(void *self , GLenum target, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterxvOES_enc(void *self , GLenum target, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTranslatexOES_enc(void *self , GLfixed x, GLfixed y, GLfixed z)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTranslatexOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsRenderbufferOES_enc(void *self , GLuint renderbuffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsRenderbufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &renderbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsRenderbufferOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glBindRenderbufferOES_enc(void *self , GLenum target, GLuint renderbuffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindRenderbufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &renderbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteRenderbuffersOES_enc(void *self , GLsizei n, const GLuint* renderbuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_renderbuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_renderbuffers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteRenderbuffersOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_renderbuffers; ptr += 4;
	memcpy(ptr, renderbuffers, __size_renderbuffers);ptr += __size_renderbuffers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenRenderbuffersOES_enc(void *self , GLsizei n, GLuint* renderbuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_renderbuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenRenderbuffersOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_renderbuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(renderbuffers, __size_renderbuffers);
	if (useChecksum) checksumCalculator->addBuffer(renderbuffers, __size_renderbuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenRenderbuffersOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glRenderbufferStorageOES_enc(void *self , GLenum target, GLenum internalformat, GLsizei width, GLsizei height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRenderbufferStorageOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetRenderbufferParameterivOES_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetRenderbufferParameterivOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetRenderbufferParameterivOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLboolean glIsFramebufferOES_enc(void *self , GLuint framebuffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsFramebufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &framebuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsFramebufferOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glBindFramebufferOES_enc(void *self , GLenum target, GLuint framebuffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindFramebufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &framebuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteFramebuffersOES_enc(void *self , GLsizei n, const GLuint* framebuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_framebuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_framebuffers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteFramebuffersOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_framebuffers; ptr += 4;
	memcpy(ptr, framebuffers, __size_framebuffers);ptr += __size_framebuffers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenFramebuffersOES_enc(void *self , GLsizei n, GLuint* framebuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_framebuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenFramebuffersOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_framebuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(framebuffers, __size_framebuffers);
	if (useChecksum) checksumCalculator->addBuffer(framebuffers, __size_framebuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenFramebuffersOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLenum glCheckFramebufferStatusOES_enc(void *self , GLenum target)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCheckFramebufferStatusOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glCheckFramebufferStatusOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glFramebufferRenderbufferOES_enc(void *self , GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferRenderbufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &renderbuffertarget, 4); ptr += 4;
		memcpy(ptr, &renderbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFramebufferTexture2DOES_enc(void *self , GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferTexture2DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &textarget, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetFramebufferAttachmentParameterivOES_enc(void *self , GLenum target, GLenum attachment, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFramebufferAttachmentParameterivOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFramebufferAttachmentParameterivOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGenerateMipmapOES_enc(void *self , GLenum target)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenerateMipmapOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glUnmapBufferOES_enc(void *self , GLenum target)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUnmapBufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glUnmapBufferOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glCurrentPaletteMatrixOES_enc(void *self , GLuint matrixpaletteindex)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCurrentPaletteMatrixOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &matrixpaletteindex, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLoadPaletteFromModelViewMatrixOES_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLoadPaletteFromModelViewMatrixOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLbitfield glQueryMatrixxOES_enc(void *self , GLfixed* mantissa, GLint* exponent)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_mantissa =  (16 * sizeof(GLfixed));
	const unsigned int __size_exponent =  (16 * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glQueryMatrixxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_mantissa; ptr += 4;
	*(unsigned int *)(ptr) = __size_exponent; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(mantissa, __size_mantissa);
	if (useChecksum) checksumCalculator->addBuffer(mantissa, __size_mantissa);
	stream->readback(exponent, __size_exponent);
	if (useChecksum) checksumCalculator->addBuffer(exponent, __size_exponent);

	GLbitfield retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glQueryMatrixxOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glDepthRangefOES_enc(void *self , GLclampf zNear, GLclampf zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthRangefOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFrustumfOES_enc(void *self , GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFrustumfOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glOrthofOES_enc(void *self , GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glOrthofOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &left, 4); ptr += 4;
		memcpy(ptr, &right, 4); ptr += 4;
		memcpy(ptr, &bottom, 4); ptr += 4;
		memcpy(ptr, &top, 4); ptr += 4;
		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClipPlanefOES_enc(void *self , GLenum plane, const GLfloat* equation)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_equation =  (4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_equation + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClipPlanefOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &plane, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_equation; ptr += 4;
	memcpy(ptr, equation, __size_equation);ptr += __size_equation;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClipPlanefIMG_enc(void *self , GLenum plane, const GLfloat* equation)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_equation =  (4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_equation + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClipPlanefIMG;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &plane, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_equation; ptr += 4;
	memcpy(ptr, equation, __size_equation);ptr += __size_equation;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetClipPlanefOES_enc(void *self , GLenum pname, GLfloat* eqn)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_eqn =  (4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetClipPlanefOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_eqn; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(eqn, __size_eqn);
	if (useChecksum) checksumCalculator->addBuffer(eqn, __size_eqn);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetClipPlanefOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glClearDepthfOES_enc(void *self , GLclampf depth)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearDepthfOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &depth, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexGenfOES_enc(void *self , GLenum coord, GLenum pname, GLfloat param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexGenfOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexGenfvOES_enc(void *self , GLenum coord, GLenum pname, const GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexGenfvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexGeniOES_enc(void *self , GLenum coord, GLenum pname, GLint param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexGeniOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexGenivOES_enc(void *self , GLenum coord, GLenum pname, const GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexGenivOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexGenxOES_enc(void *self , GLenum coord, GLenum pname, GLfixed param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexGenxOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexGenxvOES_enc(void *self , GLenum coord, GLenum pname, const GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexGenxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetTexGenfvOES_enc(void *self , GLenum coord, GLenum pname, GLfloat* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexGenfvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetTexGenivOES_enc(void *self , GLenum coord, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexGenivOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetTexGenxvOES_enc(void *self , GLenum coord, GLenum pname, GLfixed* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfixed));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexGenxvOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &coord, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindVertexArrayOES_enc(void *self , GLuint array)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindVertexArrayOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteVertexArraysOES_enc(void *self , GLsizei n, const GLuint* arrays)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_arrays =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_arrays + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteVertexArraysOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_arrays; ptr += 4;
	memcpy(ptr, arrays, __size_arrays);ptr += __size_arrays;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenVertexArraysOES_enc(void *self , GLsizei n, GLuint* arrays)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_arrays =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenVertexArraysOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_arrays; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(arrays, __size_arrays);
	if (useChecksum) checksumCalculator->addBuffer(arrays, __size_arrays);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenVertexArraysOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLboolean glIsVertexArrayOES_enc(void *self , GLuint array)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsVertexArrayOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsVertexArrayOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glDiscardFramebufferEXT_enc(void *self , GLenum target, GLsizei numAttachments, const GLenum* attachments)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_attachments =  (numAttachments * sizeof(const GLenum));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_attachments + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDiscardFramebufferEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &numAttachments, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_attachments; ptr += 4;
	memcpy(ptr, attachments, __size_attachments);ptr += __size_attachments;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glRenderbufferStorageMultisampleIMG_enc(void *self , GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRenderbufferStorageMultisampleIMG;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &samples, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFramebufferTexture2DMultisampleIMG_enc(void *self , GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLsizei samples)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferTexture2DMultisampleIMG;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &textarget, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &samples, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteFencesNV_enc(void *self , GLsizei n, const GLuint* fences)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_fences =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_fences + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteFencesNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_fences; ptr += 4;
	memcpy(ptr, fences, __size_fences);ptr += __size_fences;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenFencesNV_enc(void *self , GLsizei n, GLuint* fences)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_fences =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_fences + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenFencesNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_fences; ptr += 4;
	memcpy(ptr, fences, __size_fences);ptr += __size_fences;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsFenceNV_enc(void *self , GLuint fence)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsFenceNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fence, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsFenceNV: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glTestFenceNV_enc(void *self , GLuint fence)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTestFenceNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fence, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glTestFenceNV: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetFenceivNV_enc(void *self , GLuint fence, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFenceivNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fence, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFenceivNV: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glFinishFenceNV_enc(void *self , GLuint fence)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFinishFenceNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fence, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSetFenceNV_enc(void *self , GLuint fence, GLenum condition)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSetFenceNV;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fence, 4); ptr += 4;
		memcpy(ptr, &condition, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetDriverControlsQCOM_enc(void *self , GLint* num, GLsizei size, GLuint* driverControls)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_num =  (1 * sizeof(GLint));
	const unsigned int __size_driverControls =  (size * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetDriverControlsQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_num; ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_driverControls; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(num, __size_num);
	if (useChecksum) checksumCalculator->addBuffer(num, __size_num);
	stream->readback(driverControls, __size_driverControls);
	if (useChecksum) checksumCalculator->addBuffer(driverControls, __size_driverControls);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetDriverControlsQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetDriverControlStringQCOM_enc(void *self , GLuint driverControl, GLsizei bufSize, GLsizei* length, GLchar* driverControlString)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length =  (1 * sizeof(GLsizei));
	const unsigned int __size_driverControlString =  (1 * sizeof(GLchar));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetDriverControlStringQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &driverControl, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_driverControlString; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(length, __size_length);
	if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	stream->readback(driverControlString, __size_driverControlString);
	if (useChecksum) checksumCalculator->addBuffer(driverControlString, __size_driverControlString);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetDriverControlStringQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glEnableDriverControlQCOM_enc(void *self , GLuint driverControl)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEnableDriverControlQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &driverControl, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDisableDriverControlQCOM_enc(void *self , GLuint driverControl)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDisableDriverControlQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &driverControl, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glExtGetTexturesQCOM_enc(void *self , GLuint* textures, GLint maxTextures, GLint* numTextures)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_textures =  (maxTextures * sizeof(GLuint));
	const unsigned int __size_numTextures =  (1 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetTexturesQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_textures; ptr += 4;
		memcpy(ptr, &maxTextures, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_numTextures; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(textures, __size_textures);
	if (useChecksum) checksumCalculator->addBuffer(textures, __size_textures);
	stream->readback(numTextures, __size_numTextures);
	if (useChecksum) checksumCalculator->addBuffer(numTextures, __size_numTextures);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetTexturesQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtGetBuffersQCOM_enc(void *self , GLuint* buffers, GLint maxBuffers, GLint* numBuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffers =  (maxBuffers * sizeof(GLuint));
	const unsigned int __size_numBuffers =  (1 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetBuffersQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_buffers; ptr += 4;
		memcpy(ptr, &maxBuffers, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_numBuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(buffers, __size_buffers);
	if (useChecksum) checksumCalculator->addBuffer(buffers, __size_buffers);
	stream->readback(numBuffers, __size_numBuffers);
	if (useChecksum) checksumCalculator->addBuffer(numBuffers, __size_numBuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetBuffersQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtGetRenderbuffersQCOM_enc(void *self , GLuint* renderbuffers, GLint maxRenderbuffers, GLint* numRenderbuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_renderbuffers =  (maxRenderbuffers * sizeof(GLuint));
	const unsigned int __size_numRenderbuffers =  (1 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetRenderbuffersQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_renderbuffers; ptr += 4;
		memcpy(ptr, &maxRenderbuffers, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_numRenderbuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(renderbuffers, __size_renderbuffers);
	if (useChecksum) checksumCalculator->addBuffer(renderbuffers, __size_renderbuffers);
	stream->readback(numRenderbuffers, __size_numRenderbuffers);
	if (useChecksum) checksumCalculator->addBuffer(numRenderbuffers, __size_numRenderbuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetRenderbuffersQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtGetFramebuffersQCOM_enc(void *self , GLuint* framebuffers, GLint maxFramebuffers, GLint* numFramebuffers)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_framebuffers =  (maxFramebuffers * sizeof(GLuint));
	const unsigned int __size_numFramebuffers =  (1 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetFramebuffersQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_framebuffers; ptr += 4;
		memcpy(ptr, &maxFramebuffers, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_numFramebuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(framebuffers, __size_framebuffers);
	if (useChecksum) checksumCalculator->addBuffer(framebuffers, __size_framebuffers);
	stream->readback(numFramebuffers, __size_numFramebuffers);
	if (useChecksum) checksumCalculator->addBuffer(numFramebuffers, __size_numFramebuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetFramebuffersQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtGetTexLevelParameterivQCOM_enc(void *self , GLuint texture, GLenum face, GLint level, GLenum pname, GLint* params)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetTexLevelParameterivQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetTexLevelParameterivQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtTexObjectStateOverrideiQCOM_enc(void *self , GLenum target, GLenum pname, GLint param)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtTexObjectStateOverrideiQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glExtGetTexSubImageQCOM_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLvoid* texels)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_texels =  (depth * glesv1_enc::pixelDataSize(self, width, height, format, type, 0));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetTexSubImageQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_texels; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(texels, __size_texels);
	if (useChecksum) checksumCalculator->addBuffer(texels, __size_texels);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetTexSubImageQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtGetShadersQCOM_enc(void *self , GLuint* shaders, GLint maxShaders, GLint* numShaders)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_shaders =  (maxShaders * sizeof(GLuint));
	const unsigned int __size_numShaders =  (1 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetShadersQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_shaders; ptr += 4;
		memcpy(ptr, &maxShaders, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_numShaders; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(shaders, __size_shaders);
	if (useChecksum) checksumCalculator->addBuffer(shaders, __size_shaders);
	stream->readback(numShaders, __size_numShaders);
	if (useChecksum) checksumCalculator->addBuffer(numShaders, __size_numShaders);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetShadersQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glExtGetProgramsQCOM_enc(void *self , GLuint* programs, GLint maxPrograms, GLint* numPrograms)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_programs =  (maxPrograms * sizeof(GLuint));
	const unsigned int __size_numPrograms =  (1 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtGetProgramsQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_programs; ptr += 4;
		memcpy(ptr, &maxPrograms, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_numPrograms; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(programs, __size_programs);
	if (useChecksum) checksumCalculator->addBuffer(programs, __size_programs);
	stream->readback(numPrograms, __size_numPrograms);
	if (useChecksum) checksumCalculator->addBuffer(numPrograms, __size_numPrograms);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtGetProgramsQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLboolean glExtIsProgramBinaryQCOM_enc(void *self , GLuint program)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glExtIsProgramBinaryQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glExtIsProgramBinaryQCOM: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glStartTilingQCOM_enc(void *self , GLuint x, GLuint y, GLuint width, GLuint height, GLbitfield preserveMask)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStartTilingQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &preserveMask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEndTilingQCOM_enc(void *self , GLbitfield preserveMask)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEndTilingQCOM;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &preserveMask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLenum glGetGraphicsResetStatusEXT_enc(void *self )
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetGraphicsResetStatusEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetGraphicsResetStatusEXT: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glReadnPixelsEXT_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLsizei bufSize, GLvoid* data)
{

	gl_encoder_context_t *ctx = (gl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReadnPixelsEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(data, __size_data);
	if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glReadnPixelsEXT: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

}  // namespace

gl_encoder_context_t::gl_encoder_context_t(IOStream *stream, ChecksumCalculator *checksumCalculator)
{
	m_stream = stream;
	m_checksumCalculator = checksumCalculator;

	this->glAlphaFunc = &glAlphaFunc_enc;
	this->glClearColor = &glClearColor_enc;
	this->glClearDepthf = &glClearDepthf_enc;
	this->glClipPlanef = &glClipPlanef_enc;
	this->glColor4f = &glColor4f_enc;
	this->glDepthRangef = &glDepthRangef_enc;
	this->glFogf = &glFogf_enc;
	this->glFogfv = &glFogfv_enc;
	this->glFrustumf = &glFrustumf_enc;
	this->glGetClipPlanef = &glGetClipPlanef_enc;
	this->glGetFloatv = &glGetFloatv_enc;
	this->glGetLightfv = &glGetLightfv_enc;
	this->glGetMaterialfv = &glGetMaterialfv_enc;
	this->glGetTexEnvfv = &glGetTexEnvfv_enc;
	this->glGetTexParameterfv = &glGetTexParameterfv_enc;
	this->glLightModelf = &glLightModelf_enc;
	this->glLightModelfv = &glLightModelfv_enc;
	this->glLightf = &glLightf_enc;
	this->glLightfv = &glLightfv_enc;
	this->glLineWidth = &glLineWidth_enc;
	this->glLoadMatrixf = &glLoadMatrixf_enc;
	this->glMaterialf = &glMaterialf_enc;
	this->glMaterialfv = &glMaterialfv_enc;
	this->glMultMatrixf = &glMultMatrixf_enc;
	this->glMultiTexCoord4f = &glMultiTexCoord4f_enc;
	this->glNormal3f = &glNormal3f_enc;
	this->glOrthof = &glOrthof_enc;
	this->glPointParameterf = &glPointParameterf_enc;
	this->glPointParameterfv = &glPointParameterfv_enc;
	this->glPointSize = &glPointSize_enc;
	this->glPolygonOffset = &glPolygonOffset_enc;
	this->glRotatef = &glRotatef_enc;
	this->glScalef = &glScalef_enc;
	this->glTexEnvf = &glTexEnvf_enc;
	this->glTexEnvfv = &glTexEnvfv_enc;
	this->glTexParameterf = &glTexParameterf_enc;
	this->glTexParameterfv = &glTexParameterfv_enc;
	this->glTranslatef = &glTranslatef_enc;
	this->glActiveTexture = &glActiveTexture_enc;
	this->glAlphaFuncx = &glAlphaFuncx_enc;
	this->glBindBuffer = &glBindBuffer_enc;
	this->glBindTexture = &glBindTexture_enc;
	this->glBlendFunc = &glBlendFunc_enc;
	this->glBufferData = &glBufferData_enc;
	this->glBufferSubData = &glBufferSubData_enc;
	this->glClear = &glClear_enc;
	this->glClearColorx = &glClearColorx_enc;
	this->glClearDepthx = &glClearDepthx_enc;
	this->glClearStencil = &glClearStencil_enc;
	this->glClientActiveTexture = &glClientActiveTexture_enc;
	this->glColor4ub = &glColor4ub_enc;
	this->glColor4x = &glColor4x_enc;
	this->glColorMask = &glColorMask_enc;
	this->glColorPointer = (glColorPointer_client_proc_t) &enc_unsupported;
	this->glCompressedTexImage2D = &glCompressedTexImage2D_enc;
	this->glCompressedTexSubImage2D = &glCompressedTexSubImage2D_enc;
	this->glCopyTexImage2D = &glCopyTexImage2D_enc;
	this->glCopyTexSubImage2D = &glCopyTexSubImage2D_enc;
	this->glCullFace = &glCullFace_enc;
	this->glDeleteBuffers = &glDeleteBuffers_enc;
	this->glDeleteTextures = &glDeleteTextures_enc;
	this->glDepthFunc = &glDepthFunc_enc;
	this->glDepthMask = &glDepthMask_enc;
	this->glDepthRangex = &glDepthRangex_enc;
	this->glDisable = &glDisable_enc;
	this->glDisableClientState = &glDisableClientState_enc;
	this->glDrawArrays = &glDrawArrays_enc;
	this->glDrawElements = (glDrawElements_client_proc_t) &enc_unsupported;
	this->glEnable = &glEnable_enc;
	this->glEnableClientState = &glEnableClientState_enc;
	this->glFinish = &glFinish_enc;
	this->glFlush = &glFlush_enc;
	this->glFogx = &glFogx_enc;
	this->glFogxv = &glFogxv_enc;
	this->glFrontFace = &glFrontFace_enc;
	this->glFrustumx = &glFrustumx_enc;
	this->glGetBooleanv = &glGetBooleanv_enc;
	this->glGetBufferParameteriv = &glGetBufferParameteriv_enc;
	this->glClipPlanex = &glClipPlanex_enc;
	this->glGenBuffers = &glGenBuffers_enc;
	this->glGenTextures = &glGenTextures_enc;
	this->glGetError = &glGetError_enc;
	this->glGetFixedv = &glGetFixedv_enc;
	this->glGetIntegerv = &glGetIntegerv_enc;
	this->glGetLightxv = &glGetLightxv_enc;
	this->glGetMaterialxv = &glGetMaterialxv_enc;
	this->glGetPointerv = (glGetPointerv_client_proc_t) &enc_unsupported;
	this->glGetString = (glGetString_client_proc_t) &enc_unsupported;
	this->glGetTexEnviv = &glGetTexEnviv_enc;
	this->glGetTexEnvxv = &glGetTexEnvxv_enc;
	this->glGetTexParameteriv = &glGetTexParameteriv_enc;
	this->glGetTexParameterxv = &glGetTexParameterxv_enc;
	this->glHint = &glHint_enc;
	this->glIsBuffer = &glIsBuffer_enc;
	this->glIsEnabled = &glIsEnabled_enc;
	this->glIsTexture = &glIsTexture_enc;
	this->glLightModelx = &glLightModelx_enc;
	this->glLightModelxv = &glLightModelxv_enc;
	this->glLightx = &glLightx_enc;
	this->glLightxv = &glLightxv_enc;
	this->glLineWidthx = &glLineWidthx_enc;
	this->glLoadIdentity = &glLoadIdentity_enc;
	this->glLoadMatrixx = &glLoadMatrixx_enc;
	this->glLogicOp = &glLogicOp_enc;
	this->glMaterialx = &glMaterialx_enc;
	this->glMaterialxv = &glMaterialxv_enc;
	this->glMatrixMode = &glMatrixMode_enc;
	this->glMultMatrixx = &glMultMatrixx_enc;
	this->glMultiTexCoord4x = &glMultiTexCoord4x_enc;
	this->glNormal3x = &glNormal3x_enc;
	this->glNormalPointer = (glNormalPointer_client_proc_t) &enc_unsupported;
	this->glOrthox = &glOrthox_enc;
	this->glPixelStorei = &glPixelStorei_enc;
	this->glPointParameterx = &glPointParameterx_enc;
	this->glPointParameterxv = &glPointParameterxv_enc;
	this->glPointSizex = &glPointSizex_enc;
	this->glPolygonOffsetx = &glPolygonOffsetx_enc;
	this->glPopMatrix = &glPopMatrix_enc;
	this->glPushMatrix = &glPushMatrix_enc;
	this->glReadPixels = &glReadPixels_enc;
	this->glRotatex = &glRotatex_enc;
	this->glSampleCoverage = &glSampleCoverage_enc;
	this->glSampleCoveragex = &glSampleCoveragex_enc;
	this->glScalex = &glScalex_enc;
	this->glScissor = &glScissor_enc;
	this->glShadeModel = &glShadeModel_enc;
	this->glStencilFunc = &glStencilFunc_enc;
	this->glStencilMask = &glStencilMask_enc;
	this->glStencilOp = &glStencilOp_enc;
	this->glTexCoordPointer = (glTexCoordPointer_client_proc_t) &enc_unsupported;
	this->glTexEnvi = &glTexEnvi_enc;
	this->glTexEnvx = &glTexEnvx_enc;
	this->glTexEnviv = &glTexEnviv_enc;
	this->glTexEnvxv = &glTexEnvxv_enc;
	this->glTexImage2D = &glTexImage2D_enc;
	this->glTexParameteri = &glTexParameteri_enc;
	this->glTexParameterx = &glTexParameterx_enc;
	this->glTexParameteriv = &glTexParameteriv_enc;
	this->glTexParameterxv = &glTexParameterxv_enc;
	this->glTexSubImage2D = &glTexSubImage2D_enc;
	this->glTranslatex = &glTranslatex_enc;
	this->glVertexPointer = (glVertexPointer_client_proc_t) &enc_unsupported;
	this->glViewport = &glViewport_enc;
	this->glPointSizePointerOES = (glPointSizePointerOES_client_proc_t) &enc_unsupported;
	this->glVertexPointerOffset = &glVertexPointerOffset_enc;
	this->glColorPointerOffset = &glColorPointerOffset_enc;
	this->glNormalPointerOffset = &glNormalPointerOffset_enc;
	this->glPointSizePointerOffset = &glPointSizePointerOffset_enc;
	this->glTexCoordPointerOffset = &glTexCoordPointerOffset_enc;
	this->glWeightPointerOffset = &glWeightPointerOffset_enc;
	this->glMatrixIndexPointerOffset = &glMatrixIndexPointerOffset_enc;
	this->glVertexPointerData = &glVertexPointerData_enc;
	this->glColorPointerData = &glColorPointerData_enc;
	this->glNormalPointerData = &glNormalPointerData_enc;
	this->glTexCoordPointerData = &glTexCoordPointerData_enc;
	this->glPointSizePointerData = &glPointSizePointerData_enc;
	this->glWeightPointerData = &glWeightPointerData_enc;
	this->glMatrixIndexPointerData = &glMatrixIndexPointerData_enc;
	this->glDrawElementsOffset = &glDrawElementsOffset_enc;
	this->glDrawElementsData = &glDrawElementsData_enc;
	this->glGetCompressedTextureFormats = &glGetCompressedTextureFormats_enc;
	this->glFinishRoundTrip = &glFinishRoundTrip_enc;
	this->glBlendEquationSeparateOES = &glBlendEquationSeparateOES_enc;
	this->glBlendFuncSeparateOES = &glBlendFuncSeparateOES_enc;
	this->glBlendEquationOES = &glBlendEquationOES_enc;
	this->glDrawTexsOES = &glDrawTexsOES_enc;
	this->glDrawTexiOES = &glDrawTexiOES_enc;
	this->glDrawTexxOES = &glDrawTexxOES_enc;
	this->glDrawTexsvOES = &glDrawTexsvOES_enc;
	this->glDrawTexivOES = &glDrawTexivOES_enc;
	this->glDrawTexxvOES = &glDrawTexxvOES_enc;
	this->glDrawTexfOES = &glDrawTexfOES_enc;
	this->glDrawTexfvOES = &glDrawTexfvOES_enc;
	this->glEGLImageTargetTexture2DOES = &glEGLImageTargetTexture2DOES_enc;
	this->glEGLImageTargetRenderbufferStorageOES = &glEGLImageTargetRenderbufferStorageOES_enc;
	this->glAlphaFuncxOES = &glAlphaFuncxOES_enc;
	this->glClearColorxOES = &glClearColorxOES_enc;
	this->glClearDepthxOES = &glClearDepthxOES_enc;
	this->glClipPlanexOES = &glClipPlanexOES_enc;
	this->glClipPlanexIMG = &glClipPlanexIMG_enc;
	this->glColor4xOES = &glColor4xOES_enc;
	this->glDepthRangexOES = &glDepthRangexOES_enc;
	this->glFogxOES = &glFogxOES_enc;
	this->glFogxvOES = &glFogxvOES_enc;
	this->glFrustumxOES = &glFrustumxOES_enc;
	this->glGetClipPlanexOES = &glGetClipPlanexOES_enc;
	this->glGetClipPlanex = &glGetClipPlanex_enc;
	this->glGetFixedvOES = &glGetFixedvOES_enc;
	this->glGetLightxvOES = &glGetLightxvOES_enc;
	this->glGetMaterialxvOES = &glGetMaterialxvOES_enc;
	this->glGetTexEnvxvOES = &glGetTexEnvxvOES_enc;
	this->glGetTexParameterxvOES = &glGetTexParameterxvOES_enc;
	this->glLightModelxOES = &glLightModelxOES_enc;
	this->glLightModelxvOES = &glLightModelxvOES_enc;
	this->glLightxOES = &glLightxOES_enc;
	this->glLightxvOES = &glLightxvOES_enc;
	this->glLineWidthxOES = &glLineWidthxOES_enc;
	this->glLoadMatrixxOES = &glLoadMatrixxOES_enc;
	this->glMaterialxOES = &glMaterialxOES_enc;
	this->glMaterialxvOES = &glMaterialxvOES_enc;
	this->glMultMatrixxOES = &glMultMatrixxOES_enc;
	this->glMultiTexCoord4xOES = &glMultiTexCoord4xOES_enc;
	this->glNormal3xOES = &glNormal3xOES_enc;
	this->glOrthoxOES = &glOrthoxOES_enc;
	this->glPointParameterxOES = &glPointParameterxOES_enc;
	this->glPointParameterxvOES = &glPointParameterxvOES_enc;
	this->glPointSizexOES = &glPointSizexOES_enc;
	this->glPolygonOffsetxOES = &glPolygonOffsetxOES_enc;
	this->glRotatexOES = &glRotatexOES_enc;
	this->glSampleCoveragexOES = &glSampleCoveragexOES_enc;
	this->glScalexOES = &glScalexOES_enc;
	this->glTexEnvxOES = &glTexEnvxOES_enc;
	this->glTexEnvxvOES = &glTexEnvxvOES_enc;
	this->glTexParameterxOES = &glTexParameterxOES_enc;
	this->glTexParameterxvOES = &glTexParameterxvOES_enc;
	this->glTranslatexOES = &glTranslatexOES_enc;
	this->glIsRenderbufferOES = &glIsRenderbufferOES_enc;
	this->glBindRenderbufferOES = &glBindRenderbufferOES_enc;
	this->glDeleteRenderbuffersOES = &glDeleteRenderbuffersOES_enc;
	this->glGenRenderbuffersOES = &glGenRenderbuffersOES_enc;
	this->glRenderbufferStorageOES = &glRenderbufferStorageOES_enc;
	this->glGetRenderbufferParameterivOES = &glGetRenderbufferParameterivOES_enc;
	this->glIsFramebufferOES = &glIsFramebufferOES_enc;
	this->glBindFramebufferOES = &glBindFramebufferOES_enc;
	this->glDeleteFramebuffersOES = &glDeleteFramebuffersOES_enc;
	this->glGenFramebuffersOES = &glGenFramebuffersOES_enc;
	this->glCheckFramebufferStatusOES = &glCheckFramebufferStatusOES_enc;
	this->glFramebufferRenderbufferOES = &glFramebufferRenderbufferOES_enc;
	this->glFramebufferTexture2DOES = &glFramebufferTexture2DOES_enc;
	this->glGetFramebufferAttachmentParameterivOES = &glGetFramebufferAttachmentParameterivOES_enc;
	this->glGenerateMipmapOES = &glGenerateMipmapOES_enc;
	this->glMapBufferOES = (glMapBufferOES_client_proc_t) &enc_unsupported;
	this->glUnmapBufferOES = &glUnmapBufferOES_enc;
	this->glGetBufferPointervOES = (glGetBufferPointervOES_client_proc_t) &enc_unsupported;
	this->glCurrentPaletteMatrixOES = &glCurrentPaletteMatrixOES_enc;
	this->glLoadPaletteFromModelViewMatrixOES = &glLoadPaletteFromModelViewMatrixOES_enc;
	this->glMatrixIndexPointerOES = (glMatrixIndexPointerOES_client_proc_t) &enc_unsupported;
	this->glWeightPointerOES = (glWeightPointerOES_client_proc_t) &enc_unsupported;
	this->glQueryMatrixxOES = &glQueryMatrixxOES_enc;
	this->glDepthRangefOES = &glDepthRangefOES_enc;
	this->glFrustumfOES = &glFrustumfOES_enc;
	this->glOrthofOES = &glOrthofOES_enc;
	this->glClipPlanefOES = &glClipPlanefOES_enc;
	this->glClipPlanefIMG = &glClipPlanefIMG_enc;
	this->glGetClipPlanefOES = &glGetClipPlanefOES_enc;
	this->glClearDepthfOES = &glClearDepthfOES_enc;
	this->glTexGenfOES = &glTexGenfOES_enc;
	this->glTexGenfvOES = &glTexGenfvOES_enc;
	this->glTexGeniOES = &glTexGeniOES_enc;
	this->glTexGenivOES = &glTexGenivOES_enc;
	this->glTexGenxOES = &glTexGenxOES_enc;
	this->glTexGenxvOES = &glTexGenxvOES_enc;
	this->glGetTexGenfvOES = &glGetTexGenfvOES_enc;
	this->glGetTexGenivOES = &glGetTexGenivOES_enc;
	this->glGetTexGenxvOES = &glGetTexGenxvOES_enc;
	this->glBindVertexArrayOES = &glBindVertexArrayOES_enc;
	this->glDeleteVertexArraysOES = &glDeleteVertexArraysOES_enc;
	this->glGenVertexArraysOES = &glGenVertexArraysOES_enc;
	this->glIsVertexArrayOES = &glIsVertexArrayOES_enc;
	this->glDiscardFramebufferEXT = &glDiscardFramebufferEXT_enc;
	this->glMultiDrawArraysEXT = (glMultiDrawArraysEXT_client_proc_t) &enc_unsupported;
	this->glMultiDrawElementsEXT = (glMultiDrawElementsEXT_client_proc_t) &enc_unsupported;
	this->glMultiDrawArraysSUN = (glMultiDrawArraysSUN_client_proc_t) &enc_unsupported;
	this->glMultiDrawElementsSUN = (glMultiDrawElementsSUN_client_proc_t) &enc_unsupported;
	this->glRenderbufferStorageMultisampleIMG = &glRenderbufferStorageMultisampleIMG_enc;
	this->glFramebufferTexture2DMultisampleIMG = &glFramebufferTexture2DMultisampleIMG_enc;
	this->glDeleteFencesNV = &glDeleteFencesNV_enc;
	this->glGenFencesNV = &glGenFencesNV_enc;
	this->glIsFenceNV = &glIsFenceNV_enc;
	this->glTestFenceNV = &glTestFenceNV_enc;
	this->glGetFenceivNV = &glGetFenceivNV_enc;
	this->glFinishFenceNV = &glFinishFenceNV_enc;
	this->glSetFenceNV = &glSetFenceNV_enc;
	this->glGetDriverControlsQCOM = &glGetDriverControlsQCOM_enc;
	this->glGetDriverControlStringQCOM = &glGetDriverControlStringQCOM_enc;
	this->glEnableDriverControlQCOM = &glEnableDriverControlQCOM_enc;
	this->glDisableDriverControlQCOM = &glDisableDriverControlQCOM_enc;
	this->glExtGetTexturesQCOM = &glExtGetTexturesQCOM_enc;
	this->glExtGetBuffersQCOM = &glExtGetBuffersQCOM_enc;
	this->glExtGetRenderbuffersQCOM = &glExtGetRenderbuffersQCOM_enc;
	this->glExtGetFramebuffersQCOM = &glExtGetFramebuffersQCOM_enc;
	this->glExtGetTexLevelParameterivQCOM = &glExtGetTexLevelParameterivQCOM_enc;
	this->glExtTexObjectStateOverrideiQCOM = &glExtTexObjectStateOverrideiQCOM_enc;
	this->glExtGetTexSubImageQCOM = &glExtGetTexSubImageQCOM_enc;
	this->glExtGetBufferPointervQCOM = (glExtGetBufferPointervQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetShadersQCOM = &glExtGetShadersQCOM_enc;
	this->glExtGetProgramsQCOM = &glExtGetProgramsQCOM_enc;
	this->glExtIsProgramBinaryQCOM = &glExtIsProgramBinaryQCOM_enc;
	this->glExtGetProgramBinarySourceQCOM = (glExtGetProgramBinarySourceQCOM_client_proc_t) &enc_unsupported;
	this->glStartTilingQCOM = &glStartTilingQCOM_enc;
	this->glEndTilingQCOM = &glEndTilingQCOM_enc;
	this->glGetGraphicsResetStatusEXT = &glGetGraphicsResetStatusEXT_enc;
	this->glReadnPixelsEXT = &glReadnPixelsEXT_enc;
}

