//
// Each extension function should have one of the following
// macro definitions:
//    API_ENTRY(funcname, paramlist, arglist)
//  -or- (if the function has a return value)
//    API_ENTRY_RET(return_type,funcname, paramlist, arglist)
//
API_ENTRY(glEGLImageTargetTexture2DOES,
          (GLenum target, GLeglImageOES image),
          (target, image))

API_ENTRY(glEGLImageTargetRenderbufferStorageOES,
          (GLenum target, GLeglImageOES image),
          (target, image))

API_ENTRY(glBlendEquationSeparateOES,
          (GLenum modeRGB, GLenum modeAlpha),
          (modeRGB, modeAlpha))

API_ENTRY(glBlendFuncSeparateOES,
          (GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha),
          (srcRGB, dstRGB, srcAlpha, dstAlpha))

API_ENTRY(glBlendEquationOES,
          (GLenum mode),
          (mode))

API_ENTRY(glCurrentPaletteMatrixOES,
          (GLuint matrixpaletteindex),
          (matrixpaletteindex))

API_ENTRY(glLoadPaletteFromModelViewMatrixOES,
          (void),
          ())

API_ENTRY(glMatrixIndexPointerOES,
          (GLint size, GLenum type, GLsizei stride, const GLvoid * pointer),
          (size, type, stride, pointer))

API_ENTRY(glWeightPointerOES,
          (GLint size, GLenum type, GLsizei stride, const GLvoid * pointer),
          (size, type, stride, pointer))

API_ENTRY(glDepthRangefOES,
          (GLclampf zNear, GLclampf zFar),
          (zNear, zFar))

API_ENTRY(glFrustumfOES,
          (GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar),
          (left, right, bottom, top, zNear, zFar))

API_ENTRY(glOrthofOES,
          (GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar),
          (left, right, bottom, top, zNear, zFar))

API_ENTRY(glClipPlanefOES,
          (GLenum plane, const GLfloat *equation),
          (plane, equation))

API_ENTRY(glGetClipPlanefOES,
          (GLenum pname, GLfloat * eqn),
          (pname, eqn))

API_ENTRY(glClearDepthfOES,
          (GLclampf depth),
          (depth))

API_ENTRY(glPointSizePointerOES,
          (GLenum type, GLsizei stride, const GLvoid *pointer),
          (type, stride, pointer))

API_ENTRY(glTexGenfOES,
          (GLenum coord, GLenum pname, GLfloat param),
          (coord, pname, param))

API_ENTRY(glTexGenfvOES,
          (GLenum coord, GLenum pname, const GLfloat *params),
          (coord, pname, params))

API_ENTRY(glTexGeniOES,
          (GLenum coord, GLenum pname, GLint param),
          (coord, pname, param))

API_ENTRY(glTexGenivOES,
          (GLenum coord, GLenum pname, const GLint *params),
          (coord, pname, params))

API_ENTRY(glTexGenxOES,
          (GLenum coord, GLenum pname, GLfixed param),
          (coord, pname, param))

API_ENTRY(glTexGenxvOES,
          (GLenum coord, GLenum pname, const GLfixed *params),
          (coord, pname, params))

API_ENTRY(glGetTexGenfvOES,
          (GLenum coord, GLenum pname, GLfloat *params),
          (coord, pname, params))

API_ENTRY(glGetTexGenivOES,
          (GLenum coord, GLenum pname, GLint *params),
          (coord, pname, params))

API_ENTRY(glGetTexGenxvOES,
          (GLenum coord, GLenum pname, GLfixed *params),
          (coord, pname, params))

API_ENTRY_RET(GLboolean,
              glIsRenderbufferOES,
              (GLuint renderbuffer),
              (renderbuffer))

API_ENTRY(glBindRenderbufferOES,
          (GLenum target, GLuint renderbuffer),
          (target, renderbuffer))

API_ENTRY(glDeleteRenderbuffersOES,
          (GLsizei n, const GLuint* renderbuffers),
          (n, renderbuffers))

API_ENTRY(glGenRenderbuffersOES,
          (GLsizei n, GLuint* renderbuffers),
          (n, renderbuffers))

API_ENTRY(glRenderbufferStorageOES,
          (GLenum target, GLenum internalformat, GLsizei width, GLsizei height),
          (target, internalformat, width, height))

API_ENTRY(glGetRenderbufferParameterivOES,
          (GLenum target, GLenum pname, GLint* params),
          (target, pname, params))

API_ENTRY_RET(GLboolean,
              glIsFramebufferOES,
              (GLuint framebuffer),
              (framebuffer))

API_ENTRY(glBindFramebufferOES,
          (GLenum target, GLuint framebuffer),
          (target, framebuffer))

API_ENTRY(glDeleteFramebuffersOES,
          (GLsizei n, const GLuint* framebuffers),
          (n, framebuffers))

API_ENTRY(glGenFramebuffersOES,
          (GLsizei n, GLuint* framebuffers),
          (n, framebuffers))

API_ENTRY_RET(GLenum,
              glCheckFramebufferStatusOES,
              (GLenum target),
              (target))

API_ENTRY(glFramebufferTexture2DOES,
          (GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level),
          (target, attachment, textarget, texture, level))

API_ENTRY(glFramebufferRenderbufferOES,
          (GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer),
          (target, attachment, renderbuffertarget, renderbuffer))

API_ENTRY(glGetFramebufferAttachmentParameterivOES,
          (GLenum target, GLenum attachment, GLenum pname, GLint* params),
          (target, attachment, pname, params))

API_ENTRY(glGenerateMipmapOES,
          (GLenum target),
          (target))

API_ENTRY(glDrawTexsOES,
          (GLshort x, GLshort y, GLshort z, GLshort width, GLshort height),
          (x, y, z, width, height))

API_ENTRY(glDrawTexiOES,
          (GLint x, GLint y, GLint z, GLint width, GLint height),
          (x, y, z, width, height))

API_ENTRY(glDrawTexfOES,
          (GLfloat x, GLfloat y, GLfloat z, GLfloat width, GLfloat height),
          (x, y, z, width, height))

API_ENTRY(glDrawTexxOES,
          (GLfixed x, GLfixed y, GLfixed z, GLfixed width, GLfixed height),
          (x, y, z, width, height))

API_ENTRY(glDrawTexsvOES,
          (const GLshort *coords),
          (coords))

API_ENTRY(glDrawTexivOES,
          (const GLint *coords),
          (coords))

API_ENTRY(glDrawTexfvOES,
          (const GLfloat *coords),
          (coords))

API_ENTRY(glDrawTexxvOES,
          (const GLfixed *coords),
          (coords))

API_ENTRY(glBindVertexArrayOES,
          (GLuint array),
          (array))

API_ENTRY(glDeleteVertexArraysOES,
          (GLsizei n, const GLuint* arrays),
          (n, arrays))

API_ENTRY(glGenVertexArraysOES,
          (GLsizei n, GLuint* arrays),
          (n, arrays))

API_ENTRY(glIsVertexArrayOES,
          (GLuint array),
          (array))

API_ENTRY(glMapBufferOES,
          (GLenum target, GLenum access),
          (target, access))

API_ENTRY(glUnmapBufferOES,
          (GLenum target),
          (target))