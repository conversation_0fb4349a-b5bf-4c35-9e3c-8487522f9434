// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __GUARD_gl2_opcodes_h_
#define __GUARD_gl2_opcodes_h_

#define OP_glActiveTexture 					2048
#define OP_glAttachShader 					2049
#define OP_glBindAttribLocation 					2050
#define OP_glBindBuffer 					2051
#define OP_glBindFramebuffer 					2052
#define OP_glBindRenderbuffer 					2053
#define OP_glBindTexture 					2054
#define OP_glBlendColor 					2055
#define OP_glBlendEquation 					2056
#define OP_glBlendEquationSeparate 					2057
#define OP_glBlendFunc 					2058
#define OP_glBlendFuncSeparate 					2059
#define OP_glBufferData 					2060
#define OP_glBufferSubData 					2061
#define OP_glCheckFramebufferStatus 					2062
#define OP_glClear 					2063
#define OP_glClearColor 					2064
#define OP_glClearDepthf 					2065
#define OP_glClearStencil 					2066
#define OP_glColorMask 					2067
#define OP_glCompileShader 					2068
#define OP_glCompressedTexImage2D 					2069
#define OP_glCompressedTexSubImage2D 					2070
#define OP_glCopyTexImage2D 					2071
#define OP_glCopyTexSubImage2D 					2072
#define OP_glCreateProgram 					2073
#define OP_glCreateShader 					2074
#define OP_glCullFace 					2075
#define OP_glDeleteBuffers 					2076
#define OP_glDeleteFramebuffers 					2077
#define OP_glDeleteProgram 					2078
#define OP_glDeleteRenderbuffers 					2079
#define OP_glDeleteShader 					2080
#define OP_glDeleteTextures 					2081
#define OP_glDepthFunc 					2082
#define OP_glDepthMask 					2083
#define OP_glDepthRangef 					2084
#define OP_glDetachShader 					2085
#define OP_glDisable 					2086
#define OP_glDisableVertexAttribArray 					2087
#define OP_glDrawArrays 					2088
#define OP_glDrawElements 					2089
#define OP_glEnable 					2090
#define OP_glEnableVertexAttribArray 					2091
#define OP_glFinish 					2092
#define OP_glFlush 					2093
#define OP_glFramebufferRenderbuffer 					2094
#define OP_glFramebufferTexture2D 					2095
#define OP_glFrontFace 					2096
#define OP_glGenBuffers 					2097
#define OP_glGenerateMipmap 					2098
#define OP_glGenFramebuffers 					2099
#define OP_glGenRenderbuffers 					2100
#define OP_glGenTextures 					2101
#define OP_glGetActiveAttrib 					2102
#define OP_glGetActiveUniform 					2103
#define OP_glGetAttachedShaders 					2104
#define OP_glGetAttribLocation 					2105
#define OP_glGetBooleanv 					2106
#define OP_glGetBufferParameteriv 					2107
#define OP_glGetError 					2108
#define OP_glGetFloatv 					2109
#define OP_glGetFramebufferAttachmentParameteriv 					2110
#define OP_glGetIntegerv 					2111
#define OP_glGetProgramiv 					2112
#define OP_glGetProgramInfoLog 					2113
#define OP_glGetRenderbufferParameteriv 					2114
#define OP_glGetShaderiv 					2115
#define OP_glGetShaderInfoLog 					2116
#define OP_glGetShaderPrecisionFormat 					2117
#define OP_glGetShaderSource 					2118
#define OP_glGetString 					2119
#define OP_glGetTexParameterfv 					2120
#define OP_glGetTexParameteriv 					2121
#define OP_glGetUniformfv 					2122
#define OP_glGetUniformiv 					2123
#define OP_glGetUniformLocation 					2124
#define OP_glGetVertexAttribfv 					2125
#define OP_glGetVertexAttribiv 					2126
#define OP_glGetVertexAttribPointerv 					2127
#define OP_glHint 					2128
#define OP_glIsBuffer 					2129
#define OP_glIsEnabled 					2130
#define OP_glIsFramebuffer 					2131
#define OP_glIsProgram 					2132
#define OP_glIsRenderbuffer 					2133
#define OP_glIsShader 					2134
#define OP_glIsTexture 					2135
#define OP_glLineWidth 					2136
#define OP_glLinkProgram 					2137
#define OP_glPixelStorei 					2138
#define OP_glPolygonOffset 					2139
#define OP_glReadPixels 					2140
#define OP_glReleaseShaderCompiler 					2141
#define OP_glRenderbufferStorage 					2142
#define OP_glSampleCoverage 					2143
#define OP_glScissor 					2144
#define OP_glShaderBinary 					2145
#define OP_glShaderSource 					2146
#define OP_glStencilFunc 					2147
#define OP_glStencilFuncSeparate 					2148
#define OP_glStencilMask 					2149
#define OP_glStencilMaskSeparate 					2150
#define OP_glStencilOp 					2151
#define OP_glStencilOpSeparate 					2152
#define OP_glTexImage2D 					2153
#define OP_glTexParameterf 					2154
#define OP_glTexParameterfv 					2155
#define OP_glTexParameteri 					2156
#define OP_glTexParameteriv 					2157
#define OP_glTexSubImage2D 					2158
#define OP_glUniform1f 					2159
#define OP_glUniform1fv 					2160
#define OP_glUniform1i 					2161
#define OP_glUniform1iv 					2162
#define OP_glUniform2f 					2163
#define OP_glUniform2fv 					2164
#define OP_glUniform2i 					2165
#define OP_glUniform2iv 					2166
#define OP_glUniform3f 					2167
#define OP_glUniform3fv 					2168
#define OP_glUniform3i 					2169
#define OP_glUniform3iv 					2170
#define OP_glUniform4f 					2171
#define OP_glUniform4fv 					2172
#define OP_glUniform4i 					2173
#define OP_glUniform4iv 					2174
#define OP_glUniformMatrix2fv 					2175
#define OP_glUniformMatrix3fv 					2176
#define OP_glUniformMatrix4fv 					2177
#define OP_glUseProgram 					2178
#define OP_glValidateProgram 					2179
#define OP_glVertexAttrib1f 					2180
#define OP_glVertexAttrib1fv 					2181
#define OP_glVertexAttrib2f 					2182
#define OP_glVertexAttrib2fv 					2183
#define OP_glVertexAttrib3f 					2184
#define OP_glVertexAttrib3fv 					2185
#define OP_glVertexAttrib4f 					2186
#define OP_glVertexAttrib4fv 					2187
#define OP_glVertexAttribPointer 					2188
#define OP_glViewport 					2189
#define OP_glEGLImageTargetTexture2DOES 					2190
#define OP_glEGLImageTargetRenderbufferStorageOES 					2191
#define OP_glGetProgramBinaryOES 					2192
#define OP_glProgramBinaryOES 					2193
#define OP_glMapBufferOES 					2194
#define OP_glUnmapBufferOES 					2195
#define OP_glTexImage3DOES 					2196
#define OP_glTexSubImage3DOES 					2197
#define OP_glCopyTexSubImage3DOES 					2198
#define OP_glCompressedTexImage3DOES 					2199
#define OP_glCompressedTexSubImage3DOES 					2200
#define OP_glFramebufferTexture3DOES 					2201
#define OP_glBindVertexArrayOES 					2202
#define OP_glDeleteVertexArraysOES 					2203
#define OP_glGenVertexArraysOES 					2204
#define OP_glIsVertexArrayOES 					2205
#define OP_glDiscardFramebufferEXT 					2206
#define OP_glMultiDrawArraysEXT 					2207
#define OP_glMultiDrawElementsEXT 					2208
#define OP_glGetPerfMonitorGroupsAMD 					2209
#define OP_glGetPerfMonitorCountersAMD 					2210
#define OP_glGetPerfMonitorGroupStringAMD 					2211
#define OP_glGetPerfMonitorCounterStringAMD 					2212
#define OP_glGetPerfMonitorCounterInfoAMD 					2213
#define OP_glGenPerfMonitorsAMD 					2214
#define OP_glDeletePerfMonitorsAMD 					2215
#define OP_glSelectPerfMonitorCountersAMD 					2216
#define OP_glBeginPerfMonitorAMD 					2217
#define OP_glEndPerfMonitorAMD 					2218
#define OP_glGetPerfMonitorCounterDataAMD 					2219
#define OP_glRenderbufferStorageMultisampleIMG 					2220
#define OP_glFramebufferTexture2DMultisampleIMG 					2221
#define OP_glDeleteFencesNV 					2222
#define OP_glGenFencesNV 					2223
#define OP_glIsFenceNV 					2224
#define OP_glTestFenceNV 					2225
#define OP_glGetFenceivNV 					2226
#define OP_glFinishFenceNV 					2227
#define OP_glSetFenceNV 					2228
#define OP_glCoverageMaskNV 					2229
#define OP_glCoverageOperationNV 					2230
#define OP_glGetDriverControlsQCOM 					2231
#define OP_glGetDriverControlStringQCOM 					2232
#define OP_glEnableDriverControlQCOM 					2233
#define OP_glDisableDriverControlQCOM 					2234
#define OP_glExtGetTexturesQCOM 					2235
#define OP_glExtGetBuffersQCOM 					2236
#define OP_glExtGetRenderbuffersQCOM 					2237
#define OP_glExtGetFramebuffersQCOM 					2238
#define OP_glExtGetTexLevelParameterivQCOM 					2239
#define OP_glExtTexObjectStateOverrideiQCOM 					2240
#define OP_glExtGetTexSubImageQCOM 					2241
#define OP_glExtGetBufferPointervQCOM 					2242
#define OP_glExtGetShadersQCOM 					2243
#define OP_glExtGetProgramsQCOM 					2244
#define OP_glExtIsProgramBinaryQCOM 					2245
#define OP_glExtGetProgramBinarySourceQCOM 					2246
#define OP_glStartTilingQCOM 					2247
#define OP_glEndTilingQCOM 					2248
#define OP_glVertexAttribPointerData 					2249
#define OP_glVertexAttribPointerOffset 					2250
#define OP_glDrawElementsOffset 					2251
#define OP_glDrawElementsData 					2252
#define OP_glGetCompressedTextureFormats 					2253
#define OP_glShaderString 					2254
#define OP_glFinishRoundTrip 					2255
#define OP_glGenVertexArrays 					2256
#define OP_glBindVertexArray 					2257
#define OP_glDeleteVertexArrays 					2258
#define OP_glIsVertexArray 					2259
#define OP_glMapBufferRange 					2260
#define OP_glUnmapBuffer 					2261
#define OP_glFlushMappedBufferRange 					2262
#define OP_glMapBufferRangeAEMU 					2263
#define OP_glUnmapBufferAEMU 					2264
#define OP_glFlushMappedBufferRangeAEMU 					2265
#define OP_glReadPixelsOffsetAEMU 					2266
#define OP_glCompressedTexImage2DOffsetAEMU 					2267
#define OP_glCompressedTexSubImage2DOffsetAEMU 					2268
#define OP_glTexImage2DOffsetAEMU 					2269
#define OP_glTexSubImage2DOffsetAEMU 					2270
#define OP_glBindBufferRange 					2271
#define OP_glBindBufferBase 					2272
#define OP_glCopyBufferSubData 					2273
#define OP_glClearBufferiv 					2274
#define OP_glClearBufferuiv 					2275
#define OP_glClearBufferfv 					2276
#define OP_glClearBufferfi 					2277
#define OP_glGetBufferParameteri64v 					2278
#define OP_glGetBufferPointerv 					2279
#define OP_glUniformBlockBinding 					2280
#define OP_glGetUniformBlockIndex 					2281
#define OP_glGetUniformIndices 					2282
#define OP_glGetUniformIndicesAEMU 					2283
#define OP_glGetActiveUniformBlockiv 					2284
#define OP_glGetActiveUniformBlockName 					2285
#define OP_glUniform1ui 					2286
#define OP_glUniform2ui 					2287
#define OP_glUniform3ui 					2288
#define OP_glUniform4ui 					2289
#define OP_glUniform1uiv 					2290
#define OP_glUniform2uiv 					2291
#define OP_glUniform3uiv 					2292
#define OP_glUniform4uiv 					2293
#define OP_glUniformMatrix2x3fv 					2294
#define OP_glUniformMatrix3x2fv 					2295
#define OP_glUniformMatrix2x4fv 					2296
#define OP_glUniformMatrix4x2fv 					2297
#define OP_glUniformMatrix3x4fv 					2298
#define OP_glUniformMatrix4x3fv 					2299
#define OP_glGetUniformuiv 					2300
#define OP_glGetActiveUniformsiv 					2301
#define OP_glVertexAttribI4i 					2302
#define OP_glVertexAttribI4ui 					2303
#define OP_glVertexAttribI4iv 					2304
#define OP_glVertexAttribI4uiv 					2305
#define OP_glVertexAttribIPointer 					2306
#define OP_glVertexAttribIPointerOffsetAEMU 					2307
#define OP_glVertexAttribIPointerDataAEMU 					2308
#define OP_glGetVertexAttribIiv 					2309
#define OP_glGetVertexAttribIuiv 					2310
#define OP_glVertexAttribDivisor 					2311
#define OP_glDrawArraysInstanced 					2312
#define OP_glDrawElementsInstanced 					2313
#define OP_glDrawElementsInstancedDataAEMU 					2314
#define OP_glDrawElementsInstancedOffsetAEMU 					2315
#define OP_glDrawRangeElements 					2316
#define OP_glDrawRangeElementsDataAEMU 					2317
#define OP_glDrawRangeElementsOffsetAEMU 					2318
#define OP_glFenceSync 					2319
#define OP_glClientWaitSync 					2320
#define OP_glWaitSync 					2321
#define OP_glDeleteSync 					2322
#define OP_glIsSync 					2323
#define OP_glGetSynciv 					2324
#define OP_glFenceSyncAEMU 					2325
#define OP_glClientWaitSyncAEMU 					2326
#define OP_glWaitSyncAEMU 					2327
#define OP_glDeleteSyncAEMU 					2328
#define OP_glIsSyncAEMU 					2329
#define OP_glGetSyncivAEMU 					2330
#define OP_glDrawBuffers 					2331
#define OP_glReadBuffer 					2332
#define OP_glBlitFramebuffer 					2333
#define OP_glInvalidateFramebuffer 					2334
#define OP_glInvalidateSubFramebuffer 					2335
#define OP_glFramebufferTextureLayer 					2336
#define OP_glRenderbufferStorageMultisample 					2337
#define OP_glTexStorage2D 					2338
#define OP_glGetInternalformativ 					2339
#define OP_glBeginTransformFeedback 					2340
#define OP_glEndTransformFeedback 					2341
#define OP_glGenTransformFeedbacks 					2342
#define OP_glDeleteTransformFeedbacks 					2343
#define OP_glBindTransformFeedback 					2344
#define OP_glPauseTransformFeedback 					2345
#define OP_glResumeTransformFeedback 					2346
#define OP_glIsTransformFeedback 					2347
#define OP_glTransformFeedbackVaryings 					2348
#define OP_glTransformFeedbackVaryingsAEMU 					2349
#define OP_glGetTransformFeedbackVarying 					2350
#define OP_glGenSamplers 					2351
#define OP_glDeleteSamplers 					2352
#define OP_glBindSampler 					2353
#define OP_glSamplerParameterf 					2354
#define OP_glSamplerParameteri 					2355
#define OP_glSamplerParameterfv 					2356
#define OP_glSamplerParameteriv 					2357
#define OP_glGetSamplerParameterfv 					2358
#define OP_glGetSamplerParameteriv 					2359
#define OP_glIsSampler 					2360
#define OP_glGenQueries 					2361
#define OP_glDeleteQueries 					2362
#define OP_glBeginQuery 					2363
#define OP_glEndQuery 					2364
#define OP_glGetQueryiv 					2365
#define OP_glGetQueryObjectuiv 					2366
#define OP_glIsQuery 					2367
#define OP_glProgramParameteri 					2368
#define OP_glProgramBinary 					2369
#define OP_glGetProgramBinary 					2370
#define OP_glGetFragDataLocation 					2371
#define OP_glGetInteger64v 					2372
#define OP_glGetIntegeri_v 					2373
#define OP_glGetInteger64i_v 					2374
#define OP_glTexImage3D 					2375
#define OP_glTexImage3DOffsetAEMU 					2376
#define OP_glTexStorage3D 					2377
#define OP_glTexSubImage3D 					2378
#define OP_glTexSubImage3DOffsetAEMU 					2379
#define OP_glCompressedTexImage3D 					2380
#define OP_glCompressedTexImage3DOffsetAEMU 					2381
#define OP_glCompressedTexSubImage3D 					2382
#define OP_glCompressedTexSubImage3DOffsetAEMU 					2383
#define OP_glCopyTexSubImage3D 					2384
#define OP_glGetStringi 					2385
#define OP_glGetBooleani_v 					2386
#define OP_glMemoryBarrier 					2387
#define OP_glMemoryBarrierByRegion 					2388
#define OP_glGenProgramPipelines 					2389
#define OP_glDeleteProgramPipelines 					2390
#define OP_glBindProgramPipeline 					2391
#define OP_glGetProgramPipelineiv 					2392
#define OP_glGetProgramPipelineInfoLog 					2393
#define OP_glValidateProgramPipeline 					2394
#define OP_glIsProgramPipeline 					2395
#define OP_glUseProgramStages 					2396
#define OP_glActiveShaderProgram 					2397
#define OP_glCreateShaderProgramv 					2398
#define OP_glCreateShaderProgramvAEMU 					2399
#define OP_glProgramUniform1f 					2400
#define OP_glProgramUniform2f 					2401
#define OP_glProgramUniform3f 					2402
#define OP_glProgramUniform4f 					2403
#define OP_glProgramUniform1i 					2404
#define OP_glProgramUniform2i 					2405
#define OP_glProgramUniform3i 					2406
#define OP_glProgramUniform4i 					2407
#define OP_glProgramUniform1ui 					2408
#define OP_glProgramUniform2ui 					2409
#define OP_glProgramUniform3ui 					2410
#define OP_glProgramUniform4ui 					2411
#define OP_glProgramUniform1fv 					2412
#define OP_glProgramUniform2fv 					2413
#define OP_glProgramUniform3fv 					2414
#define OP_glProgramUniform4fv 					2415
#define OP_glProgramUniform1iv 					2416
#define OP_glProgramUniform2iv 					2417
#define OP_glProgramUniform3iv 					2418
#define OP_glProgramUniform4iv 					2419
#define OP_glProgramUniform1uiv 					2420
#define OP_glProgramUniform2uiv 					2421
#define OP_glProgramUniform3uiv 					2422
#define OP_glProgramUniform4uiv 					2423
#define OP_glProgramUniformMatrix2fv 					2424
#define OP_glProgramUniformMatrix3fv 					2425
#define OP_glProgramUniformMatrix4fv 					2426
#define OP_glProgramUniformMatrix2x3fv 					2427
#define OP_glProgramUniformMatrix3x2fv 					2428
#define OP_glProgramUniformMatrix2x4fv 					2429
#define OP_glProgramUniformMatrix4x2fv 					2430
#define OP_glProgramUniformMatrix3x4fv 					2431
#define OP_glProgramUniformMatrix4x3fv 					2432
#define OP_glGetProgramInterfaceiv 					2433
#define OP_glGetProgramResourceiv 					2434
#define OP_glGetProgramResourceIndex 					2435
#define OP_glGetProgramResourceLocation 					2436
#define OP_glGetProgramResourceName 					2437
#define OP_glBindImageTexture 					2438
#define OP_glDispatchCompute 					2439
#define OP_glDispatchComputeIndirect 					2440
#define OP_glBindVertexBuffer 					2441
#define OP_glVertexAttribBinding 					2442
#define OP_glVertexAttribFormat 					2443
#define OP_glVertexAttribIFormat 					2444
#define OP_glVertexBindingDivisor 					2445
#define OP_glDrawArraysIndirect 					2446
#define OP_glDrawArraysIndirectDataAEMU 					2447
#define OP_glDrawArraysIndirectOffsetAEMU 					2448
#define OP_glDrawElementsIndirect 					2449
#define OP_glDrawElementsIndirectDataAEMU 					2450
#define OP_glDrawElementsIndirectOffsetAEMU 					2451
#define OP_glTexStorage2DMultisample 					2452
#define OP_glSampleMaski 					2453
#define OP_glGetMultisamplefv 					2454
#define OP_glFramebufferParameteri 					2455
#define OP_glGetFramebufferParameteriv 					2456
#define OP_glGetTexLevelParameterfv 					2457
#define OP_glGetTexLevelParameteriv 					2458
#define OP_glMapBufferRangeDMA 					2459
#define OP_glUnmapBufferDMA 					2460
#define OP_glMapBufferRangeDirect 					2461
#define OP_glUnmapBufferDirect 					2462
#define OP_glFlushMappedBufferRangeDirect 					2463
#define OP_glGetGraphicsResetStatusEXT 					2464
#define OP_glReadnPixelsEXT 					2465
#define OP_glGetnUniformfvEXT 					2466
#define OP_glGetnUniformivEXT 					2467
#define OP_glDrawArraysNullAEMU 					2468
#define OP_glDrawElementsNullAEMU 					2469
#define OP_glDrawElementsOffsetNullAEMU 					2470
#define OP_glDrawElementsDataNullAEMU 					2471
#define OP_last 					2472


#endif
