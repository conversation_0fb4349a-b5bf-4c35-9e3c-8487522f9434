/*
 * Copyright (C) 2016 Google, Inc.
 *
 * This software is licensed under the terms of the GNU General Public
 * License version 2, as published by the Free Software Foundation, and
 * may be copied, distributed, and modified under those terms.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 */

#ifndef __GOLDFISH_FORMATCONVERSIONS_H__
#define __GOLDFISH_FORMATCONVERSIONS_H__

#include <inttypes.h>

// format conversions and helper functions
void get_yv12_offsets(int width, int height,
                      uint32_t* yStride_out,
                      uint32_t* cStride_out,
                      uint32_t* totalSz_out);
void get_yuv420p_offsets(int width, int height,
                         uint32_t* yStride_out,
                         uint32_t* cStride_out,
                         uint32_t* totalSz_out);
signed clamp_rgb(signed value);
void rgb565_to_yv12(char* dest, char* src, int width, int height,
                    int left, int top, int right, int bottom);
void rgb888_to_yv12(char* dest, char* src, int width, int height,
                    int left, int top, int right, int bottom);
void rgb888_to_yuv420p(char* dest, char* src, int width, int height,
                       int left, int top, int right, int bottom);
void yv12_to_rgb565(char* dest, char* src, int width, int height,
                    int left, int top, int right, int bottom);
void yv12_to_rgb888(char* dest, char* src, int width, int height,
                    int left, int top, int right, int bottom);
void yuv420p_to_rgb888(char* dest, char* src, int width, int height,
                       int left, int top, int right, int bottom);
void copy_rgb_buffer_from_unlocked(char* _dst, char* raw_data,
                                   int unlockedWidth,
                                   int width, int height, int top, int left,
                                   int bpp);
#endif
