// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_handlemap_guest
// (header) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#pragma once

#include <vulkan/vulkan.h>


#include "vk_platform_compat.h"

#include "goldfish_vk_private_defs.h"
#include "VulkanHandleMapping.h"
// Stuff we are not going to use but if included,
// will cause compile errors. These are Android Vulkan
// required extensions, but the approach will be to
// implement them completely on the guest side.
#undef VK_KHR_android_surface
#undef VK_ANDROID_external_memory_android_hardware_buffer


namespace goldfish_vk {

#ifdef VK_VERSION_1_0
void handlemap_VkApplicationInfo(
    VulkanHandleMapping* handlemap,
    VkApplicationInfo* toMap);

void handlemap_VkInstanceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkInstanceCreateInfo* toMap);

void handlemap_VkAllocationCallbacks(
    VulkanHandleMapping* handlemap,
    VkAllocationCallbacks* toMap);

void handlemap_VkPhysicalDeviceFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceFeatures* toMap);

void handlemap_VkFormatProperties(
    VulkanHandleMapping* handlemap,
    VkFormatProperties* toMap);

void handlemap_VkExtent3D(
    VulkanHandleMapping* handlemap,
    VkExtent3D* toMap);

void handlemap_VkImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkImageFormatProperties* toMap);

void handlemap_VkPhysicalDeviceLimits(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceLimits* toMap);

void handlemap_VkPhysicalDeviceSparseProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSparseProperties* toMap);

void handlemap_VkPhysicalDeviceProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProperties* toMap);

void handlemap_VkQueueFamilyProperties(
    VulkanHandleMapping* handlemap,
    VkQueueFamilyProperties* toMap);

void handlemap_VkMemoryType(
    VulkanHandleMapping* handlemap,
    VkMemoryType* toMap);

void handlemap_VkMemoryHeap(
    VulkanHandleMapping* handlemap,
    VkMemoryHeap* toMap);

void handlemap_VkPhysicalDeviceMemoryProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMemoryProperties* toMap);

void handlemap_VkDeviceQueueCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceQueueCreateInfo* toMap);

void handlemap_VkDeviceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceCreateInfo* toMap);

void handlemap_VkExtensionProperties(
    VulkanHandleMapping* handlemap,
    VkExtensionProperties* toMap);

void handlemap_VkLayerProperties(
    VulkanHandleMapping* handlemap,
    VkLayerProperties* toMap);

void handlemap_VkSubmitInfo(
    VulkanHandleMapping* handlemap,
    VkSubmitInfo* toMap);

void handlemap_VkMemoryAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkMemoryAllocateInfo* toMap);

void handlemap_VkMappedMemoryRange(
    VulkanHandleMapping* handlemap,
    VkMappedMemoryRange* toMap);

void handlemap_VkMemoryRequirements(
    VulkanHandleMapping* handlemap,
    VkMemoryRequirements* toMap);

void handlemap_VkSparseImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkSparseImageFormatProperties* toMap);

void handlemap_VkSparseImageMemoryRequirements(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryRequirements* toMap);

void handlemap_VkSparseMemoryBind(
    VulkanHandleMapping* handlemap,
    VkSparseMemoryBind* toMap);

void handlemap_VkSparseBufferMemoryBindInfo(
    VulkanHandleMapping* handlemap,
    VkSparseBufferMemoryBindInfo* toMap);

void handlemap_VkSparseImageOpaqueMemoryBindInfo(
    VulkanHandleMapping* handlemap,
    VkSparseImageOpaqueMemoryBindInfo* toMap);

void handlemap_VkImageSubresource(
    VulkanHandleMapping* handlemap,
    VkImageSubresource* toMap);

void handlemap_VkOffset3D(
    VulkanHandleMapping* handlemap,
    VkOffset3D* toMap);

void handlemap_VkSparseImageMemoryBind(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryBind* toMap);

void handlemap_VkSparseImageMemoryBindInfo(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryBindInfo* toMap);

void handlemap_VkBindSparseInfo(
    VulkanHandleMapping* handlemap,
    VkBindSparseInfo* toMap);

void handlemap_VkFenceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkFenceCreateInfo* toMap);

void handlemap_VkSemaphoreCreateInfo(
    VulkanHandleMapping* handlemap,
    VkSemaphoreCreateInfo* toMap);

void handlemap_VkEventCreateInfo(
    VulkanHandleMapping* handlemap,
    VkEventCreateInfo* toMap);

void handlemap_VkQueryPoolCreateInfo(
    VulkanHandleMapping* handlemap,
    VkQueryPoolCreateInfo* toMap);

void handlemap_VkBufferCreateInfo(
    VulkanHandleMapping* handlemap,
    VkBufferCreateInfo* toMap);

void handlemap_VkBufferViewCreateInfo(
    VulkanHandleMapping* handlemap,
    VkBufferViewCreateInfo* toMap);

void handlemap_VkImageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkImageCreateInfo* toMap);

void handlemap_VkSubresourceLayout(
    VulkanHandleMapping* handlemap,
    VkSubresourceLayout* toMap);

void handlemap_VkComponentMapping(
    VulkanHandleMapping* handlemap,
    VkComponentMapping* toMap);

void handlemap_VkImageSubresourceRange(
    VulkanHandleMapping* handlemap,
    VkImageSubresourceRange* toMap);

void handlemap_VkImageViewCreateInfo(
    VulkanHandleMapping* handlemap,
    VkImageViewCreateInfo* toMap);

void handlemap_VkShaderModuleCreateInfo(
    VulkanHandleMapping* handlemap,
    VkShaderModuleCreateInfo* toMap);

void handlemap_VkPipelineCacheCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineCacheCreateInfo* toMap);

void handlemap_VkSpecializationMapEntry(
    VulkanHandleMapping* handlemap,
    VkSpecializationMapEntry* toMap);

void handlemap_VkSpecializationInfo(
    VulkanHandleMapping* handlemap,
    VkSpecializationInfo* toMap);

void handlemap_VkPipelineShaderStageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineShaderStageCreateInfo* toMap);

void handlemap_VkVertexInputBindingDescription(
    VulkanHandleMapping* handlemap,
    VkVertexInputBindingDescription* toMap);

void handlemap_VkVertexInputAttributeDescription(
    VulkanHandleMapping* handlemap,
    VkVertexInputAttributeDescription* toMap);

void handlemap_VkPipelineVertexInputStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineVertexInputStateCreateInfo* toMap);

void handlemap_VkPipelineInputAssemblyStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineInputAssemblyStateCreateInfo* toMap);

void handlemap_VkPipelineTessellationStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineTessellationStateCreateInfo* toMap);

void handlemap_VkViewport(
    VulkanHandleMapping* handlemap,
    VkViewport* toMap);

void handlemap_VkOffset2D(
    VulkanHandleMapping* handlemap,
    VkOffset2D* toMap);

void handlemap_VkExtent2D(
    VulkanHandleMapping* handlemap,
    VkExtent2D* toMap);

void handlemap_VkRect2D(
    VulkanHandleMapping* handlemap,
    VkRect2D* toMap);

void handlemap_VkPipelineViewportStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineViewportStateCreateInfo* toMap);

void handlemap_VkPipelineRasterizationStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineRasterizationStateCreateInfo* toMap);

void handlemap_VkPipelineMultisampleStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineMultisampleStateCreateInfo* toMap);

void handlemap_VkStencilOpState(
    VulkanHandleMapping* handlemap,
    VkStencilOpState* toMap);

void handlemap_VkPipelineDepthStencilStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineDepthStencilStateCreateInfo* toMap);

void handlemap_VkPipelineColorBlendAttachmentState(
    VulkanHandleMapping* handlemap,
    VkPipelineColorBlendAttachmentState* toMap);

void handlemap_VkPipelineColorBlendStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineColorBlendStateCreateInfo* toMap);

void handlemap_VkPipelineDynamicStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineDynamicStateCreateInfo* toMap);

void handlemap_VkGraphicsPipelineCreateInfo(
    VulkanHandleMapping* handlemap,
    VkGraphicsPipelineCreateInfo* toMap);

void handlemap_VkComputePipelineCreateInfo(
    VulkanHandleMapping* handlemap,
    VkComputePipelineCreateInfo* toMap);

void handlemap_VkPushConstantRange(
    VulkanHandleMapping* handlemap,
    VkPushConstantRange* toMap);

void handlemap_VkPipelineLayoutCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineLayoutCreateInfo* toMap);

void handlemap_VkSamplerCreateInfo(
    VulkanHandleMapping* handlemap,
    VkSamplerCreateInfo* toMap);

void handlemap_VkDescriptorSetLayoutBinding(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutBinding* toMap);

void handlemap_VkDescriptorSetLayoutCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutCreateInfo* toMap);

void handlemap_VkDescriptorPoolSize(
    VulkanHandleMapping* handlemap,
    VkDescriptorPoolSize* toMap);

void handlemap_VkDescriptorPoolCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorPoolCreateInfo* toMap);

void handlemap_VkDescriptorSetAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetAllocateInfo* toMap);

void handlemap_VkDescriptorImageInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorImageInfo* toMap);

void handlemap_VkDescriptorBufferInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorBufferInfo* toMap);

void handlemap_VkWriteDescriptorSet(
    VulkanHandleMapping* handlemap,
    VkWriteDescriptorSet* toMap);

void handlemap_VkCopyDescriptorSet(
    VulkanHandleMapping* handlemap,
    VkCopyDescriptorSet* toMap);

void handlemap_VkFramebufferCreateInfo(
    VulkanHandleMapping* handlemap,
    VkFramebufferCreateInfo* toMap);

void handlemap_VkAttachmentDescription(
    VulkanHandleMapping* handlemap,
    VkAttachmentDescription* toMap);

void handlemap_VkAttachmentReference(
    VulkanHandleMapping* handlemap,
    VkAttachmentReference* toMap);

void handlemap_VkSubpassDescription(
    VulkanHandleMapping* handlemap,
    VkSubpassDescription* toMap);

void handlemap_VkSubpassDependency(
    VulkanHandleMapping* handlemap,
    VkSubpassDependency* toMap);

void handlemap_VkRenderPassCreateInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassCreateInfo* toMap);

void handlemap_VkCommandPoolCreateInfo(
    VulkanHandleMapping* handlemap,
    VkCommandPoolCreateInfo* toMap);

void handlemap_VkCommandBufferAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkCommandBufferAllocateInfo* toMap);

void handlemap_VkCommandBufferInheritanceInfo(
    VulkanHandleMapping* handlemap,
    VkCommandBufferInheritanceInfo* toMap);

void handlemap_VkCommandBufferBeginInfo(
    VulkanHandleMapping* handlemap,
    VkCommandBufferBeginInfo* toMap);

void handlemap_VkBufferCopy(
    VulkanHandleMapping* handlemap,
    VkBufferCopy* toMap);

void handlemap_VkImageSubresourceLayers(
    VulkanHandleMapping* handlemap,
    VkImageSubresourceLayers* toMap);

void handlemap_VkImageCopy(
    VulkanHandleMapping* handlemap,
    VkImageCopy* toMap);

void handlemap_VkImageBlit(
    VulkanHandleMapping* handlemap,
    VkImageBlit* toMap);

void handlemap_VkBufferImageCopy(
    VulkanHandleMapping* handlemap,
    VkBufferImageCopy* toMap);

void handlemap_VkClearColorValue(
    VulkanHandleMapping* handlemap,
    VkClearColorValue* toMap);

void handlemap_VkClearDepthStencilValue(
    VulkanHandleMapping* handlemap,
    VkClearDepthStencilValue* toMap);

void handlemap_VkClearValue(
    VulkanHandleMapping* handlemap,
    VkClearValue* toMap);

void handlemap_VkClearAttachment(
    VulkanHandleMapping* handlemap,
    VkClearAttachment* toMap);

void handlemap_VkClearRect(
    VulkanHandleMapping* handlemap,
    VkClearRect* toMap);

void handlemap_VkImageResolve(
    VulkanHandleMapping* handlemap,
    VkImageResolve* toMap);

void handlemap_VkMemoryBarrier(
    VulkanHandleMapping* handlemap,
    VkMemoryBarrier* toMap);

void handlemap_VkBufferMemoryBarrier(
    VulkanHandleMapping* handlemap,
    VkBufferMemoryBarrier* toMap);

void handlemap_VkImageMemoryBarrier(
    VulkanHandleMapping* handlemap,
    VkImageMemoryBarrier* toMap);

void handlemap_VkRenderPassBeginInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassBeginInfo* toMap);

void handlemap_VkDispatchIndirectCommand(
    VulkanHandleMapping* handlemap,
    VkDispatchIndirectCommand* toMap);

void handlemap_VkDrawIndexedIndirectCommand(
    VulkanHandleMapping* handlemap,
    VkDrawIndexedIndirectCommand* toMap);

void handlemap_VkDrawIndirectCommand(
    VulkanHandleMapping* handlemap,
    VkDrawIndirectCommand* toMap);

void handlemap_VkBaseOutStructure(
    VulkanHandleMapping* handlemap,
    VkBaseOutStructure* toMap);

void handlemap_VkBaseInStructure(
    VulkanHandleMapping* handlemap,
    VkBaseInStructure* toMap);

#endif
#ifdef VK_VERSION_1_1
void handlemap_VkPhysicalDeviceSubgroupProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSubgroupProperties* toMap);

void handlemap_VkBindBufferMemoryInfo(
    VulkanHandleMapping* handlemap,
    VkBindBufferMemoryInfo* toMap);

void handlemap_VkBindImageMemoryInfo(
    VulkanHandleMapping* handlemap,
    VkBindImageMemoryInfo* toMap);

void handlemap_VkPhysicalDevice16BitStorageFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevice16BitStorageFeatures* toMap);

void handlemap_VkMemoryDedicatedRequirements(
    VulkanHandleMapping* handlemap,
    VkMemoryDedicatedRequirements* toMap);

void handlemap_VkMemoryDedicatedAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkMemoryDedicatedAllocateInfo* toMap);

void handlemap_VkMemoryAllocateFlagsInfo(
    VulkanHandleMapping* handlemap,
    VkMemoryAllocateFlagsInfo* toMap);

void handlemap_VkDeviceGroupRenderPassBeginInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupRenderPassBeginInfo* toMap);

void handlemap_VkDeviceGroupCommandBufferBeginInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupCommandBufferBeginInfo* toMap);

void handlemap_VkDeviceGroupSubmitInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupSubmitInfo* toMap);

void handlemap_VkDeviceGroupBindSparseInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupBindSparseInfo* toMap);

void handlemap_VkBindBufferMemoryDeviceGroupInfo(
    VulkanHandleMapping* handlemap,
    VkBindBufferMemoryDeviceGroupInfo* toMap);

void handlemap_VkBindImageMemoryDeviceGroupInfo(
    VulkanHandleMapping* handlemap,
    VkBindImageMemoryDeviceGroupInfo* toMap);

void handlemap_VkPhysicalDeviceGroupProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceGroupProperties* toMap);

void handlemap_VkDeviceGroupDeviceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupDeviceCreateInfo* toMap);

void handlemap_VkBufferMemoryRequirementsInfo2(
    VulkanHandleMapping* handlemap,
    VkBufferMemoryRequirementsInfo2* toMap);

void handlemap_VkImageMemoryRequirementsInfo2(
    VulkanHandleMapping* handlemap,
    VkImageMemoryRequirementsInfo2* toMap);

void handlemap_VkImageSparseMemoryRequirementsInfo2(
    VulkanHandleMapping* handlemap,
    VkImageSparseMemoryRequirementsInfo2* toMap);

void handlemap_VkMemoryRequirements2(
    VulkanHandleMapping* handlemap,
    VkMemoryRequirements2* toMap);

void handlemap_VkSparseImageMemoryRequirements2(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryRequirements2* toMap);

void handlemap_VkPhysicalDeviceFeatures2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceFeatures2* toMap);

void handlemap_VkPhysicalDeviceProperties2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProperties2* toMap);

void handlemap_VkFormatProperties2(
    VulkanHandleMapping* handlemap,
    VkFormatProperties2* toMap);

void handlemap_VkImageFormatProperties2(
    VulkanHandleMapping* handlemap,
    VkImageFormatProperties2* toMap);

void handlemap_VkPhysicalDeviceImageFormatInfo2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceImageFormatInfo2* toMap);

void handlemap_VkQueueFamilyProperties2(
    VulkanHandleMapping* handlemap,
    VkQueueFamilyProperties2* toMap);

void handlemap_VkPhysicalDeviceMemoryProperties2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMemoryProperties2* toMap);

void handlemap_VkSparseImageFormatProperties2(
    VulkanHandleMapping* handlemap,
    VkSparseImageFormatProperties2* toMap);

void handlemap_VkPhysicalDeviceSparseImageFormatInfo2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSparseImageFormatInfo2* toMap);

void handlemap_VkPhysicalDevicePointClippingProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevicePointClippingProperties* toMap);

void handlemap_VkInputAttachmentAspectReference(
    VulkanHandleMapping* handlemap,
    VkInputAttachmentAspectReference* toMap);

void handlemap_VkRenderPassInputAttachmentAspectCreateInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassInputAttachmentAspectCreateInfo* toMap);

void handlemap_VkImageViewUsageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkImageViewUsageCreateInfo* toMap);

void handlemap_VkPipelineTessellationDomainOriginStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineTessellationDomainOriginStateCreateInfo* toMap);

void handlemap_VkRenderPassMultiviewCreateInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassMultiviewCreateInfo* toMap);

void handlemap_VkPhysicalDeviceMultiviewFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMultiviewFeatures* toMap);

void handlemap_VkPhysicalDeviceMultiviewProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMultiviewProperties* toMap);

void handlemap_VkPhysicalDeviceVariablePointerFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceVariablePointerFeatures* toMap);

void handlemap_VkPhysicalDeviceProtectedMemoryFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProtectedMemoryFeatures* toMap);

void handlemap_VkPhysicalDeviceProtectedMemoryProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProtectedMemoryProperties* toMap);

void handlemap_VkDeviceQueueInfo2(
    VulkanHandleMapping* handlemap,
    VkDeviceQueueInfo2* toMap);

void handlemap_VkProtectedSubmitInfo(
    VulkanHandleMapping* handlemap,
    VkProtectedSubmitInfo* toMap);

void handlemap_VkSamplerYcbcrConversionCreateInfo(
    VulkanHandleMapping* handlemap,
    VkSamplerYcbcrConversionCreateInfo* toMap);

void handlemap_VkSamplerYcbcrConversionInfo(
    VulkanHandleMapping* handlemap,
    VkSamplerYcbcrConversionInfo* toMap);

void handlemap_VkBindImagePlaneMemoryInfo(
    VulkanHandleMapping* handlemap,
    VkBindImagePlaneMemoryInfo* toMap);

void handlemap_VkImagePlaneMemoryRequirementsInfo(
    VulkanHandleMapping* handlemap,
    VkImagePlaneMemoryRequirementsInfo* toMap);

void handlemap_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* toMap);

void handlemap_VkSamplerYcbcrConversionImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkSamplerYcbcrConversionImageFormatProperties* toMap);

void handlemap_VkDescriptorUpdateTemplateEntry(
    VulkanHandleMapping* handlemap,
    VkDescriptorUpdateTemplateEntry* toMap);

void handlemap_VkDescriptorUpdateTemplateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorUpdateTemplateCreateInfo* toMap);

void handlemap_VkExternalMemoryProperties(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryProperties* toMap);

void handlemap_VkPhysicalDeviceExternalImageFormatInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalImageFormatInfo* toMap);

void handlemap_VkExternalImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkExternalImageFormatProperties* toMap);

void handlemap_VkPhysicalDeviceExternalBufferInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalBufferInfo* toMap);

void handlemap_VkExternalBufferProperties(
    VulkanHandleMapping* handlemap,
    VkExternalBufferProperties* toMap);

void handlemap_VkPhysicalDeviceIDProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceIDProperties* toMap);

void handlemap_VkExternalMemoryImageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryImageCreateInfo* toMap);

void handlemap_VkExternalMemoryBufferCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryBufferCreateInfo* toMap);

void handlemap_VkExportMemoryAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkExportMemoryAllocateInfo* toMap);

void handlemap_VkPhysicalDeviceExternalFenceInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalFenceInfo* toMap);

void handlemap_VkExternalFenceProperties(
    VulkanHandleMapping* handlemap,
    VkExternalFenceProperties* toMap);

void handlemap_VkExportFenceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExportFenceCreateInfo* toMap);

void handlemap_VkExportSemaphoreCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExportSemaphoreCreateInfo* toMap);

void handlemap_VkPhysicalDeviceExternalSemaphoreInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalSemaphoreInfo* toMap);

void handlemap_VkExternalSemaphoreProperties(
    VulkanHandleMapping* handlemap,
    VkExternalSemaphoreProperties* toMap);

void handlemap_VkPhysicalDeviceMaintenance3Properties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMaintenance3Properties* toMap);

void handlemap_VkDescriptorSetLayoutSupport(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutSupport* toMap);

void handlemap_VkPhysicalDeviceShaderDrawParameterFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceShaderDrawParameterFeatures* toMap);

#endif
#ifdef VK_KHR_surface
void handlemap_VkSurfaceCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceCapabilitiesKHR* toMap);

void handlemap_VkSurfaceFormatKHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceFormatKHR* toMap);

#endif
#ifdef VK_KHR_swapchain
void handlemap_VkSwapchainCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSwapchainCreateInfoKHR* toMap);

void handlemap_VkPresentInfoKHR(
    VulkanHandleMapping* handlemap,
    VkPresentInfoKHR* toMap);

void handlemap_VkImageSwapchainCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImageSwapchainCreateInfoKHR* toMap);

void handlemap_VkBindImageMemorySwapchainInfoKHR(
    VulkanHandleMapping* handlemap,
    VkBindImageMemorySwapchainInfoKHR* toMap);

void handlemap_VkAcquireNextImageInfoKHR(
    VulkanHandleMapping* handlemap,
    VkAcquireNextImageInfoKHR* toMap);

void handlemap_VkDeviceGroupPresentCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupPresentCapabilitiesKHR* toMap);

void handlemap_VkDeviceGroupPresentInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupPresentInfoKHR* toMap);

void handlemap_VkDeviceGroupSwapchainCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupSwapchainCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_display
void handlemap_VkDisplayPropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPropertiesKHR* toMap);

void handlemap_VkDisplayModeParametersKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModeParametersKHR* toMap);

void handlemap_VkDisplayModePropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModePropertiesKHR* toMap);

void handlemap_VkDisplayModeCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModeCreateInfoKHR* toMap);

void handlemap_VkDisplayPlaneCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneCapabilitiesKHR* toMap);

void handlemap_VkDisplayPlanePropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlanePropertiesKHR* toMap);

void handlemap_VkDisplaySurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDisplaySurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_display_swapchain
void handlemap_VkDisplayPresentInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPresentInfoKHR* toMap);

#endif
#ifdef VK_KHR_xlib_surface
void handlemap_VkXlibSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkXlibSurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_xcb_surface
void handlemap_VkXcbSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkXcbSurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_wayland_surface
void handlemap_VkWaylandSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkWaylandSurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_mir_surface
void handlemap_VkMirSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkMirSurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_android_surface
void handlemap_VkAndroidSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkAndroidSurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_win32_surface
void handlemap_VkWin32SurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkWin32SurfaceCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#endif
#ifdef VK_KHR_device_group
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#endif
#ifdef VK_KHR_device_group_creation
#endif
#ifdef VK_KHR_external_memory_capabilities
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void handlemap_VkImportMemoryWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportMemoryWin32HandleInfoKHR* toMap);

void handlemap_VkExportMemoryWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkExportMemoryWin32HandleInfoKHR* toMap);

void handlemap_VkMemoryWin32HandlePropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryWin32HandlePropertiesKHR* toMap);

void handlemap_VkMemoryGetWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryGetWin32HandleInfoKHR* toMap);

#endif
#ifdef VK_KHR_external_memory_fd
void handlemap_VkImportMemoryFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportMemoryFdInfoKHR* toMap);

void handlemap_VkMemoryFdPropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryFdPropertiesKHR* toMap);

void handlemap_VkMemoryGetFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryGetFdInfoKHR* toMap);

#endif
#ifdef VK_KHR_win32_keyed_mutex
void handlemap_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    VulkanHandleMapping* handlemap,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* toMap);

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void handlemap_VkImportSemaphoreWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportSemaphoreWin32HandleInfoKHR* toMap);

void handlemap_VkExportSemaphoreWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkExportSemaphoreWin32HandleInfoKHR* toMap);

void handlemap_VkD3D12FenceSubmitInfoKHR(
    VulkanHandleMapping* handlemap,
    VkD3D12FenceSubmitInfoKHR* toMap);

void handlemap_VkSemaphoreGetWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSemaphoreGetWin32HandleInfoKHR* toMap);

#endif
#ifdef VK_KHR_external_semaphore_fd
void handlemap_VkImportSemaphoreFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportSemaphoreFdInfoKHR* toMap);

void handlemap_VkSemaphoreGetFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSemaphoreGetFdInfoKHR* toMap);

#endif
#ifdef VK_KHR_push_descriptor
void handlemap_VkPhysicalDevicePushDescriptorPropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevicePushDescriptorPropertiesKHR* toMap);

#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void handlemap_VkRectLayerKHR(
    VulkanHandleMapping* handlemap,
    VkRectLayerKHR* toMap);

void handlemap_VkPresentRegionKHR(
    VulkanHandleMapping* handlemap,
    VkPresentRegionKHR* toMap);

void handlemap_VkPresentRegionsKHR(
    VulkanHandleMapping* handlemap,
    VkPresentRegionsKHR* toMap);

#endif
#ifdef VK_KHR_descriptor_update_template
#endif
#ifdef VK_KHR_create_renderpass2
void handlemap_VkAttachmentDescription2KHR(
    VulkanHandleMapping* handlemap,
    VkAttachmentDescription2KHR* toMap);

void handlemap_VkAttachmentReference2KHR(
    VulkanHandleMapping* handlemap,
    VkAttachmentReference2KHR* toMap);

void handlemap_VkSubpassDescription2KHR(
    VulkanHandleMapping* handlemap,
    VkSubpassDescription2KHR* toMap);

void handlemap_VkSubpassDependency2KHR(
    VulkanHandleMapping* handlemap,
    VkSubpassDependency2KHR* toMap);

void handlemap_VkRenderPassCreateInfo2KHR(
    VulkanHandleMapping* handlemap,
    VkRenderPassCreateInfo2KHR* toMap);

void handlemap_VkSubpassBeginInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSubpassBeginInfoKHR* toMap);

void handlemap_VkSubpassEndInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSubpassEndInfoKHR* toMap);

#endif
#ifdef VK_KHR_shared_presentable_image
void handlemap_VkSharedPresentSurfaceCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkSharedPresentSurfaceCapabilitiesKHR* toMap);

#endif
#ifdef VK_KHR_external_fence_capabilities
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void handlemap_VkImportFenceWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportFenceWin32HandleInfoKHR* toMap);

void handlemap_VkExportFenceWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkExportFenceWin32HandleInfoKHR* toMap);

void handlemap_VkFenceGetWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkFenceGetWin32HandleInfoKHR* toMap);

#endif
#ifdef VK_KHR_external_fence_fd
void handlemap_VkImportFenceFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportFenceFdInfoKHR* toMap);

void handlemap_VkFenceGetFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkFenceGetFdInfoKHR* toMap);

#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void handlemap_VkPhysicalDeviceSurfaceInfo2KHR(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSurfaceInfo2KHR* toMap);

void handlemap_VkSurfaceCapabilities2KHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceCapabilities2KHR* toMap);

void handlemap_VkSurfaceFormat2KHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceFormat2KHR* toMap);

#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void handlemap_VkDisplayProperties2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayProperties2KHR* toMap);

void handlemap_VkDisplayPlaneProperties2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneProperties2KHR* toMap);

void handlemap_VkDisplayModeProperties2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModeProperties2KHR* toMap);

void handlemap_VkDisplayPlaneInfo2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneInfo2KHR* toMap);

void handlemap_VkDisplayPlaneCapabilities2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneCapabilities2KHR* toMap);

#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#endif
#ifdef VK_KHR_image_format_list
void handlemap_VkImageFormatListCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImageFormatListCreateInfoKHR* toMap);

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#endif
#ifdef VK_KHR_bind_memory2
#endif
#ifdef VK_KHR_maintenance3
#endif
#ifdef VK_KHR_draw_indirect_count
#endif
#ifdef VK_KHR_8bit_storage
void handlemap_VkPhysicalDevice8BitStorageFeaturesKHR(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevice8BitStorageFeaturesKHR* toMap);

#endif
#ifdef VK_ANDROID_native_buffer
void handlemap_VkNativeBufferANDROID(
    VulkanHandleMapping* handlemap,
    VkNativeBufferANDROID* toMap);

#endif
#ifdef VK_EXT_debug_report
void handlemap_VkDebugReportCallbackCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugReportCallbackCreateInfoEXT* toMap);

#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void handlemap_VkPipelineRasterizationStateRasterizationOrderAMD(
    VulkanHandleMapping* handlemap,
    VkPipelineRasterizationStateRasterizationOrderAMD* toMap);

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void handlemap_VkDebugMarkerObjectNameInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugMarkerObjectNameInfoEXT* toMap);

void handlemap_VkDebugMarkerObjectTagInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugMarkerObjectTagInfoEXT* toMap);

void handlemap_VkDebugMarkerMarkerInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugMarkerMarkerInfoEXT* toMap);

#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void handlemap_VkDedicatedAllocationImageCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkDedicatedAllocationImageCreateInfoNV* toMap);

void handlemap_VkDedicatedAllocationBufferCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkDedicatedAllocationBufferCreateInfoNV* toMap);

void handlemap_VkDedicatedAllocationMemoryAllocateInfoNV(
    VulkanHandleMapping* handlemap,
    VkDedicatedAllocationMemoryAllocateInfoNV* toMap);

#endif
#ifdef VK_AMD_draw_indirect_count
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void handlemap_VkTextureLODGatherFormatPropertiesAMD(
    VulkanHandleMapping* handlemap,
    VkTextureLODGatherFormatPropertiesAMD* toMap);

#endif
#ifdef VK_AMD_shader_info
void handlemap_VkShaderResourceUsageAMD(
    VulkanHandleMapping* handlemap,
    VkShaderResourceUsageAMD* toMap);

void handlemap_VkShaderStatisticsInfoAMD(
    VulkanHandleMapping* handlemap,
    VkShaderStatisticsInfoAMD* toMap);

#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void handlemap_VkExternalImageFormatPropertiesNV(
    VulkanHandleMapping* handlemap,
    VkExternalImageFormatPropertiesNV* toMap);

#endif
#ifdef VK_NV_external_memory
void handlemap_VkExternalMemoryImageCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryImageCreateInfoNV* toMap);

void handlemap_VkExportMemoryAllocateInfoNV(
    VulkanHandleMapping* handlemap,
    VkExportMemoryAllocateInfoNV* toMap);

#endif
#ifdef VK_NV_external_memory_win32
void handlemap_VkImportMemoryWin32HandleInfoNV(
    VulkanHandleMapping* handlemap,
    VkImportMemoryWin32HandleInfoNV* toMap);

void handlemap_VkExportMemoryWin32HandleInfoNV(
    VulkanHandleMapping* handlemap,
    VkExportMemoryWin32HandleInfoNV* toMap);

#endif
#ifdef VK_NV_win32_keyed_mutex
void handlemap_VkWin32KeyedMutexAcquireReleaseInfoNV(
    VulkanHandleMapping* handlemap,
    VkWin32KeyedMutexAcquireReleaseInfoNV* toMap);

#endif
#ifdef VK_EXT_validation_flags
void handlemap_VkValidationFlagsEXT(
    VulkanHandleMapping* handlemap,
    VkValidationFlagsEXT* toMap);

#endif
#ifdef VK_NN_vi_surface
void handlemap_VkViSurfaceCreateInfoNN(
    VulkanHandleMapping* handlemap,
    VkViSurfaceCreateInfoNN* toMap);

#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void handlemap_VkConditionalRenderingBeginInfoEXT(
    VulkanHandleMapping* handlemap,
    VkConditionalRenderingBeginInfoEXT* toMap);

void handlemap_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* toMap);

void handlemap_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    VulkanHandleMapping* handlemap,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* toMap);

#endif
#ifdef VK_NVX_device_generated_commands
void handlemap_VkDeviceGeneratedCommandsFeaturesNVX(
    VulkanHandleMapping* handlemap,
    VkDeviceGeneratedCommandsFeaturesNVX* toMap);

void handlemap_VkDeviceGeneratedCommandsLimitsNVX(
    VulkanHandleMapping* handlemap,
    VkDeviceGeneratedCommandsLimitsNVX* toMap);

void handlemap_VkIndirectCommandsTokenNVX(
    VulkanHandleMapping* handlemap,
    VkIndirectCommandsTokenNVX* toMap);

void handlemap_VkIndirectCommandsLayoutTokenNVX(
    VulkanHandleMapping* handlemap,
    VkIndirectCommandsLayoutTokenNVX* toMap);

void handlemap_VkIndirectCommandsLayoutCreateInfoNVX(
    VulkanHandleMapping* handlemap,
    VkIndirectCommandsLayoutCreateInfoNVX* toMap);

void handlemap_VkCmdProcessCommandsInfoNVX(
    VulkanHandleMapping* handlemap,
    VkCmdProcessCommandsInfoNVX* toMap);

void handlemap_VkCmdReserveSpaceForCommandsInfoNVX(
    VulkanHandleMapping* handlemap,
    VkCmdReserveSpaceForCommandsInfoNVX* toMap);

void handlemap_VkObjectTableCreateInfoNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableCreateInfoNVX* toMap);

void handlemap_VkObjectTableEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableEntryNVX* toMap);

void handlemap_VkObjectTablePipelineEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTablePipelineEntryNVX* toMap);

void handlemap_VkObjectTableDescriptorSetEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableDescriptorSetEntryNVX* toMap);

void handlemap_VkObjectTableVertexBufferEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableVertexBufferEntryNVX* toMap);

void handlemap_VkObjectTableIndexBufferEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableIndexBufferEntryNVX* toMap);

void handlemap_VkObjectTablePushConstantEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTablePushConstantEntryNVX* toMap);

#endif
#ifdef VK_NV_clip_space_w_scaling
void handlemap_VkViewportWScalingNV(
    VulkanHandleMapping* handlemap,
    VkViewportWScalingNV* toMap);

void handlemap_VkPipelineViewportWScalingStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineViewportWScalingStateCreateInfoNV* toMap);

#endif
#ifdef VK_EXT_direct_mode_display
#endif
#ifdef VK_EXT_acquire_xlib_display
#endif
#ifdef VK_EXT_display_surface_counter
void handlemap_VkSurfaceCapabilities2EXT(
    VulkanHandleMapping* handlemap,
    VkSurfaceCapabilities2EXT* toMap);

#endif
#ifdef VK_EXT_display_control
void handlemap_VkDisplayPowerInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDisplayPowerInfoEXT* toMap);

void handlemap_VkDeviceEventInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDeviceEventInfoEXT* toMap);

void handlemap_VkDisplayEventInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDisplayEventInfoEXT* toMap);

void handlemap_VkSwapchainCounterCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkSwapchainCounterCreateInfoEXT* toMap);

#endif
#ifdef VK_GOOGLE_display_timing
void handlemap_VkRefreshCycleDurationGOOGLE(
    VulkanHandleMapping* handlemap,
    VkRefreshCycleDurationGOOGLE* toMap);

void handlemap_VkPastPresentationTimingGOOGLE(
    VulkanHandleMapping* handlemap,
    VkPastPresentationTimingGOOGLE* toMap);

void handlemap_VkPresentTimeGOOGLE(
    VulkanHandleMapping* handlemap,
    VkPresentTimeGOOGLE* toMap);

void handlemap_VkPresentTimesInfoGOOGLE(
    VulkanHandleMapping* handlemap,
    VkPresentTimesInfoGOOGLE* toMap);

#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void handlemap_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* toMap);

#endif
#ifdef VK_NV_viewport_swizzle
void handlemap_VkViewportSwizzleNV(
    VulkanHandleMapping* handlemap,
    VkViewportSwizzleNV* toMap);

void handlemap_VkPipelineViewportSwizzleStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineViewportSwizzleStateCreateInfoNV* toMap);

#endif
#ifdef VK_EXT_discard_rectangles
void handlemap_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* toMap);

void handlemap_VkPipelineDiscardRectangleStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineDiscardRectangleStateCreateInfoEXT* toMap);

#endif
#ifdef VK_EXT_conservative_rasterization
void handlemap_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* toMap);

void handlemap_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* toMap);

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void handlemap_VkXYColorEXT(
    VulkanHandleMapping* handlemap,
    VkXYColorEXT* toMap);

void handlemap_VkHdrMetadataEXT(
    VulkanHandleMapping* handlemap,
    VkHdrMetadataEXT* toMap);

#endif
#ifdef VK_MVK_ios_surface
void handlemap_VkIOSSurfaceCreateInfoMVK(
    VulkanHandleMapping* handlemap,
    VkIOSSurfaceCreateInfoMVK* toMap);

#endif
#ifdef VK_MVK_macos_surface
void handlemap_VkMacOSSurfaceCreateInfoMVK(
    VulkanHandleMapping* handlemap,
    VkMacOSSurfaceCreateInfoMVK* toMap);

#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void handlemap_VkDebugUtilsObjectNameInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsObjectNameInfoEXT* toMap);

void handlemap_VkDebugUtilsObjectTagInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsObjectTagInfoEXT* toMap);

void handlemap_VkDebugUtilsLabelEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsLabelEXT* toMap);

void handlemap_VkDebugUtilsMessengerCallbackDataEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsMessengerCallbackDataEXT* toMap);

void handlemap_VkDebugUtilsMessengerCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsMessengerCreateInfoEXT* toMap);

#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void handlemap_VkAndroidHardwareBufferUsageANDROID(
    VulkanHandleMapping* handlemap,
    VkAndroidHardwareBufferUsageANDROID* toMap);

void handlemap_VkAndroidHardwareBufferPropertiesANDROID(
    VulkanHandleMapping* handlemap,
    VkAndroidHardwareBufferPropertiesANDROID* toMap);

void handlemap_VkAndroidHardwareBufferFormatPropertiesANDROID(
    VulkanHandleMapping* handlemap,
    VkAndroidHardwareBufferFormatPropertiesANDROID* toMap);

void handlemap_VkImportAndroidHardwareBufferInfoANDROID(
    VulkanHandleMapping* handlemap,
    VkImportAndroidHardwareBufferInfoANDROID* toMap);

void handlemap_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    VulkanHandleMapping* handlemap,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* toMap);

void handlemap_VkExternalFormatANDROID(
    VulkanHandleMapping* handlemap,
    VkExternalFormatANDROID* toMap);

#endif
#ifdef VK_EXT_sampler_filter_minmax
void handlemap_VkSamplerReductionModeCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkSamplerReductionModeCreateInfoEXT* toMap);

void handlemap_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* toMap);

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void handlemap_VkSampleLocationEXT(
    VulkanHandleMapping* handlemap,
    VkSampleLocationEXT* toMap);

void handlemap_VkSampleLocationsInfoEXT(
    VulkanHandleMapping* handlemap,
    VkSampleLocationsInfoEXT* toMap);

void handlemap_VkAttachmentSampleLocationsEXT(
    VulkanHandleMapping* handlemap,
    VkAttachmentSampleLocationsEXT* toMap);

void handlemap_VkSubpassSampleLocationsEXT(
    VulkanHandleMapping* handlemap,
    VkSubpassSampleLocationsEXT* toMap);

void handlemap_VkRenderPassSampleLocationsBeginInfoEXT(
    VulkanHandleMapping* handlemap,
    VkRenderPassSampleLocationsBeginInfoEXT* toMap);

void handlemap_VkPipelineSampleLocationsStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineSampleLocationsStateCreateInfoEXT* toMap);

void handlemap_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* toMap);

void handlemap_VkMultisamplePropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkMultisamplePropertiesEXT* toMap);

#endif
#ifdef VK_EXT_blend_operation_advanced
void handlemap_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* toMap);

void handlemap_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* toMap);

void handlemap_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* toMap);

#endif
#ifdef VK_NV_fragment_coverage_to_color
void handlemap_VkPipelineCoverageToColorStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineCoverageToColorStateCreateInfoNV* toMap);

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void handlemap_VkPipelineCoverageModulationStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineCoverageModulationStateCreateInfoNV* toMap);

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void handlemap_VkValidationCacheCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkValidationCacheCreateInfoEXT* toMap);

void handlemap_VkShaderModuleValidationCacheCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkShaderModuleValidationCacheCreateInfoEXT* toMap);

#endif
#ifdef VK_EXT_descriptor_indexing
void handlemap_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* toMap);

void handlemap_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* toMap);

void handlemap_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* toMap);

void handlemap_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* toMap);

void handlemap_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* toMap);

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void handlemap_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* toMap);

#endif
#ifdef VK_EXT_external_memory_host
void handlemap_VkImportMemoryHostPointerInfoEXT(
    VulkanHandleMapping* handlemap,
    VkImportMemoryHostPointerInfoEXT* toMap);

void handlemap_VkMemoryHostPointerPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkMemoryHostPointerPropertiesEXT* toMap);

void handlemap_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* toMap);

#endif
#ifdef VK_AMD_buffer_marker
#endif
#ifdef VK_AMD_shader_core_properties
void handlemap_VkPhysicalDeviceShaderCorePropertiesAMD(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceShaderCorePropertiesAMD* toMap);

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void handlemap_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* toMap);

void handlemap_VkVertexInputBindingDivisorDescriptionEXT(
    VulkanHandleMapping* handlemap,
    VkVertexInputBindingDivisorDescriptionEXT* toMap);

void handlemap_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* toMap);

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void handlemap_VkQueueFamilyCheckpointPropertiesNV(
    VulkanHandleMapping* handlemap,
    VkQueueFamilyCheckpointPropertiesNV* toMap);

void handlemap_VkCheckpointDataNV(
    VulkanHandleMapping* handlemap,
    VkCheckpointDataNV* toMap);

#endif
#ifdef VK_GOOGLE_address_space
#endif
#ifdef VK_GOOGLE_color_buffer
void handlemap_VkImportColorBufferGOOGLE(
    VulkanHandleMapping* handlemap,
    VkImportColorBufferGOOGLE* toMap);

void handlemap_VkImportPhysicalAddressGOOGLE(
    VulkanHandleMapping* handlemap,
    VkImportPhysicalAddressGOOGLE* toMap);

#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#endif
#ifdef VK_GOOGLE_async_command_buffers
#endif

} // namespace goldfish_vk
