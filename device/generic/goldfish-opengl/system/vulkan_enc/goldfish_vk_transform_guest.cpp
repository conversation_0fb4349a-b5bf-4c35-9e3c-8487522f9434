// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_transform_guest
// (impl) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#include "goldfish_vk_transform_guest.h"


#include "goldfish_vk_extension_structs_guest.h"
#include "goldfish_vk_private_defs.h"

#include "ResourceTracker.h"


namespace goldfish_vk {

void transform_tohost_extension_struct(
    ResourceTracker* resourceTracker,
    void* structExtension_out);

void transform_fromhost_extension_struct(
    ResourceTracker* resourceTracker,
    void* structExtension_out);

#ifdef VK_VERSION_1_0
void transform_tohost_VkApplicationInfo(
    ResourceTracker* resourceTracker,
    VkApplicationInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkApplicationInfo(
    ResourceTracker* resourceTracker,
    VkApplicationInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkInstanceCreateInfo(
    ResourceTracker* resourceTracker,
    VkInstanceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pApplicationInfo)
    {
        transform_tohost_VkApplicationInfo(resourceTracker, (VkApplicationInfo*)(toTransform->pApplicationInfo));
    }
}

void transform_fromhost_VkInstanceCreateInfo(
    ResourceTracker* resourceTracker,
    VkInstanceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pApplicationInfo)
    {
        transform_fromhost_VkApplicationInfo(resourceTracker, (VkApplicationInfo*)(toTransform->pApplicationInfo));
    }
}

void transform_tohost_VkAllocationCallbacks(
    ResourceTracker* resourceTracker,
    VkAllocationCallbacks* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkAllocationCallbacks(
    ResourceTracker* resourceTracker,
    VkAllocationCallbacks* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPhysicalDeviceFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPhysicalDeviceFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkFormatProperties(
    ResourceTracker* resourceTracker,
    VkFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkFormatProperties(
    ResourceTracker* resourceTracker,
    VkFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkExtent3D(
    ResourceTracker* resourceTracker,
    VkExtent3D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkExtent3D(
    ResourceTracker* resourceTracker,
    VkExtent3D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->maxExtent));
}

void transform_fromhost_VkImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->maxExtent));
}

void transform_tohost_VkPhysicalDeviceLimits(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceLimits* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPhysicalDeviceLimits(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceLimits* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPhysicalDeviceSparseProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPhysicalDeviceSparseProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPhysicalDeviceProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkPhysicalDeviceLimits(resourceTracker, (VkPhysicalDeviceLimits*)(&toTransform->limits));
    transform_tohost_VkPhysicalDeviceSparseProperties(resourceTracker, (VkPhysicalDeviceSparseProperties*)(&toTransform->sparseProperties));
}

void transform_fromhost_VkPhysicalDeviceProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkPhysicalDeviceLimits(resourceTracker, (VkPhysicalDeviceLimits*)(&toTransform->limits));
    transform_fromhost_VkPhysicalDeviceSparseProperties(resourceTracker, (VkPhysicalDeviceSparseProperties*)(&toTransform->sparseProperties));
}

void transform_tohost_VkQueueFamilyProperties(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->minImageTransferGranularity));
}

void transform_fromhost_VkQueueFamilyProperties(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->minImageTransferGranularity));
}

void transform_tohost_VkMemoryType(
    ResourceTracker* resourceTracker,
    VkMemoryType* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkMemoryType(
    ResourceTracker* resourceTracker,
    VkMemoryType* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkMemoryHeap(
    ResourceTracker* resourceTracker,
    VkMemoryHeap* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkMemoryHeap(
    ResourceTracker* resourceTracker,
    VkMemoryHeap* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPhysicalDeviceMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_TYPES; ++i)
    {
        transform_tohost_VkMemoryType(resourceTracker, (VkMemoryType*)(toTransform->memoryTypes + i));
    }
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_HEAPS; ++i)
    {
        transform_tohost_VkMemoryHeap(resourceTracker, (VkMemoryHeap*)(toTransform->memoryHeaps + i));
    }
}

void transform_fromhost_VkPhysicalDeviceMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_TYPES; ++i)
    {
        transform_fromhost_VkMemoryType(resourceTracker, (VkMemoryType*)(toTransform->memoryTypes + i));
    }
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_HEAPS; ++i)
    {
        transform_fromhost_VkMemoryHeap(resourceTracker, (VkMemoryHeap*)(toTransform->memoryHeaps + i));
    }
}

void transform_tohost_VkDeviceQueueCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceQueueCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceQueueCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceQueueCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pQueueCreateInfos)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->queueCreateInfoCount; ++i)
        {
            transform_tohost_VkDeviceQueueCreateInfo(resourceTracker, (VkDeviceQueueCreateInfo*)(toTransform->pQueueCreateInfos + i));
        }
    }
    if (toTransform->pEnabledFeatures)
    {
        transform_tohost_VkPhysicalDeviceFeatures(resourceTracker, (VkPhysicalDeviceFeatures*)(toTransform->pEnabledFeatures));
    }
}

void transform_fromhost_VkDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pQueueCreateInfos)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->queueCreateInfoCount; ++i)
        {
            transform_fromhost_VkDeviceQueueCreateInfo(resourceTracker, (VkDeviceQueueCreateInfo*)(toTransform->pQueueCreateInfos + i));
        }
    }
    if (toTransform->pEnabledFeatures)
    {
        transform_fromhost_VkPhysicalDeviceFeatures(resourceTracker, (VkPhysicalDeviceFeatures*)(toTransform->pEnabledFeatures));
    }
}

void transform_tohost_VkExtensionProperties(
    ResourceTracker* resourceTracker,
    VkExtensionProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkExtensionProperties(
    ResourceTracker* resourceTracker,
    VkExtensionProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkLayerProperties(
    ResourceTracker* resourceTracker,
    VkLayerProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkLayerProperties(
    ResourceTracker* resourceTracker,
    VkLayerProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkSubmitInfo(
    ResourceTracker* resourceTracker,
    VkSubmitInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSubmitInfo(
    ResourceTracker* resourceTracker,
    VkSubmitInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeIndex, 1, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeIndex, 1, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMappedMemoryRange(
    ResourceTracker* resourceTracker,
    VkMappedMemoryRange* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->offset, 1, (VkDeviceSize*)&toTransform->size, 1, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMappedMemoryRange(
    ResourceTracker* resourceTracker,
    VkMappedMemoryRange* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->offset, 1, (VkDeviceSize*)&toTransform->size, 1, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
}

void transform_fromhost_VkMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
}

void transform_tohost_VkSparseImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->imageGranularity));
}

void transform_fromhost_VkSparseImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->imageGranularity));
}

void transform_tohost_VkSparseImageMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkSparseImageFormatProperties(resourceTracker, (VkSparseImageFormatProperties*)(&toTransform->formatProperties));
}

void transform_fromhost_VkSparseImageMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkSparseImageFormatProperties(resourceTracker, (VkSparseImageFormatProperties*)(&toTransform->formatProperties));
}

void transform_tohost_VkSparseMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseMemoryBind* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
}

void transform_fromhost_VkSparseMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseMemoryBind* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
}

void transform_tohost_VkSparseBufferMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseBufferMemoryBindInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindCount; ++i)
        {
            transform_tohost_VkSparseMemoryBind(resourceTracker, (VkSparseMemoryBind*)(toTransform->pBinds + i));
        }
    }
}

void transform_fromhost_VkSparseBufferMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseBufferMemoryBindInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindCount; ++i)
        {
            transform_fromhost_VkSparseMemoryBind(resourceTracker, (VkSparseMemoryBind*)(toTransform->pBinds + i));
        }
    }
}

void transform_tohost_VkSparseImageOpaqueMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageOpaqueMemoryBindInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindCount; ++i)
        {
            transform_tohost_VkSparseMemoryBind(resourceTracker, (VkSparseMemoryBind*)(toTransform->pBinds + i));
        }
    }
}

void transform_fromhost_VkSparseImageOpaqueMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageOpaqueMemoryBindInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindCount; ++i)
        {
            transform_fromhost_VkSparseMemoryBind(resourceTracker, (VkSparseMemoryBind*)(toTransform->pBinds + i));
        }
    }
}

void transform_tohost_VkImageSubresource(
    ResourceTracker* resourceTracker,
    VkImageSubresource* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkImageSubresource(
    ResourceTracker* resourceTracker,
    VkImageSubresource* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkOffset3D(
    ResourceTracker* resourceTracker,
    VkOffset3D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkOffset3D(
    ResourceTracker* resourceTracker,
    VkOffset3D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkSparseImageMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBind* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    transform_tohost_VkImageSubresource(resourceTracker, (VkImageSubresource*)(&toTransform->subresource));
    transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->offset));
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_fromhost_VkSparseImageMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBind* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    transform_fromhost_VkImageSubresource(resourceTracker, (VkImageSubresource*)(&toTransform->subresource));
    transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->offset));
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_tohost_VkSparseImageMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBindInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindCount; ++i)
        {
            transform_tohost_VkSparseImageMemoryBind(resourceTracker, (VkSparseImageMemoryBind*)(toTransform->pBinds + i));
        }
    }
}

void transform_fromhost_VkSparseImageMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBindInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindCount; ++i)
        {
            transform_fromhost_VkSparseImageMemoryBind(resourceTracker, (VkSparseImageMemoryBind*)(toTransform->pBinds + i));
        }
    }
}

void transform_tohost_VkBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkBindSparseInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pBufferBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bufferBindCount; ++i)
        {
            transform_tohost_VkSparseBufferMemoryBindInfo(resourceTracker, (VkSparseBufferMemoryBindInfo*)(toTransform->pBufferBinds + i));
        }
    }
    if (toTransform->pImageOpaqueBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->imageOpaqueBindCount; ++i)
        {
            transform_tohost_VkSparseImageOpaqueMemoryBindInfo(resourceTracker, (VkSparseImageOpaqueMemoryBindInfo*)(toTransform->pImageOpaqueBinds + i));
        }
    }
    if (toTransform->pImageBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->imageBindCount; ++i)
        {
            transform_tohost_VkSparseImageMemoryBindInfo(resourceTracker, (VkSparseImageMemoryBindInfo*)(toTransform->pImageBinds + i));
        }
    }
}

void transform_fromhost_VkBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkBindSparseInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pBufferBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bufferBindCount; ++i)
        {
            transform_fromhost_VkSparseBufferMemoryBindInfo(resourceTracker, (VkSparseBufferMemoryBindInfo*)(toTransform->pBufferBinds + i));
        }
    }
    if (toTransform->pImageOpaqueBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->imageOpaqueBindCount; ++i)
        {
            transform_fromhost_VkSparseImageOpaqueMemoryBindInfo(resourceTracker, (VkSparseImageOpaqueMemoryBindInfo*)(toTransform->pImageOpaqueBinds + i));
        }
    }
    if (toTransform->pImageBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->imageBindCount; ++i)
        {
            transform_fromhost_VkSparseImageMemoryBindInfo(resourceTracker, (VkSparseImageMemoryBindInfo*)(toTransform->pImageBinds + i));
        }
    }
}

void transform_tohost_VkFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkFenceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkFenceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkSemaphoreCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkSemaphoreCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkEventCreateInfo(
    ResourceTracker* resourceTracker,
    VkEventCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkEventCreateInfo(
    ResourceTracker* resourceTracker,
    VkEventCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkQueryPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkQueryPoolCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkQueryPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkQueryPoolCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBufferViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferViewCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBufferViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferViewCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_fromhost_VkImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_tohost_VkSubresourceLayout(
    ResourceTracker* resourceTracker,
    VkSubresourceLayout* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkSubresourceLayout(
    ResourceTracker* resourceTracker,
    VkSubresourceLayout* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkComponentMapping(
    ResourceTracker* resourceTracker,
    VkComponentMapping* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkComponentMapping(
    ResourceTracker* resourceTracker,
    VkComponentMapping* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkImageSubresourceRange(
    ResourceTracker* resourceTracker,
    VkImageSubresourceRange* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkImageSubresourceRange(
    ResourceTracker* resourceTracker,
    VkImageSubresourceRange* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkImageViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkComponentMapping(resourceTracker, (VkComponentMapping*)(&toTransform->components));
    transform_tohost_VkImageSubresourceRange(resourceTracker, (VkImageSubresourceRange*)(&toTransform->subresourceRange));
}

void transform_fromhost_VkImageViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkComponentMapping(resourceTracker, (VkComponentMapping*)(&toTransform->components));
    transform_fromhost_VkImageSubresourceRange(resourceTracker, (VkImageSubresourceRange*)(&toTransform->subresourceRange));
}

void transform_tohost_VkShaderModuleCreateInfo(
    ResourceTracker* resourceTracker,
    VkShaderModuleCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkShaderModuleCreateInfo(
    ResourceTracker* resourceTracker,
    VkShaderModuleCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineCacheCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineCacheCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineCacheCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineCacheCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSpecializationMapEntry(
    ResourceTracker* resourceTracker,
    VkSpecializationMapEntry* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkSpecializationMapEntry(
    ResourceTracker* resourceTracker,
    VkSpecializationMapEntry* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkSpecializationInfo(
    ResourceTracker* resourceTracker,
    VkSpecializationInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pMapEntries)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->mapEntryCount; ++i)
        {
            transform_tohost_VkSpecializationMapEntry(resourceTracker, (VkSpecializationMapEntry*)(toTransform->pMapEntries + i));
        }
    }
}

void transform_fromhost_VkSpecializationInfo(
    ResourceTracker* resourceTracker,
    VkSpecializationInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pMapEntries)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->mapEntryCount; ++i)
        {
            transform_fromhost_VkSpecializationMapEntry(resourceTracker, (VkSpecializationMapEntry*)(toTransform->pMapEntries + i));
        }
    }
}

void transform_tohost_VkPipelineShaderStageCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineShaderStageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pSpecializationInfo)
    {
        transform_tohost_VkSpecializationInfo(resourceTracker, (VkSpecializationInfo*)(toTransform->pSpecializationInfo));
    }
}

void transform_fromhost_VkPipelineShaderStageCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineShaderStageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pSpecializationInfo)
    {
        transform_fromhost_VkSpecializationInfo(resourceTracker, (VkSpecializationInfo*)(toTransform->pSpecializationInfo));
    }
}

void transform_tohost_VkVertexInputBindingDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkVertexInputBindingDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkVertexInputAttributeDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputAttributeDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkVertexInputAttributeDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputAttributeDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineVertexInputStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pVertexBindingDescriptions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->vertexBindingDescriptionCount; ++i)
        {
            transform_tohost_VkVertexInputBindingDescription(resourceTracker, (VkVertexInputBindingDescription*)(toTransform->pVertexBindingDescriptions + i));
        }
    }
    if (toTransform->pVertexAttributeDescriptions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->vertexAttributeDescriptionCount; ++i)
        {
            transform_tohost_VkVertexInputAttributeDescription(resourceTracker, (VkVertexInputAttributeDescription*)(toTransform->pVertexAttributeDescriptions + i));
        }
    }
}

void transform_fromhost_VkPipelineVertexInputStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pVertexBindingDescriptions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->vertexBindingDescriptionCount; ++i)
        {
            transform_fromhost_VkVertexInputBindingDescription(resourceTracker, (VkVertexInputBindingDescription*)(toTransform->pVertexBindingDescriptions + i));
        }
    }
    if (toTransform->pVertexAttributeDescriptions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->vertexAttributeDescriptionCount; ++i)
        {
            transform_fromhost_VkVertexInputAttributeDescription(resourceTracker, (VkVertexInputAttributeDescription*)(toTransform->pVertexAttributeDescriptions + i));
        }
    }
}

void transform_tohost_VkPipelineInputAssemblyStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineInputAssemblyStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineInputAssemblyStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineInputAssemblyStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineTessellationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineTessellationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkViewport(
    ResourceTracker* resourceTracker,
    VkViewport* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkViewport(
    ResourceTracker* resourceTracker,
    VkViewport* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkOffset2D(
    ResourceTracker* resourceTracker,
    VkOffset2D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkOffset2D(
    ResourceTracker* resourceTracker,
    VkOffset2D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkExtent2D(
    ResourceTracker* resourceTracker,
    VkExtent2D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkExtent2D(
    ResourceTracker* resourceTracker,
    VkExtent2D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkRect2D(
    ResourceTracker* resourceTracker,
    VkRect2D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->offset));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->extent));
}

void transform_fromhost_VkRect2D(
    ResourceTracker* resourceTracker,
    VkRect2D* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->offset));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->extent));
}

void transform_tohost_VkPipelineViewportStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineViewportStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pViewports)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->viewportCount; ++i)
        {
            transform_tohost_VkViewport(resourceTracker, (VkViewport*)(toTransform->pViewports + i));
        }
    }
    if (toTransform->pScissors)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->scissorCount; ++i)
        {
            transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pScissors + i));
        }
    }
}

void transform_fromhost_VkPipelineViewportStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineViewportStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pViewports)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->viewportCount; ++i)
        {
            transform_fromhost_VkViewport(resourceTracker, (VkViewport*)(toTransform->pViewports + i));
        }
    }
    if (toTransform->pScissors)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->scissorCount; ++i)
        {
            transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pScissors + i));
        }
    }
}

void transform_tohost_VkPipelineRasterizationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineRasterizationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineMultisampleStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineMultisampleStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineMultisampleStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineMultisampleStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkStencilOpState(
    ResourceTracker* resourceTracker,
    VkStencilOpState* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkStencilOpState(
    ResourceTracker* resourceTracker,
    VkStencilOpState* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineDepthStencilStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDepthStencilStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkStencilOpState(resourceTracker, (VkStencilOpState*)(&toTransform->front));
    transform_tohost_VkStencilOpState(resourceTracker, (VkStencilOpState*)(&toTransform->back));
}

void transform_fromhost_VkPipelineDepthStencilStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDepthStencilStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkStencilOpState(resourceTracker, (VkStencilOpState*)(&toTransform->front));
    transform_fromhost_VkStencilOpState(resourceTracker, (VkStencilOpState*)(&toTransform->back));
}

void transform_tohost_VkPipelineColorBlendAttachmentState(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAttachmentState* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPipelineColorBlendAttachmentState(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAttachmentState* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineColorBlendStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentCount; ++i)
        {
            transform_tohost_VkPipelineColorBlendAttachmentState(resourceTracker, (VkPipelineColorBlendAttachmentState*)(toTransform->pAttachments + i));
        }
    }
}

void transform_fromhost_VkPipelineColorBlendStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentCount; ++i)
        {
            transform_fromhost_VkPipelineColorBlendAttachmentState(resourceTracker, (VkPipelineColorBlendAttachmentState*)(toTransform->pAttachments + i));
        }
    }
}

void transform_tohost_VkPipelineDynamicStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDynamicStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineDynamicStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDynamicStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkGraphicsPipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkGraphicsPipelineCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pStages)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->stageCount; ++i)
        {
            transform_tohost_VkPipelineShaderStageCreateInfo(resourceTracker, (VkPipelineShaderStageCreateInfo*)(toTransform->pStages + i));
        }
    }
    if (toTransform->pVertexInputState)
    {
        transform_tohost_VkPipelineVertexInputStateCreateInfo(resourceTracker, (VkPipelineVertexInputStateCreateInfo*)(toTransform->pVertexInputState));
    }
    if (toTransform->pInputAssemblyState)
    {
        transform_tohost_VkPipelineInputAssemblyStateCreateInfo(resourceTracker, (VkPipelineInputAssemblyStateCreateInfo*)(toTransform->pInputAssemblyState));
    }
    if (toTransform->pTessellationState)
    {
        transform_tohost_VkPipelineTessellationStateCreateInfo(resourceTracker, (VkPipelineTessellationStateCreateInfo*)(toTransform->pTessellationState));
    }
    if (toTransform->pViewportState)
    {
        transform_tohost_VkPipelineViewportStateCreateInfo(resourceTracker, (VkPipelineViewportStateCreateInfo*)(toTransform->pViewportState));
    }
    if (toTransform->pRasterizationState)
    {
        transform_tohost_VkPipelineRasterizationStateCreateInfo(resourceTracker, (VkPipelineRasterizationStateCreateInfo*)(toTransform->pRasterizationState));
    }
    if (toTransform->pMultisampleState)
    {
        transform_tohost_VkPipelineMultisampleStateCreateInfo(resourceTracker, (VkPipelineMultisampleStateCreateInfo*)(toTransform->pMultisampleState));
    }
    if (toTransform->pDepthStencilState)
    {
        transform_tohost_VkPipelineDepthStencilStateCreateInfo(resourceTracker, (VkPipelineDepthStencilStateCreateInfo*)(toTransform->pDepthStencilState));
    }
    if (toTransform->pColorBlendState)
    {
        transform_tohost_VkPipelineColorBlendStateCreateInfo(resourceTracker, (VkPipelineColorBlendStateCreateInfo*)(toTransform->pColorBlendState));
    }
    if (toTransform->pDynamicState)
    {
        transform_tohost_VkPipelineDynamicStateCreateInfo(resourceTracker, (VkPipelineDynamicStateCreateInfo*)(toTransform->pDynamicState));
    }
}

void transform_fromhost_VkGraphicsPipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkGraphicsPipelineCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pStages)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->stageCount; ++i)
        {
            transform_fromhost_VkPipelineShaderStageCreateInfo(resourceTracker, (VkPipelineShaderStageCreateInfo*)(toTransform->pStages + i));
        }
    }
    if (toTransform->pVertexInputState)
    {
        transform_fromhost_VkPipelineVertexInputStateCreateInfo(resourceTracker, (VkPipelineVertexInputStateCreateInfo*)(toTransform->pVertexInputState));
    }
    if (toTransform->pInputAssemblyState)
    {
        transform_fromhost_VkPipelineInputAssemblyStateCreateInfo(resourceTracker, (VkPipelineInputAssemblyStateCreateInfo*)(toTransform->pInputAssemblyState));
    }
    if (toTransform->pTessellationState)
    {
        transform_fromhost_VkPipelineTessellationStateCreateInfo(resourceTracker, (VkPipelineTessellationStateCreateInfo*)(toTransform->pTessellationState));
    }
    if (toTransform->pViewportState)
    {
        transform_fromhost_VkPipelineViewportStateCreateInfo(resourceTracker, (VkPipelineViewportStateCreateInfo*)(toTransform->pViewportState));
    }
    if (toTransform->pRasterizationState)
    {
        transform_fromhost_VkPipelineRasterizationStateCreateInfo(resourceTracker, (VkPipelineRasterizationStateCreateInfo*)(toTransform->pRasterizationState));
    }
    if (toTransform->pMultisampleState)
    {
        transform_fromhost_VkPipelineMultisampleStateCreateInfo(resourceTracker, (VkPipelineMultisampleStateCreateInfo*)(toTransform->pMultisampleState));
    }
    if (toTransform->pDepthStencilState)
    {
        transform_fromhost_VkPipelineDepthStencilStateCreateInfo(resourceTracker, (VkPipelineDepthStencilStateCreateInfo*)(toTransform->pDepthStencilState));
    }
    if (toTransform->pColorBlendState)
    {
        transform_fromhost_VkPipelineColorBlendStateCreateInfo(resourceTracker, (VkPipelineColorBlendStateCreateInfo*)(toTransform->pColorBlendState));
    }
    if (toTransform->pDynamicState)
    {
        transform_fromhost_VkPipelineDynamicStateCreateInfo(resourceTracker, (VkPipelineDynamicStateCreateInfo*)(toTransform->pDynamicState));
    }
}

void transform_tohost_VkComputePipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkComputePipelineCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkPipelineShaderStageCreateInfo(resourceTracker, (VkPipelineShaderStageCreateInfo*)(&toTransform->stage));
}

void transform_fromhost_VkComputePipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkComputePipelineCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkPipelineShaderStageCreateInfo(resourceTracker, (VkPipelineShaderStageCreateInfo*)(&toTransform->stage));
}

void transform_tohost_VkPushConstantRange(
    ResourceTracker* resourceTracker,
    VkPushConstantRange* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPushConstantRange(
    ResourceTracker* resourceTracker,
    VkPushConstantRange* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineLayoutCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pPushConstantRanges)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->pushConstantRangeCount; ++i)
        {
            transform_tohost_VkPushConstantRange(resourceTracker, (VkPushConstantRange*)(toTransform->pPushConstantRanges + i));
        }
    }
}

void transform_fromhost_VkPipelineLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineLayoutCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pPushConstantRanges)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->pushConstantRangeCount; ++i)
        {
            transform_fromhost_VkPushConstantRange(resourceTracker, (VkPushConstantRange*)(toTransform->pPushConstantRanges + i));
        }
    }
}

void transform_tohost_VkSamplerCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSamplerCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDescriptorSetLayoutBinding(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBinding* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDescriptorSetLayoutBinding(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBinding* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDescriptorSetLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pBindings)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindingCount; ++i)
        {
            transform_tohost_VkDescriptorSetLayoutBinding(resourceTracker, (VkDescriptorSetLayoutBinding*)(toTransform->pBindings + i));
        }
    }
}

void transform_fromhost_VkDescriptorSetLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pBindings)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->bindingCount; ++i)
        {
            transform_fromhost_VkDescriptorSetLayoutBinding(resourceTracker, (VkDescriptorSetLayoutBinding*)(toTransform->pBindings + i));
        }
    }
}

void transform_tohost_VkDescriptorPoolSize(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolSize* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDescriptorPoolSize(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolSize* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDescriptorPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pPoolSizes)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->poolSizeCount; ++i)
        {
            transform_tohost_VkDescriptorPoolSize(resourceTracker, (VkDescriptorPoolSize*)(toTransform->pPoolSizes + i));
        }
    }
}

void transform_fromhost_VkDescriptorPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pPoolSizes)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->poolSizeCount; ++i)
        {
            transform_fromhost_VkDescriptorPoolSize(resourceTracker, (VkDescriptorPoolSize*)(toTransform->pPoolSizes + i));
        }
    }
}

void transform_tohost_VkDescriptorSetAllocateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDescriptorSetAllocateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDescriptorImageInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorImageInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDescriptorImageInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorImageInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDescriptorBufferInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorBufferInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDescriptorBufferInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorBufferInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkWriteDescriptorSet(
    ResourceTracker* resourceTracker,
    VkWriteDescriptorSet* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pImageInfo)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->descriptorCount; ++i)
        {
            transform_tohost_VkDescriptorImageInfo(resourceTracker, (VkDescriptorImageInfo*)(toTransform->pImageInfo + i));
        }
    }
    if (toTransform->pBufferInfo)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->descriptorCount; ++i)
        {
            transform_tohost_VkDescriptorBufferInfo(resourceTracker, (VkDescriptorBufferInfo*)(toTransform->pBufferInfo + i));
        }
    }
}

void transform_fromhost_VkWriteDescriptorSet(
    ResourceTracker* resourceTracker,
    VkWriteDescriptorSet* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pImageInfo)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->descriptorCount; ++i)
        {
            transform_fromhost_VkDescriptorImageInfo(resourceTracker, (VkDescriptorImageInfo*)(toTransform->pImageInfo + i));
        }
    }
    if (toTransform->pBufferInfo)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->descriptorCount; ++i)
        {
            transform_fromhost_VkDescriptorBufferInfo(resourceTracker, (VkDescriptorBufferInfo*)(toTransform->pBufferInfo + i));
        }
    }
}

void transform_tohost_VkCopyDescriptorSet(
    ResourceTracker* resourceTracker,
    VkCopyDescriptorSet* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCopyDescriptorSet(
    ResourceTracker* resourceTracker,
    VkCopyDescriptorSet* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkFramebufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkFramebufferCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkFramebufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkFramebufferCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkAttachmentDescription(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkAttachmentDescription(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkAttachmentReference(
    ResourceTracker* resourceTracker,
    VkAttachmentReference* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkAttachmentReference(
    ResourceTracker* resourceTracker,
    VkAttachmentReference* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkSubpassDescription(
    ResourceTracker* resourceTracker,
    VkSubpassDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pInputAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->inputAttachmentCount; ++i)
        {
            transform_tohost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pInputAttachments + i));
        }
    }
    if (toTransform->pColorAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_tohost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pColorAttachments + i));
        }
    }
    if (toTransform->pResolveAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_tohost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pResolveAttachments + i));
        }
    }
    if (toTransform->pDepthStencilAttachment)
    {
        transform_tohost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pDepthStencilAttachment));
    }
}

void transform_fromhost_VkSubpassDescription(
    ResourceTracker* resourceTracker,
    VkSubpassDescription* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pInputAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->inputAttachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pInputAttachments + i));
        }
    }
    if (toTransform->pColorAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pColorAttachments + i));
        }
    }
    if (toTransform->pResolveAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pResolveAttachments + i));
        }
    }
    if (toTransform->pDepthStencilAttachment)
    {
        transform_fromhost_VkAttachmentReference(resourceTracker, (VkAttachmentReference*)(toTransform->pDepthStencilAttachment));
    }
}

void transform_tohost_VkSubpassDependency(
    ResourceTracker* resourceTracker,
    VkSubpassDependency* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkSubpassDependency(
    ResourceTracker* resourceTracker,
    VkSubpassDependency* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkRenderPassCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentCount; ++i)
        {
            transform_tohost_VkAttachmentDescription(resourceTracker, (VkAttachmentDescription*)(toTransform->pAttachments + i));
        }
    }
    if (toTransform->pSubpasses)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->subpassCount; ++i)
        {
            transform_tohost_VkSubpassDescription(resourceTracker, (VkSubpassDescription*)(toTransform->pSubpasses + i));
        }
    }
    if (toTransform->pDependencies)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->dependencyCount; ++i)
        {
            transform_tohost_VkSubpassDependency(resourceTracker, (VkSubpassDependency*)(toTransform->pDependencies + i));
        }
    }
}

void transform_fromhost_VkRenderPassCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentDescription(resourceTracker, (VkAttachmentDescription*)(toTransform->pAttachments + i));
        }
    }
    if (toTransform->pSubpasses)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->subpassCount; ++i)
        {
            transform_fromhost_VkSubpassDescription(resourceTracker, (VkSubpassDescription*)(toTransform->pSubpasses + i));
        }
    }
    if (toTransform->pDependencies)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->dependencyCount; ++i)
        {
            transform_fromhost_VkSubpassDependency(resourceTracker, (VkSubpassDependency*)(toTransform->pDependencies + i));
        }
    }
}

void transform_tohost_VkCommandPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkCommandPoolCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCommandPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkCommandPoolCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkCommandBufferAllocateInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCommandBufferAllocateInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkCommandBufferInheritanceInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCommandBufferInheritanceInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pInheritanceInfo)
    {
        transform_tohost_VkCommandBufferInheritanceInfo(resourceTracker, (VkCommandBufferInheritanceInfo*)(toTransform->pInheritanceInfo));
    }
}

void transform_fromhost_VkCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pInheritanceInfo)
    {
        transform_fromhost_VkCommandBufferInheritanceInfo(resourceTracker, (VkCommandBufferInheritanceInfo*)(toTransform->pInheritanceInfo));
    }
}

void transform_tohost_VkBufferCopy(
    ResourceTracker* resourceTracker,
    VkBufferCopy* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkBufferCopy(
    ResourceTracker* resourceTracker,
    VkBufferCopy* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkImageSubresourceLayers(
    ResourceTracker* resourceTracker,
    VkImageSubresourceLayers* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkImageSubresourceLayers(
    ResourceTracker* resourceTracker,
    VkImageSubresourceLayers* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkImageCopy(
    ResourceTracker* resourceTracker,
    VkImageCopy* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->srcSubresource));
    transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->srcOffset));
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->dstSubresource));
    transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->dstOffset));
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_fromhost_VkImageCopy(
    ResourceTracker* resourceTracker,
    VkImageCopy* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->srcSubresource));
    transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->srcOffset));
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->dstSubresource));
    transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->dstOffset));
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_tohost_VkImageBlit(
    ResourceTracker* resourceTracker,
    VkImageBlit* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->srcSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(toTransform->srcOffsets + i));
    }
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->dstSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(toTransform->dstOffsets + i));
    }
}

void transform_fromhost_VkImageBlit(
    ResourceTracker* resourceTracker,
    VkImageBlit* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->srcSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(toTransform->srcOffsets + i));
    }
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->dstSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(toTransform->dstOffsets + i));
    }
}

void transform_tohost_VkBufferImageCopy(
    ResourceTracker* resourceTracker,
    VkBufferImageCopy* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->imageSubresource));
    transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->imageOffset));
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->imageExtent));
}

void transform_fromhost_VkBufferImageCopy(
    ResourceTracker* resourceTracker,
    VkBufferImageCopy* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->imageSubresource));
    transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->imageOffset));
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->imageExtent));
}

void transform_tohost_VkClearColorValue(
    ResourceTracker* resourceTracker,
    VkClearColorValue* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkClearColorValue(
    ResourceTracker* resourceTracker,
    VkClearColorValue* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkClearDepthStencilValue(
    ResourceTracker* resourceTracker,
    VkClearDepthStencilValue* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkClearDepthStencilValue(
    ResourceTracker* resourceTracker,
    VkClearDepthStencilValue* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkClearValue(
    ResourceTracker* resourceTracker,
    VkClearValue* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkClearColorValue(resourceTracker, (VkClearColorValue*)(&toTransform->color));
    transform_tohost_VkClearDepthStencilValue(resourceTracker, (VkClearDepthStencilValue*)(&toTransform->depthStencil));
}

void transform_fromhost_VkClearValue(
    ResourceTracker* resourceTracker,
    VkClearValue* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkClearColorValue(resourceTracker, (VkClearColorValue*)(&toTransform->color));
    transform_fromhost_VkClearDepthStencilValue(resourceTracker, (VkClearDepthStencilValue*)(&toTransform->depthStencil));
}

void transform_tohost_VkClearAttachment(
    ResourceTracker* resourceTracker,
    VkClearAttachment* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkClearValue(resourceTracker, (VkClearValue*)(&toTransform->clearValue));
}

void transform_fromhost_VkClearAttachment(
    ResourceTracker* resourceTracker,
    VkClearAttachment* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkClearValue(resourceTracker, (VkClearValue*)(&toTransform->clearValue));
}

void transform_tohost_VkClearRect(
    ResourceTracker* resourceTracker,
    VkClearRect* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->rect));
}

void transform_fromhost_VkClearRect(
    ResourceTracker* resourceTracker,
    VkClearRect* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->rect));
}

void transform_tohost_VkImageResolve(
    ResourceTracker* resourceTracker,
    VkImageResolve* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->srcSubresource));
    transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->srcOffset));
    transform_tohost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->dstSubresource));
    transform_tohost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->dstOffset));
    transform_tohost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_fromhost_VkImageResolve(
    ResourceTracker* resourceTracker,
    VkImageResolve* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->srcSubresource));
    transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->srcOffset));
    transform_fromhost_VkImageSubresourceLayers(resourceTracker, (VkImageSubresourceLayers*)(&toTransform->dstSubresource));
    transform_fromhost_VkOffset3D(resourceTracker, (VkOffset3D*)(&toTransform->dstOffset));
    transform_fromhost_VkExtent3D(resourceTracker, (VkExtent3D*)(&toTransform->extent));
}

void transform_tohost_VkMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkMemoryBarrier* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkMemoryBarrier* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBufferMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkBufferMemoryBarrier* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBufferMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkBufferMemoryBarrier* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImageMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkImageMemoryBarrier* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkImageSubresourceRange(resourceTracker, (VkImageSubresourceRange*)(&toTransform->subresourceRange));
}

void transform_fromhost_VkImageMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkImageMemoryBarrier* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkImageSubresourceRange(resourceTracker, (VkImageSubresourceRange*)(&toTransform->subresourceRange));
}

void transform_tohost_VkRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->renderArea));
    if (toTransform->pClearValues)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->clearValueCount; ++i)
        {
            transform_tohost_VkClearValue(resourceTracker, (VkClearValue*)(toTransform->pClearValues + i));
        }
    }
}

void transform_fromhost_VkRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->renderArea));
    if (toTransform->pClearValues)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->clearValueCount; ++i)
        {
            transform_fromhost_VkClearValue(resourceTracker, (VkClearValue*)(toTransform->pClearValues + i));
        }
    }
}

void transform_tohost_VkDispatchIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDispatchIndirectCommand* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDispatchIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDispatchIndirectCommand* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDrawIndexedIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndexedIndirectCommand* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDrawIndexedIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndexedIndirectCommand* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDrawIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndirectCommand* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDrawIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndirectCommand* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkBaseOutStructure(
    ResourceTracker* resourceTracker,
    VkBaseOutStructure* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBaseOutStructure(
    ResourceTracker* resourceTracker,
    VkBaseOutStructure* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBaseInStructure(
    ResourceTracker* resourceTracker,
    VkBaseInStructure* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBaseInStructure(
    ResourceTracker* resourceTracker,
    VkBaseInStructure* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_VERSION_1_1
void transform_tohost_VkPhysicalDeviceSubgroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSubgroupProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceSubgroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSubgroupProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBindBufferMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBindBufferMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBindImageMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBindImageMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)&toTransform->memoryOffset, 1, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDevice16BitStorageFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice16BitStorageFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDevice16BitStorageFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice16BitStorageFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryDedicatedRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedRequirements* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryDedicatedRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedRequirements* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryDedicatedAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryDedicatedAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryAllocateFlagsInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateFlagsInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryAllocateFlagsInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateFlagsInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupRenderPassBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pDeviceRenderAreas)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->deviceRenderAreaCount; ++i)
        {
            transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pDeviceRenderAreas + i));
        }
    }
}

void transform_fromhost_VkDeviceGroupRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupRenderPassBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pDeviceRenderAreas)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->deviceRenderAreaCount; ++i)
        {
            transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pDeviceRenderAreas + i));
        }
    }
}

void transform_tohost_VkDeviceGroupCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupCommandBufferBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupCommandBufferBeginInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupSubmitInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSubmitInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupSubmitInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSubmitInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupBindSparseInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupBindSparseInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBindBufferMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryDeviceGroupInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBindBufferMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryDeviceGroupInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBindImageMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryDeviceGroupInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pSplitInstanceBindRegions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->splitInstanceBindRegionCount; ++i)
        {
            transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pSplitInstanceBindRegions + i));
        }
    }
}

void transform_fromhost_VkBindImageMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryDeviceGroupInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pSplitInstanceBindRegions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->splitInstanceBindRegionCount; ++i)
        {
            transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pSplitInstanceBindRegions + i));
        }
    }
}

void transform_tohost_VkPhysicalDeviceGroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceGroupProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceGroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceGroupProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupDeviceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupDeviceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBufferMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkBufferMemoryRequirementsInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBufferMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkBufferMemoryRequirementsInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImageMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageMemoryRequirementsInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImageMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageMemoryRequirementsInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImageSparseMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageSparseMemoryRequirementsInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImageSparseMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageSparseMemoryRequirementsInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkMemoryRequirements(resourceTracker, (VkMemoryRequirements*)(&toTransform->memoryRequirements));
}

void transform_fromhost_VkMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkMemoryRequirements(resourceTracker, (VkMemoryRequirements*)(&toTransform->memoryRequirements));
}

void transform_tohost_VkSparseImageMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkSparseImageMemoryRequirements(resourceTracker, (VkSparseImageMemoryRequirements*)(&toTransform->memoryRequirements));
}

void transform_fromhost_VkSparseImageMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkSparseImageMemoryRequirements(resourceTracker, (VkSparseImageMemoryRequirements*)(&toTransform->memoryRequirements));
}

void transform_tohost_VkPhysicalDeviceFeatures2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkPhysicalDeviceFeatures(resourceTracker, (VkPhysicalDeviceFeatures*)(&toTransform->features));
}

void transform_fromhost_VkPhysicalDeviceFeatures2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkPhysicalDeviceFeatures(resourceTracker, (VkPhysicalDeviceFeatures*)(&toTransform->features));
}

void transform_tohost_VkPhysicalDeviceProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkPhysicalDeviceProperties(resourceTracker, (VkPhysicalDeviceProperties*)(&toTransform->properties));
}

void transform_fromhost_VkPhysicalDeviceProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkPhysicalDeviceProperties(resourceTracker, (VkPhysicalDeviceProperties*)(&toTransform->properties));
}

void transform_tohost_VkFormatProperties2(
    ResourceTracker* resourceTracker,
    VkFormatProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkFormatProperties(resourceTracker, (VkFormatProperties*)(&toTransform->formatProperties));
}

void transform_fromhost_VkFormatProperties2(
    ResourceTracker* resourceTracker,
    VkFormatProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkFormatProperties(resourceTracker, (VkFormatProperties*)(&toTransform->formatProperties));
}

void transform_tohost_VkImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkImageFormatProperties(resourceTracker, (VkImageFormatProperties*)(&toTransform->imageFormatProperties));
}

void transform_fromhost_VkImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkImageFormatProperties(resourceTracker, (VkImageFormatProperties*)(&toTransform->imageFormatProperties));
}

void transform_tohost_VkPhysicalDeviceImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceImageFormatInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceImageFormatInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkQueueFamilyProperties2(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkQueueFamilyProperties(resourceTracker, (VkQueueFamilyProperties*)(&toTransform->queueFamilyProperties));
}

void transform_fromhost_VkQueueFamilyProperties2(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkQueueFamilyProperties(resourceTracker, (VkQueueFamilyProperties*)(&toTransform->queueFamilyProperties));
}

void transform_tohost_VkPhysicalDeviceMemoryProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkPhysicalDeviceMemoryProperties(resourceTracker, (VkPhysicalDeviceMemoryProperties*)(&toTransform->memoryProperties));
}

void transform_fromhost_VkPhysicalDeviceMemoryProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkPhysicalDeviceMemoryProperties(resourceTracker, (VkPhysicalDeviceMemoryProperties*)(&toTransform->memoryProperties));
}

void transform_tohost_VkSparseImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkSparseImageFormatProperties(resourceTracker, (VkSparseImageFormatProperties*)(&toTransform->properties));
}

void transform_fromhost_VkSparseImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkSparseImageFormatProperties(resourceTracker, (VkSparseImageFormatProperties*)(&toTransform->properties));
}

void transform_tohost_VkPhysicalDeviceSparseImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseImageFormatInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceSparseImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseImageFormatInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDevicePointClippingProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePointClippingProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDevicePointClippingProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePointClippingProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkInputAttachmentAspectReference(
    ResourceTracker* resourceTracker,
    VkInputAttachmentAspectReference* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkInputAttachmentAspectReference(
    ResourceTracker* resourceTracker,
    VkInputAttachmentAspectReference* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkRenderPassInputAttachmentAspectCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassInputAttachmentAspectCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAspectReferences)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->aspectReferenceCount; ++i)
        {
            transform_tohost_VkInputAttachmentAspectReference(resourceTracker, (VkInputAttachmentAspectReference*)(toTransform->pAspectReferences + i));
        }
    }
}

void transform_fromhost_VkRenderPassInputAttachmentAspectCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassInputAttachmentAspectCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAspectReferences)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->aspectReferenceCount; ++i)
        {
            transform_fromhost_VkInputAttachmentAspectReference(resourceTracker, (VkInputAttachmentAspectReference*)(toTransform->pAspectReferences + i));
        }
    }
}

void transform_tohost_VkImageViewUsageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewUsageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImageViewUsageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewUsageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineTessellationDomainOriginStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationDomainOriginStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineTessellationDomainOriginStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationDomainOriginStateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkRenderPassMultiviewCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassMultiviewCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkRenderPassMultiviewCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassMultiviewCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceMultiviewFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceMultiviewFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceMultiviewProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceMultiviewProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceVariablePointerFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVariablePointerFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceVariablePointerFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVariablePointerFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceProtectedMemoryFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceProtectedMemoryFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceProtectedMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceProtectedMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceQueueInfo2(
    ResourceTracker* resourceTracker,
    VkDeviceQueueInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceQueueInfo2(
    ResourceTracker* resourceTracker,
    VkDeviceQueueInfo2* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkProtectedSubmitInfo(
    ResourceTracker* resourceTracker,
    VkProtectedSubmitInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkProtectedSubmitInfo(
    ResourceTracker* resourceTracker,
    VkProtectedSubmitInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSamplerYcbcrConversionCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkComponentMapping(resourceTracker, (VkComponentMapping*)(&toTransform->components));
}

void transform_fromhost_VkSamplerYcbcrConversionCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkComponentMapping(resourceTracker, (VkComponentMapping*)(&toTransform->components));
}

void transform_tohost_VkSamplerYcbcrConversionInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSamplerYcbcrConversionInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBindImagePlaneMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImagePlaneMemoryInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBindImagePlaneMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImagePlaneMemoryInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImagePlaneMemoryRequirementsInfo(
    ResourceTracker* resourceTracker,
    VkImagePlaneMemoryRequirementsInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImagePlaneMemoryRequirementsInfo(
    ResourceTracker* resourceTracker,
    VkImagePlaneMemoryRequirementsInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSamplerYcbcrConversionImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSamplerYcbcrConversionImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDescriptorUpdateTemplateEntry(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateEntry* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDescriptorUpdateTemplateEntry(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateEntry* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDescriptorUpdateTemplateCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pDescriptorUpdateEntries)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->descriptorUpdateEntryCount; ++i)
        {
            transform_tohost_VkDescriptorUpdateTemplateEntry(resourceTracker, (VkDescriptorUpdateTemplateEntry*)(toTransform->pDescriptorUpdateEntries + i));
        }
    }
}

void transform_fromhost_VkDescriptorUpdateTemplateCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pDescriptorUpdateEntries)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->descriptorUpdateEntryCount; ++i)
        {
            transform_fromhost_VkDescriptorUpdateTemplateEntry(resourceTracker, (VkDescriptorUpdateTemplateEntry*)(toTransform->pDescriptorUpdateEntries + i));
        }
    }
}

void transform_tohost_VkExternalMemoryProperties(
    ResourceTracker* resourceTracker,
    VkExternalMemoryProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkExternalMemoryProperties(
    ResourceTracker* resourceTracker,
    VkExternalMemoryProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPhysicalDeviceExternalImageFormatInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalImageFormatInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceExternalImageFormatInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalImageFormatInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    resourceTracker->transformImpl_VkExternalMemoryProperties_tohost(&toTransform->externalMemoryProperties, 1);
    transform_tohost_VkExternalMemoryProperties(resourceTracker, (VkExternalMemoryProperties*)(&toTransform->externalMemoryProperties));
}

void transform_fromhost_VkExternalImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    resourceTracker->transformImpl_VkExternalMemoryProperties_fromhost(&toTransform->externalMemoryProperties, 1);
    transform_fromhost_VkExternalMemoryProperties(resourceTracker, (VkExternalMemoryProperties*)(&toTransform->externalMemoryProperties));
}

void transform_tohost_VkPhysicalDeviceExternalBufferInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalBufferInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceExternalBufferInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalBufferInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalBufferProperties(
    ResourceTracker* resourceTracker,
    VkExternalBufferProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    resourceTracker->transformImpl_VkExternalMemoryProperties_tohost(&toTransform->externalMemoryProperties, 1);
    transform_tohost_VkExternalMemoryProperties(resourceTracker, (VkExternalMemoryProperties*)(&toTransform->externalMemoryProperties));
}

void transform_fromhost_VkExternalBufferProperties(
    ResourceTracker* resourceTracker,
    VkExternalBufferProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    resourceTracker->transformImpl_VkExternalMemoryProperties_fromhost(&toTransform->externalMemoryProperties, 1);
    transform_fromhost_VkExternalMemoryProperties(resourceTracker, (VkExternalMemoryProperties*)(&toTransform->externalMemoryProperties));
}

void transform_tohost_VkPhysicalDeviceIDProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceIDProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceIDProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceIDProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalMemoryImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExternalMemoryImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalMemoryBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryBufferCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExternalMemoryBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryBufferCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceExternalFenceInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalFenceInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceExternalFenceInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalFenceInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalFenceProperties(
    ResourceTracker* resourceTracker,
    VkExternalFenceProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExternalFenceProperties(
    ResourceTracker* resourceTracker,
    VkExternalFenceProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportFenceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportFenceCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreCreateInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceExternalSemaphoreInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalSemaphoreInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceExternalSemaphoreInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalSemaphoreInfo* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalSemaphoreProperties(
    ResourceTracker* resourceTracker,
    VkExternalSemaphoreProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExternalSemaphoreProperties(
    ResourceTracker* resourceTracker,
    VkExternalSemaphoreProperties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceMaintenance3Properties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMaintenance3Properties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceMaintenance3Properties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMaintenance3Properties* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDescriptorSetLayoutSupport(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutSupport* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDescriptorSetLayoutSupport(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutSupport* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceShaderDrawParameterFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderDrawParameterFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceShaderDrawParameterFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderDrawParameterFeatures* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_surface
void transform_tohost_VkSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->currentExtent));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minImageExtent));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxImageExtent));
}

void transform_fromhost_VkSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->currentExtent));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minImageExtent));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxImageExtent));
}

void transform_tohost_VkSurfaceFormatKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormatKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkSurfaceFormatKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormatKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

#endif
#ifdef VK_KHR_swapchain
void transform_tohost_VkSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkSwapchainCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->imageExtent));
}

void transform_fromhost_VkSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkSwapchainCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->imageExtent));
}

void transform_tohost_VkPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkPresentInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkPresentInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImageSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageSwapchainCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImageSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageSwapchainCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkBindImageMemorySwapchainInfoKHR(
    ResourceTracker* resourceTracker,
    VkBindImageMemorySwapchainInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkBindImageMemorySwapchainInfoKHR(
    ResourceTracker* resourceTracker,
    VkBindImageMemorySwapchainInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkAcquireNextImageInfoKHR(
    ResourceTracker* resourceTracker,
    VkAcquireNextImageInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkAcquireNextImageInfoKHR(
    ResourceTracker* resourceTracker,
    VkAcquireNextImageInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupPresentCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupPresentCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGroupSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSwapchainCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGroupSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSwapchainCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_display
void transform_tohost_VkDisplayPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->physicalDimensions));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->physicalResolution));
}

void transform_fromhost_VkDisplayPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->physicalDimensions));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->physicalResolution));
}

void transform_tohost_VkDisplayModeParametersKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeParametersKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->visibleRegion));
}

void transform_fromhost_VkDisplayModeParametersKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeParametersKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->visibleRegion));
}

void transform_tohost_VkDisplayModePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModePropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkDisplayModeParametersKHR(resourceTracker, (VkDisplayModeParametersKHR*)(&toTransform->parameters));
}

void transform_fromhost_VkDisplayModePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModePropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkDisplayModeParametersKHR(resourceTracker, (VkDisplayModeParametersKHR*)(&toTransform->parameters));
}

void transform_tohost_VkDisplayModeCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkDisplayModeParametersKHR(resourceTracker, (VkDisplayModeParametersKHR*)(&toTransform->parameters));
}

void transform_fromhost_VkDisplayModeCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkDisplayModeParametersKHR(resourceTracker, (VkDisplayModeParametersKHR*)(&toTransform->parameters));
}

void transform_tohost_VkDisplayPlaneCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->minSrcPosition));
    transform_tohost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->maxSrcPosition));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minSrcExtent));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxSrcExtent));
    transform_tohost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->minDstPosition));
    transform_tohost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->maxDstPosition));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minDstExtent));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxDstExtent));
}

void transform_fromhost_VkDisplayPlaneCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->minSrcPosition));
    transform_fromhost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->maxSrcPosition));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minSrcExtent));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxSrcExtent));
    transform_fromhost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->minDstPosition));
    transform_fromhost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->maxDstPosition));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minDstExtent));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxDstExtent));
}

void transform_tohost_VkDisplayPlanePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlanePropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkDisplayPlanePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlanePropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkDisplaySurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplaySurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->imageExtent));
}

void transform_fromhost_VkDisplaySurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplaySurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->imageExtent));
}

#endif
#ifdef VK_KHR_display_swapchain
void transform_tohost_VkDisplayPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPresentInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->srcRect));
    transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->dstRect));
}

void transform_fromhost_VkDisplayPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPresentInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->srcRect));
    transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(&toTransform->dstRect));
}

#endif
#ifdef VK_KHR_xlib_surface
void transform_tohost_VkXlibSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXlibSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkXlibSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXlibSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_xcb_surface
void transform_tohost_VkXcbSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXcbSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkXcbSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXcbSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_wayland_surface
void transform_tohost_VkWaylandSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWaylandSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkWaylandSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWaylandSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_mir_surface
void transform_tohost_VkMirSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkMirSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMirSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkMirSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_android_surface
void transform_tohost_VkAndroidSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkAndroidSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkAndroidSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkAndroidSurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_win32_surface
void transform_tohost_VkWin32SurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32SurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkWin32SurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32SurfaceCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#endif
#ifdef VK_KHR_device_group
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#endif
#ifdef VK_KHR_device_group_creation
#endif
#ifdef VK_KHR_external_memory_capabilities
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void transform_tohost_VkImportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryWin32HandlePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryWin32HandlePropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryWin32HandlePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryWin32HandlePropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_external_memory_fd
void transform_tohost_VkImportMemoryFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportMemoryFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryFdPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryFdPropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryFdPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryFdPropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_win32_keyed_mutex
void transform_tohost_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)toTransform->pReleaseSyncs, toTransform->releaseCount, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)toTransform->pReleaseSyncs, toTransform->releaseCount, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void transform_tohost_VkImportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkD3D12FenceSubmitInfoKHR(
    ResourceTracker* resourceTracker,
    VkD3D12FenceSubmitInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkD3D12FenceSubmitInfoKHR(
    ResourceTracker* resourceTracker,
    VkD3D12FenceSubmitInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSemaphoreGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSemaphoreGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_external_semaphore_fd
void transform_tohost_VkImportSemaphoreFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportSemaphoreFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSemaphoreGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSemaphoreGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_push_descriptor
void transform_tohost_VkPhysicalDevicePushDescriptorPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePushDescriptorPropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDevicePushDescriptorPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePushDescriptorPropertiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void transform_tohost_VkRectLayerKHR(
    ResourceTracker* resourceTracker,
    VkRectLayerKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->offset));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->extent));
}

void transform_fromhost_VkRectLayerKHR(
    ResourceTracker* resourceTracker,
    VkRectLayerKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkOffset2D(resourceTracker, (VkOffset2D*)(&toTransform->offset));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->extent));
}

void transform_tohost_VkPresentRegionKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pRectangles)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->rectangleCount; ++i)
        {
            transform_tohost_VkRectLayerKHR(resourceTracker, (VkRectLayerKHR*)(toTransform->pRectangles + i));
        }
    }
}

void transform_fromhost_VkPresentRegionKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pRectangles)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->rectangleCount; ++i)
        {
            transform_fromhost_VkRectLayerKHR(resourceTracker, (VkRectLayerKHR*)(toTransform->pRectangles + i));
        }
    }
}

void transform_tohost_VkPresentRegionsKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionsKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pRegions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->swapchainCount; ++i)
        {
            transform_tohost_VkPresentRegionKHR(resourceTracker, (VkPresentRegionKHR*)(toTransform->pRegions + i));
        }
    }
}

void transform_fromhost_VkPresentRegionsKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionsKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pRegions)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->swapchainCount; ++i)
        {
            transform_fromhost_VkPresentRegionKHR(resourceTracker, (VkPresentRegionKHR*)(toTransform->pRegions + i));
        }
    }
}

#endif
#ifdef VK_KHR_descriptor_update_template
#endif
#ifdef VK_KHR_create_renderpass2
void transform_tohost_VkAttachmentDescription2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkAttachmentDescription2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkAttachmentReference2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentReference2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkAttachmentReference2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentReference2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSubpassDescription2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDescription2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pInputAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->inputAttachmentCount; ++i)
        {
            transform_tohost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pInputAttachments + i));
        }
    }
    if (toTransform->pColorAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_tohost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pColorAttachments + i));
        }
    }
    if (toTransform->pResolveAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_tohost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pResolveAttachments + i));
        }
    }
    if (toTransform->pDepthStencilAttachment)
    {
        transform_tohost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pDepthStencilAttachment));
    }
}

void transform_fromhost_VkSubpassDescription2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDescription2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pInputAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->inputAttachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pInputAttachments + i));
        }
    }
    if (toTransform->pColorAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pColorAttachments + i));
        }
    }
    if (toTransform->pResolveAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->colorAttachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pResolveAttachments + i));
        }
    }
    if (toTransform->pDepthStencilAttachment)
    {
        transform_fromhost_VkAttachmentReference2KHR(resourceTracker, (VkAttachmentReference2KHR*)(toTransform->pDepthStencilAttachment));
    }
}

void transform_tohost_VkSubpassDependency2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDependency2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSubpassDependency2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDependency2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkRenderPassCreateInfo2KHR(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentCount; ++i)
        {
            transform_tohost_VkAttachmentDescription2KHR(resourceTracker, (VkAttachmentDescription2KHR*)(toTransform->pAttachments + i));
        }
    }
    if (toTransform->pSubpasses)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->subpassCount; ++i)
        {
            transform_tohost_VkSubpassDescription2KHR(resourceTracker, (VkSubpassDescription2KHR*)(toTransform->pSubpasses + i));
        }
    }
    if (toTransform->pDependencies)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->dependencyCount; ++i)
        {
            transform_tohost_VkSubpassDependency2KHR(resourceTracker, (VkSubpassDependency2KHR*)(toTransform->pDependencies + i));
        }
    }
}

void transform_fromhost_VkRenderPassCreateInfo2KHR(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentCount; ++i)
        {
            transform_fromhost_VkAttachmentDescription2KHR(resourceTracker, (VkAttachmentDescription2KHR*)(toTransform->pAttachments + i));
        }
    }
    if (toTransform->pSubpasses)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->subpassCount; ++i)
        {
            transform_fromhost_VkSubpassDescription2KHR(resourceTracker, (VkSubpassDescription2KHR*)(toTransform->pSubpasses + i));
        }
    }
    if (toTransform->pDependencies)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->dependencyCount; ++i)
        {
            transform_fromhost_VkSubpassDependency2KHR(resourceTracker, (VkSubpassDependency2KHR*)(toTransform->pDependencies + i));
        }
    }
}

void transform_tohost_VkSubpassBeginInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassBeginInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSubpassBeginInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassBeginInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSubpassEndInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassEndInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSubpassEndInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassEndInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_shared_presentable_image
void transform_tohost_VkSharedPresentSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSharedPresentSurfaceCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSharedPresentSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSharedPresentSurfaceCapabilitiesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_external_fence_capabilities
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void transform_tohost_VkImportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportFenceWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportFenceWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkFenceGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkFenceGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetWin32HandleInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_external_fence_fd
void transform_tohost_VkImportFenceFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportFenceFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkFenceGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkFenceGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetFdInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void transform_tohost_VkPhysicalDeviceSurfaceInfo2KHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSurfaceInfo2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceSurfaceInfo2KHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSurfaceInfo2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSurfaceCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkSurfaceCapabilitiesKHR(resourceTracker, (VkSurfaceCapabilitiesKHR*)(&toTransform->surfaceCapabilities));
}

void transform_fromhost_VkSurfaceCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkSurfaceCapabilitiesKHR(resourceTracker, (VkSurfaceCapabilitiesKHR*)(&toTransform->surfaceCapabilities));
}

void transform_tohost_VkSurfaceFormat2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormat2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkSurfaceFormatKHR(resourceTracker, (VkSurfaceFormatKHR*)(&toTransform->surfaceFormat));
}

void transform_fromhost_VkSurfaceFormat2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormat2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkSurfaceFormatKHR(resourceTracker, (VkSurfaceFormatKHR*)(&toTransform->surfaceFormat));
}

#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void transform_tohost_VkDisplayProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayProperties2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkDisplayPropertiesKHR(resourceTracker, (VkDisplayPropertiesKHR*)(&toTransform->displayProperties));
}

void transform_fromhost_VkDisplayProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayProperties2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkDisplayPropertiesKHR(resourceTracker, (VkDisplayPropertiesKHR*)(&toTransform->displayProperties));
}

void transform_tohost_VkDisplayPlaneProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneProperties2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkDisplayPlanePropertiesKHR(resourceTracker, (VkDisplayPlanePropertiesKHR*)(&toTransform->displayPlaneProperties));
}

void transform_fromhost_VkDisplayPlaneProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneProperties2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkDisplayPlanePropertiesKHR(resourceTracker, (VkDisplayPlanePropertiesKHR*)(&toTransform->displayPlaneProperties));
}

void transform_tohost_VkDisplayModeProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeProperties2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkDisplayModePropertiesKHR(resourceTracker, (VkDisplayModePropertiesKHR*)(&toTransform->displayModeProperties));
}

void transform_fromhost_VkDisplayModeProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeProperties2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkDisplayModePropertiesKHR(resourceTracker, (VkDisplayModePropertiesKHR*)(&toTransform->displayModeProperties));
}

void transform_tohost_VkDisplayPlaneInfo2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneInfo2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDisplayPlaneInfo2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneInfo2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDisplayPlaneCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilities2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkDisplayPlaneCapabilitiesKHR(resourceTracker, (VkDisplayPlaneCapabilitiesKHR*)(&toTransform->capabilities));
}

void transform_fromhost_VkDisplayPlaneCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilities2KHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkDisplayPlaneCapabilitiesKHR(resourceTracker, (VkDisplayPlaneCapabilitiesKHR*)(&toTransform->capabilities));
}

#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#endif
#ifdef VK_KHR_image_format_list
void transform_tohost_VkImageFormatListCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageFormatListCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImageFormatListCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageFormatListCreateInfoKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#endif
#ifdef VK_KHR_bind_memory2
#endif
#ifdef VK_KHR_maintenance3
#endif
#ifdef VK_KHR_draw_indirect_count
#endif
#ifdef VK_KHR_8bit_storage
void transform_tohost_VkPhysicalDevice8BitStorageFeaturesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice8BitStorageFeaturesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDevice8BitStorageFeaturesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice8BitStorageFeaturesKHR* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_ANDROID_native_buffer
void transform_tohost_VkNativeBufferANDROID(
    ResourceTracker* resourceTracker,
    VkNativeBufferANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkNativeBufferANDROID(
    ResourceTracker* resourceTracker,
    VkNativeBufferANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_debug_report
void transform_tohost_VkDebugReportCallbackCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugReportCallbackCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugReportCallbackCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugReportCallbackCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void transform_tohost_VkPipelineRasterizationStateRasterizationOrderAMD(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateRasterizationOrderAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineRasterizationStateRasterizationOrderAMD(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateRasterizationOrderAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void transform_tohost_VkDebugMarkerObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectNameInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugMarkerObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectNameInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDebugMarkerObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectTagInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugMarkerObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectTagInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDebugMarkerMarkerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerMarkerInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugMarkerMarkerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerMarkerInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void transform_tohost_VkDedicatedAllocationImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationImageCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDedicatedAllocationImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationImageCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDedicatedAllocationBufferCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationBufferCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDedicatedAllocationBufferCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationBufferCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDedicatedAllocationMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationMemoryAllocateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDedicatedAllocationMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationMemoryAllocateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_AMD_draw_indirect_count
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void transform_tohost_VkTextureLODGatherFormatPropertiesAMD(
    ResourceTracker* resourceTracker,
    VkTextureLODGatherFormatPropertiesAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkTextureLODGatherFormatPropertiesAMD(
    ResourceTracker* resourceTracker,
    VkTextureLODGatherFormatPropertiesAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_AMD_shader_info
void transform_tohost_VkShaderResourceUsageAMD(
    ResourceTracker* resourceTracker,
    VkShaderResourceUsageAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkShaderResourceUsageAMD(
    ResourceTracker* resourceTracker,
    VkShaderResourceUsageAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkShaderStatisticsInfoAMD(
    ResourceTracker* resourceTracker,
    VkShaderStatisticsInfoAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkShaderResourceUsageAMD(resourceTracker, (VkShaderResourceUsageAMD*)(&toTransform->resourceUsage));
}

void transform_fromhost_VkShaderStatisticsInfoAMD(
    ResourceTracker* resourceTracker,
    VkShaderStatisticsInfoAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkShaderResourceUsageAMD(resourceTracker, (VkShaderResourceUsageAMD*)(&toTransform->resourceUsage));
}

#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void transform_tohost_VkExternalImageFormatPropertiesNV(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatPropertiesNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkImageFormatProperties(resourceTracker, (VkImageFormatProperties*)(&toTransform->imageFormatProperties));
}

void transform_fromhost_VkExternalImageFormatPropertiesNV(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatPropertiesNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkImageFormatProperties(resourceTracker, (VkImageFormatProperties*)(&toTransform->imageFormatProperties));
}

#endif
#ifdef VK_NV_external_memory
void transform_tohost_VkExternalMemoryImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExternalMemoryImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_external_memory_win32
void transform_tohost_VkImportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_win32_keyed_mutex
void transform_tohost_VkWin32KeyedMutexAcquireReleaseInfoNV(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)toTransform->pReleaseSyncs, toTransform->releaseCount, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkWin32KeyedMutexAcquireReleaseInfoNV(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)toTransform->pReleaseSyncs, toTransform->releaseCount, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_validation_flags
void transform_tohost_VkValidationFlagsEXT(
    ResourceTracker* resourceTracker,
    VkValidationFlagsEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkValidationFlagsEXT(
    ResourceTracker* resourceTracker,
    VkValidationFlagsEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NN_vi_surface
void transform_tohost_VkViSurfaceCreateInfoNN(
    ResourceTracker* resourceTracker,
    VkViSurfaceCreateInfoNN* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkViSurfaceCreateInfoNN(
    ResourceTracker* resourceTracker,
    VkViSurfaceCreateInfoNN* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void transform_tohost_VkConditionalRenderingBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkConditionalRenderingBeginInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkConditionalRenderingBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkConditionalRenderingBeginInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NVX_device_generated_commands
void transform_tohost_VkDeviceGeneratedCommandsFeaturesNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsFeaturesNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGeneratedCommandsFeaturesNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsFeaturesNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceGeneratedCommandsLimitsNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsLimitsNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceGeneratedCommandsLimitsNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsLimitsNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkIndirectCommandsTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsTokenNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkIndirectCommandsTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsTokenNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkIndirectCommandsLayoutTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutTokenNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkIndirectCommandsLayoutTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutTokenNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkIndirectCommandsLayoutCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutCreateInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pTokens)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->tokenCount; ++i)
        {
            transform_tohost_VkIndirectCommandsLayoutTokenNVX(resourceTracker, (VkIndirectCommandsLayoutTokenNVX*)(toTransform->pTokens + i));
        }
    }
}

void transform_fromhost_VkIndirectCommandsLayoutCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutCreateInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pTokens)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->tokenCount; ++i)
        {
            transform_fromhost_VkIndirectCommandsLayoutTokenNVX(resourceTracker, (VkIndirectCommandsLayoutTokenNVX*)(toTransform->pTokens + i));
        }
    }
}

void transform_tohost_VkCmdProcessCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdProcessCommandsInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pIndirectCommandsTokens)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->indirectCommandsTokenCount; ++i)
        {
            transform_tohost_VkIndirectCommandsTokenNVX(resourceTracker, (VkIndirectCommandsTokenNVX*)(toTransform->pIndirectCommandsTokens + i));
        }
    }
}

void transform_fromhost_VkCmdProcessCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdProcessCommandsInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pIndirectCommandsTokens)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->indirectCommandsTokenCount; ++i)
        {
            transform_fromhost_VkIndirectCommandsTokenNVX(resourceTracker, (VkIndirectCommandsTokenNVX*)(toTransform->pIndirectCommandsTokens + i));
        }
    }
}

void transform_tohost_VkCmdReserveSpaceForCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdReserveSpaceForCommandsInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCmdReserveSpaceForCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdReserveSpaceForCommandsInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkObjectTableCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableCreateInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkObjectTableCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableCreateInfoNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkObjectTableEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkObjectTableEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkObjectTablePipelineEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePipelineEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkObjectTablePipelineEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePipelineEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkObjectTableDescriptorSetEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableDescriptorSetEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkObjectTableDescriptorSetEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableDescriptorSetEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkObjectTableVertexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableVertexBufferEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkObjectTableVertexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableVertexBufferEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkObjectTableIndexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableIndexBufferEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkObjectTableIndexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableIndexBufferEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkObjectTablePushConstantEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePushConstantEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkObjectTablePushConstantEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePushConstantEntryNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

#endif
#ifdef VK_NV_clip_space_w_scaling
void transform_tohost_VkViewportWScalingNV(
    ResourceTracker* resourceTracker,
    VkViewportWScalingNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkViewportWScalingNV(
    ResourceTracker* resourceTracker,
    VkViewportWScalingNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineViewportWScalingStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportWScalingStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pViewportWScalings)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->viewportCount; ++i)
        {
            transform_tohost_VkViewportWScalingNV(resourceTracker, (VkViewportWScalingNV*)(toTransform->pViewportWScalings + i));
        }
    }
}

void transform_fromhost_VkPipelineViewportWScalingStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportWScalingStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pViewportWScalings)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->viewportCount; ++i)
        {
            transform_fromhost_VkViewportWScalingNV(resourceTracker, (VkViewportWScalingNV*)(toTransform->pViewportWScalings + i));
        }
    }
}

#endif
#ifdef VK_EXT_direct_mode_display
#endif
#ifdef VK_EXT_acquire_xlib_display
#endif
#ifdef VK_EXT_display_surface_counter
void transform_tohost_VkSurfaceCapabilities2EXT(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2EXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->currentExtent));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minImageExtent));
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxImageExtent));
}

void transform_fromhost_VkSurfaceCapabilities2EXT(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2EXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->currentExtent));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->minImageExtent));
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxImageExtent));
}

#endif
#ifdef VK_EXT_display_control
void transform_tohost_VkDisplayPowerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayPowerInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDisplayPowerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayPowerInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDeviceEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceEventInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceEventInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDisplayEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayEventInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDisplayEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayEventInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkSwapchainCounterCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSwapchainCounterCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSwapchainCounterCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSwapchainCounterCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_display_timing
void transform_tohost_VkRefreshCycleDurationGOOGLE(
    ResourceTracker* resourceTracker,
    VkRefreshCycleDurationGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkRefreshCycleDurationGOOGLE(
    ResourceTracker* resourceTracker,
    VkRefreshCycleDurationGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPastPresentationTimingGOOGLE(
    ResourceTracker* resourceTracker,
    VkPastPresentationTimingGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPastPresentationTimingGOOGLE(
    ResourceTracker* resourceTracker,
    VkPastPresentationTimingGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPresentTimeGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimeGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkPresentTimeGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimeGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPresentTimesInfoGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimesInfoGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pTimes)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->swapchainCount; ++i)
        {
            transform_tohost_VkPresentTimeGOOGLE(resourceTracker, (VkPresentTimeGOOGLE*)(toTransform->pTimes + i));
        }
    }
}

void transform_fromhost_VkPresentTimesInfoGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimesInfoGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pTimes)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->swapchainCount; ++i)
        {
            transform_fromhost_VkPresentTimeGOOGLE(resourceTracker, (VkPresentTimeGOOGLE*)(toTransform->pTimes + i));
        }
    }
}

#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void transform_tohost_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_viewport_swizzle
void transform_tohost_VkViewportSwizzleNV(
    ResourceTracker* resourceTracker,
    VkViewportSwizzleNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkViewportSwizzleNV(
    ResourceTracker* resourceTracker,
    VkViewportSwizzleNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineViewportSwizzleStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportSwizzleStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pViewportSwizzles)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->viewportCount; ++i)
        {
            transform_tohost_VkViewportSwizzleNV(resourceTracker, (VkViewportSwizzleNV*)(toTransform->pViewportSwizzles + i));
        }
    }
}

void transform_fromhost_VkPipelineViewportSwizzleStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportSwizzleStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pViewportSwizzles)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->viewportCount; ++i)
        {
            transform_fromhost_VkViewportSwizzleNV(resourceTracker, (VkViewportSwizzleNV*)(toTransform->pViewportSwizzles + i));
        }
    }
}

#endif
#ifdef VK_EXT_discard_rectangles
void transform_tohost_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineDiscardRectangleStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineDiscardRectangleStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pDiscardRectangles)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->discardRectangleCount; ++i)
        {
            transform_tohost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pDiscardRectangles + i));
        }
    }
}

void transform_fromhost_VkPipelineDiscardRectangleStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineDiscardRectangleStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pDiscardRectangles)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->discardRectangleCount; ++i)
        {
            transform_fromhost_VkRect2D(resourceTracker, (VkRect2D*)(toTransform->pDiscardRectangles + i));
        }
    }
}

#endif
#ifdef VK_EXT_conservative_rasterization
void transform_tohost_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void transform_tohost_VkXYColorEXT(
    ResourceTracker* resourceTracker,
    VkXYColorEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkXYColorEXT(
    ResourceTracker* resourceTracker,
    VkXYColorEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkHdrMetadataEXT(
    ResourceTracker* resourceTracker,
    VkHdrMetadataEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->displayPrimaryRed));
    transform_tohost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->displayPrimaryGreen));
    transform_tohost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->displayPrimaryBlue));
    transform_tohost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->whitePoint));
}

void transform_fromhost_VkHdrMetadataEXT(
    ResourceTracker* resourceTracker,
    VkHdrMetadataEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->displayPrimaryRed));
    transform_fromhost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->displayPrimaryGreen));
    transform_fromhost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->displayPrimaryBlue));
    transform_fromhost_VkXYColorEXT(resourceTracker, (VkXYColorEXT*)(&toTransform->whitePoint));
}

#endif
#ifdef VK_MVK_ios_surface
void transform_tohost_VkIOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkIOSSurfaceCreateInfoMVK* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkIOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkIOSSurfaceCreateInfoMVK* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_MVK_macos_surface
void transform_tohost_VkMacOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkMacOSSurfaceCreateInfoMVK* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMacOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkMacOSSurfaceCreateInfoMVK* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void transform_tohost_VkDebugUtilsObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectNameInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugUtilsObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectNameInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDebugUtilsObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectTagInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugUtilsObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectTagInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDebugUtilsLabelEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsLabelEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugUtilsLabelEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsLabelEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDebugUtilsMessengerCallbackDataEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCallbackDataEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pQueueLabels)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->queueLabelCount; ++i)
        {
            transform_tohost_VkDebugUtilsLabelEXT(resourceTracker, (VkDebugUtilsLabelEXT*)(toTransform->pQueueLabels + i));
        }
    }
    if (toTransform->pCmdBufLabels)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->cmdBufLabelCount; ++i)
        {
            transform_tohost_VkDebugUtilsLabelEXT(resourceTracker, (VkDebugUtilsLabelEXT*)(toTransform->pCmdBufLabels + i));
        }
    }
    if (toTransform->pObjects)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->objectCount; ++i)
        {
            transform_tohost_VkDebugUtilsObjectNameInfoEXT(resourceTracker, (VkDebugUtilsObjectNameInfoEXT*)(toTransform->pObjects + i));
        }
    }
}

void transform_fromhost_VkDebugUtilsMessengerCallbackDataEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCallbackDataEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pQueueLabels)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->queueLabelCount; ++i)
        {
            transform_fromhost_VkDebugUtilsLabelEXT(resourceTracker, (VkDebugUtilsLabelEXT*)(toTransform->pQueueLabels + i));
        }
    }
    if (toTransform->pCmdBufLabels)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->cmdBufLabelCount; ++i)
        {
            transform_fromhost_VkDebugUtilsLabelEXT(resourceTracker, (VkDebugUtilsLabelEXT*)(toTransform->pCmdBufLabels + i));
        }
    }
    if (toTransform->pObjects)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->objectCount; ++i)
        {
            transform_fromhost_VkDebugUtilsObjectNameInfoEXT(resourceTracker, (VkDebugUtilsObjectNameInfoEXT*)(toTransform->pObjects + i));
        }
    }
}

void transform_tohost_VkDebugUtilsMessengerCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDebugUtilsMessengerCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void transform_tohost_VkAndroidHardwareBufferUsageANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferUsageANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkAndroidHardwareBufferUsageANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferUsageANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkAndroidHardwareBufferPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferPropertiesANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkAndroidHardwareBufferPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferPropertiesANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkAndroidHardwareBufferFormatPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferFormatPropertiesANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkComponentMapping(resourceTracker, (VkComponentMapping*)(&toTransform->samplerYcbcrConversionComponents));
}

void transform_fromhost_VkAndroidHardwareBufferFormatPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferFormatPropertiesANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkComponentMapping(resourceTracker, (VkComponentMapping*)(&toTransform->samplerYcbcrConversionComponents));
}

void transform_tohost_VkImportAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkImportAndroidHardwareBufferInfoANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkImportAndroidHardwareBufferInfoANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)&toTransform->memory, 1, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)nullptr, 0);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkExternalFormatANDROID(
    ResourceTracker* resourceTracker,
    VkExternalFormatANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkExternalFormatANDROID(
    ResourceTracker* resourceTracker,
    VkExternalFormatANDROID* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_sampler_filter_minmax
void transform_tohost_VkSamplerReductionModeCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSamplerReductionModeCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkSamplerReductionModeCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSamplerReductionModeCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void transform_tohost_VkSampleLocationEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkSampleLocationEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkSampleLocationsInfoEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationsInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->sampleLocationGridSize));
    if (toTransform->pSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->sampleLocationsCount; ++i)
        {
            transform_tohost_VkSampleLocationEXT(resourceTracker, (VkSampleLocationEXT*)(toTransform->pSampleLocations + i));
        }
    }
}

void transform_fromhost_VkSampleLocationsInfoEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationsInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->sampleLocationGridSize));
    if (toTransform->pSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->sampleLocationsCount; ++i)
        {
            transform_fromhost_VkSampleLocationEXT(resourceTracker, (VkSampleLocationEXT*)(toTransform->pSampleLocations + i));
        }
    }
}

void transform_tohost_VkAttachmentSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkAttachmentSampleLocationsEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkSampleLocationsInfoEXT(resourceTracker, (VkSampleLocationsInfoEXT*)(&toTransform->sampleLocationsInfo));
}

void transform_fromhost_VkAttachmentSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkAttachmentSampleLocationsEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkSampleLocationsInfoEXT(resourceTracker, (VkSampleLocationsInfoEXT*)(&toTransform->sampleLocationsInfo));
}

void transform_tohost_VkSubpassSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkSubpassSampleLocationsEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_tohost_VkSampleLocationsInfoEXT(resourceTracker, (VkSampleLocationsInfoEXT*)(&toTransform->sampleLocationsInfo));
}

void transform_fromhost_VkSubpassSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkSubpassSampleLocationsEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    transform_fromhost_VkSampleLocationsInfoEXT(resourceTracker, (VkSampleLocationsInfoEXT*)(&toTransform->sampleLocationsInfo));
}

void transform_tohost_VkRenderPassSampleLocationsBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkRenderPassSampleLocationsBeginInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachmentInitialSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentInitialSampleLocationsCount; ++i)
        {
            transform_tohost_VkAttachmentSampleLocationsEXT(resourceTracker, (VkAttachmentSampleLocationsEXT*)(toTransform->pAttachmentInitialSampleLocations + i));
        }
    }
    if (toTransform->pPostSubpassSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->postSubpassSampleLocationsCount; ++i)
        {
            transform_tohost_VkSubpassSampleLocationsEXT(resourceTracker, (VkSubpassSampleLocationsEXT*)(toTransform->pPostSubpassSampleLocations + i));
        }
    }
}

void transform_fromhost_VkRenderPassSampleLocationsBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkRenderPassSampleLocationsBeginInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pAttachmentInitialSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->attachmentInitialSampleLocationsCount; ++i)
        {
            transform_fromhost_VkAttachmentSampleLocationsEXT(resourceTracker, (VkAttachmentSampleLocationsEXT*)(toTransform->pAttachmentInitialSampleLocations + i));
        }
    }
    if (toTransform->pPostSubpassSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->postSubpassSampleLocationsCount; ++i)
        {
            transform_fromhost_VkSubpassSampleLocationsEXT(resourceTracker, (VkSubpassSampleLocationsEXT*)(toTransform->pPostSubpassSampleLocations + i));
        }
    }
}

void transform_tohost_VkPipelineSampleLocationsStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineSampleLocationsStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkSampleLocationsInfoEXT(resourceTracker, (VkSampleLocationsInfoEXT*)(&toTransform->sampleLocationsInfo));
}

void transform_fromhost_VkPipelineSampleLocationsStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineSampleLocationsStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkSampleLocationsInfoEXT(resourceTracker, (VkSampleLocationsInfoEXT*)(&toTransform->sampleLocationsInfo));
}

void transform_tohost_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxSampleLocationGridSize));
}

void transform_fromhost_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxSampleLocationGridSize));
}

void transform_tohost_VkMultisamplePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMultisamplePropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_tohost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxSampleLocationGridSize));
}

void transform_fromhost_VkMultisamplePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMultisamplePropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    transform_fromhost_VkExtent2D(resourceTracker, (VkExtent2D*)(&toTransform->maxSampleLocationGridSize));
}

#endif
#ifdef VK_EXT_blend_operation_advanced
void transform_tohost_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_fragment_coverage_to_color
void transform_tohost_VkPipelineCoverageToColorStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageToColorStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineCoverageToColorStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageToColorStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void transform_tohost_VkPipelineCoverageModulationStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageModulationStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPipelineCoverageModulationStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageModulationStateCreateInfoNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void transform_tohost_VkValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkValidationCacheCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkValidationCacheCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkShaderModuleValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkShaderModuleValidationCacheCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkShaderModuleValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkShaderModuleValidationCacheCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_descriptor_indexing
void transform_tohost_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void transform_tohost_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_external_memory_host
void transform_tohost_VkImportMemoryHostPointerInfoEXT(
    ResourceTracker* resourceTracker,
    VkImportMemoryHostPointerInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportMemoryHostPointerInfoEXT(
    ResourceTracker* resourceTracker,
    VkImportMemoryHostPointerInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkMemoryHostPointerPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMemoryHostPointerPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_tohost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkMemoryHostPointerPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMemoryHostPointerPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    resourceTracker->deviceMemoryTransform_fromhost((VkDeviceMemory*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (VkDeviceSize*)nullptr, 0, (uint32_t*)nullptr, 0, (uint32_t*)&toTransform->memoryTypeBits, 1);
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_AMD_buffer_marker
#endif
#ifdef VK_AMD_shader_core_properties
void transform_tohost_VkPhysicalDeviceShaderCorePropertiesAMD(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderCorePropertiesAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceShaderCorePropertiesAMD(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderCorePropertiesAMD* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void transform_tohost_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkVertexInputBindingDivisorDescriptionEXT(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDivisorDescriptionEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_fromhost_VkVertexInputBindingDivisorDescriptionEXT(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDivisorDescriptionEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
}

void transform_tohost_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pVertexBindingDivisors)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->vertexBindingDivisorCount; ++i)
        {
            transform_tohost_VkVertexInputBindingDivisorDescriptionEXT(resourceTracker, (VkVertexInputBindingDivisorDescriptionEXT*)(toTransform->pVertexBindingDivisors + i));
        }
    }
}

void transform_fromhost_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
    if (toTransform->pVertexBindingDivisors)
    {
        for (uint32_t i = 0; i < (uint32_t)toTransform->vertexBindingDivisorCount; ++i)
        {
            transform_fromhost_VkVertexInputBindingDivisorDescriptionEXT(resourceTracker, (VkVertexInputBindingDivisorDescriptionEXT*)(toTransform->pVertexBindingDivisors + i));
        }
    }
}

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void transform_tohost_VkQueueFamilyCheckpointPropertiesNV(
    ResourceTracker* resourceTracker,
    VkQueueFamilyCheckpointPropertiesNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkQueueFamilyCheckpointPropertiesNV(
    ResourceTracker* resourceTracker,
    VkQueueFamilyCheckpointPropertiesNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkCheckpointDataNV(
    ResourceTracker* resourceTracker,
    VkCheckpointDataNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkCheckpointDataNV(
    ResourceTracker* resourceTracker,
    VkCheckpointDataNV* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_address_space
#endif
#ifdef VK_GOOGLE_color_buffer
void transform_tohost_VkImportColorBufferGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportColorBufferGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportColorBufferGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportColorBufferGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_tohost_VkImportPhysicalAddressGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportPhysicalAddressGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_tohost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

void transform_fromhost_VkImportPhysicalAddressGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportPhysicalAddressGOOGLE* toTransform)
{
    (void)resourceTracker;
    (void)toTransform;
    if (toTransform->pNext)
    {
        transform_fromhost_extension_struct(resourceTracker, (void*)(toTransform->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#endif
#ifdef VK_GOOGLE_async_command_buffers
#endif
void transform_tohost_extension_struct(
    ResourceTracker* resourceTracker,
    void* structExtension_out)
{
    if (!structExtension_out)
    {
        return;
    }
    uint32_t structType = (uint32_t)goldfish_vk_struct_type(structExtension_out);
    switch(structType)
    {
#ifdef VK_VERSION_1_1
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SUBGROUP_PROPERTIES:
        {
            transform_tohost_VkPhysicalDeviceSubgroupProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceSubgroupProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_16BIT_STORAGE_FEATURES:
        {
            transform_tohost_VkPhysicalDevice16BitStorageFeatures(resourceTracker, reinterpret_cast<VkPhysicalDevice16BitStorageFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_REQUIREMENTS:
        {
            transform_tohost_VkMemoryDedicatedRequirements(resourceTracker, reinterpret_cast<VkMemoryDedicatedRequirements*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO:
        {
            transform_tohost_VkMemoryDedicatedAllocateInfo(resourceTracker, reinterpret_cast<VkMemoryDedicatedAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_FLAGS_INFO:
        {
            transform_tohost_VkMemoryAllocateFlagsInfo(resourceTracker, reinterpret_cast<VkMemoryAllocateFlagsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_RENDER_PASS_BEGIN_INFO:
        {
            transform_tohost_VkDeviceGroupRenderPassBeginInfo(resourceTracker, reinterpret_cast<VkDeviceGroupRenderPassBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_COMMAND_BUFFER_BEGIN_INFO:
        {
            transform_tohost_VkDeviceGroupCommandBufferBeginInfo(resourceTracker, reinterpret_cast<VkDeviceGroupCommandBufferBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SUBMIT_INFO:
        {
            transform_tohost_VkDeviceGroupSubmitInfo(resourceTracker, reinterpret_cast<VkDeviceGroupSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_BIND_SPARSE_INFO:
        {
            transform_tohost_VkDeviceGroupBindSparseInfo(resourceTracker, reinterpret_cast<VkDeviceGroupBindSparseInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_BUFFER_MEMORY_DEVICE_GROUP_INFO:
        {
            transform_tohost_VkBindBufferMemoryDeviceGroupInfo(resourceTracker, reinterpret_cast<VkBindBufferMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_DEVICE_GROUP_INFO:
        {
            transform_tohost_VkBindImageMemoryDeviceGroupInfo(resourceTracker, reinterpret_cast<VkBindImageMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_DEVICE_CREATE_INFO:
        {
            transform_tohost_VkDeviceGroupDeviceCreateInfo(resourceTracker, reinterpret_cast<VkDeviceGroupDeviceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2:
        {
            transform_tohost_VkPhysicalDeviceFeatures2(resourceTracker, reinterpret_cast<VkPhysicalDeviceFeatures2*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_POINT_CLIPPING_PROPERTIES:
        {
            transform_tohost_VkPhysicalDevicePointClippingProperties(resourceTracker, reinterpret_cast<VkPhysicalDevicePointClippingProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_INPUT_ATTACHMENT_ASPECT_CREATE_INFO:
        {
            transform_tohost_VkRenderPassInputAttachmentAspectCreateInfo(resourceTracker, reinterpret_cast<VkRenderPassInputAttachmentAspectCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_VIEW_USAGE_CREATE_INFO:
        {
            transform_tohost_VkImageViewUsageCreateInfo(resourceTracker, reinterpret_cast<VkImageViewUsageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_DOMAIN_ORIGIN_STATE_CREATE_INFO:
        {
            transform_tohost_VkPipelineTessellationDomainOriginStateCreateInfo(resourceTracker, reinterpret_cast<VkPipelineTessellationDomainOriginStateCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_MULTIVIEW_CREATE_INFO:
        {
            transform_tohost_VkRenderPassMultiviewCreateInfo(resourceTracker, reinterpret_cast<VkRenderPassMultiviewCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_FEATURES:
        {
            transform_tohost_VkPhysicalDeviceMultiviewFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceMultiviewFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PROPERTIES:
        {
            transform_tohost_VkPhysicalDeviceMultiviewProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceMultiviewProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTER_FEATURES:
        {
            transform_tohost_VkPhysicalDeviceVariablePointerFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceVariablePointerFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_FEATURES:
        {
            transform_tohost_VkPhysicalDeviceProtectedMemoryFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceProtectedMemoryFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_PROPERTIES:
        {
            transform_tohost_VkPhysicalDeviceProtectedMemoryProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceProtectedMemoryProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PROTECTED_SUBMIT_INFO:
        {
            transform_tohost_VkProtectedSubmitInfo(resourceTracker, reinterpret_cast<VkProtectedSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_INFO:
        {
            transform_tohost_VkSamplerYcbcrConversionInfo(resourceTracker, reinterpret_cast<VkSamplerYcbcrConversionInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_PLANE_MEMORY_INFO:
        {
            transform_tohost_VkBindImagePlaneMemoryInfo(resourceTracker, reinterpret_cast<VkBindImagePlaneMemoryInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_PLANE_MEMORY_REQUIREMENTS_INFO:
        {
            transform_tohost_VkImagePlaneMemoryRequirementsInfo(resourceTracker, reinterpret_cast<VkImagePlaneMemoryRequirementsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_YCBCR_CONVERSION_FEATURES:
        {
            transform_tohost_VkPhysicalDeviceSamplerYcbcrConversionFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceSamplerYcbcrConversionFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_IMAGE_FORMAT_PROPERTIES:
        {
            transform_tohost_VkSamplerYcbcrConversionImageFormatProperties(resourceTracker, reinterpret_cast<VkSamplerYcbcrConversionImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_IMAGE_FORMAT_INFO:
        {
            resourceTracker->transformImpl_VkPhysicalDeviceExternalImageFormatInfo_tohost(reinterpret_cast<VkPhysicalDeviceExternalImageFormatInfo*>(structExtension_out), 1);
            transform_tohost_VkPhysicalDeviceExternalImageFormatInfo(resourceTracker, reinterpret_cast<VkPhysicalDeviceExternalImageFormatInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_IMAGE_FORMAT_PROPERTIES:
        {
            resourceTracker->transformImpl_VkExternalImageFormatProperties_tohost(reinterpret_cast<VkExternalImageFormatProperties*>(structExtension_out), 1);
            transform_tohost_VkExternalImageFormatProperties(resourceTracker, reinterpret_cast<VkExternalImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ID_PROPERTIES:
        {
            transform_tohost_VkPhysicalDeviceIDProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceIDProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO:
        {
            resourceTracker->transformImpl_VkExternalMemoryImageCreateInfo_tohost(reinterpret_cast<VkExternalMemoryImageCreateInfo*>(structExtension_out), 1);
            transform_tohost_VkExternalMemoryImageCreateInfo(resourceTracker, reinterpret_cast<VkExternalMemoryImageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_BUFFER_CREATE_INFO:
        {
            resourceTracker->transformImpl_VkExternalMemoryBufferCreateInfo_tohost(reinterpret_cast<VkExternalMemoryBufferCreateInfo*>(structExtension_out), 1);
            transform_tohost_VkExternalMemoryBufferCreateInfo(resourceTracker, reinterpret_cast<VkExternalMemoryBufferCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO:
        {
            resourceTracker->transformImpl_VkExportMemoryAllocateInfo_tohost(reinterpret_cast<VkExportMemoryAllocateInfo*>(structExtension_out), 1);
            transform_tohost_VkExportMemoryAllocateInfo(resourceTracker, reinterpret_cast<VkExportMemoryAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_CREATE_INFO:
        {
            transform_tohost_VkExportFenceCreateInfo(resourceTracker, reinterpret_cast<VkExportFenceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_CREATE_INFO:
        {
            transform_tohost_VkExportSemaphoreCreateInfo(resourceTracker, reinterpret_cast<VkExportSemaphoreCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MAINTENANCE_3_PROPERTIES:
        {
            transform_tohost_VkPhysicalDeviceMaintenance3Properties(resourceTracker, reinterpret_cast<VkPhysicalDeviceMaintenance3Properties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETER_FEATURES:
        {
            transform_tohost_VkPhysicalDeviceShaderDrawParameterFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceShaderDrawParameterFeatures*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_swapchain
        case VK_STRUCTURE_TYPE_IMAGE_SWAPCHAIN_CREATE_INFO_KHR:
        {
            transform_tohost_VkImageSwapchainCreateInfoKHR(resourceTracker, reinterpret_cast<VkImageSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_SWAPCHAIN_INFO_KHR:
        {
            transform_tohost_VkBindImageMemorySwapchainInfoKHR(resourceTracker, reinterpret_cast<VkBindImageMemorySwapchainInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_PRESENT_INFO_KHR:
        {
            transform_tohost_VkDeviceGroupPresentInfoKHR(resourceTracker, reinterpret_cast<VkDeviceGroupPresentInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SWAPCHAIN_CREATE_INFO_KHR:
        {
            transform_tohost_VkDeviceGroupSwapchainCreateInfoKHR(resourceTracker, reinterpret_cast<VkDeviceGroupSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_display_swapchain
        case VK_STRUCTURE_TYPE_DISPLAY_PRESENT_INFO_KHR:
        {
            transform_tohost_VkDisplayPresentInfoKHR(resourceTracker, reinterpret_cast<VkDisplayPresentInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            transform_tohost_VkImportMemoryWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkImportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            transform_tohost_VkExportMemoryWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkExportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_fd
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_FD_INFO_KHR:
        {
            transform_tohost_VkImportMemoryFdInfoKHR(resourceTracker, reinterpret_cast<VkImportMemoryFdInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_KHR:
        {
            transform_tohost_VkWin32KeyedMutexAcquireReleaseInfoKHR(resourceTracker, reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_semaphore_win32
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_WIN32_HANDLE_INFO_KHR:
        {
            transform_tohost_VkExportSemaphoreWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkExportSemaphoreWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_D3D12_FENCE_SUBMIT_INFO_KHR:
        {
            transform_tohost_VkD3D12FenceSubmitInfoKHR(resourceTracker, reinterpret_cast<VkD3D12FenceSubmitInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_push_descriptor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PUSH_DESCRIPTOR_PROPERTIES_KHR:
        {
            transform_tohost_VkPhysicalDevicePushDescriptorPropertiesKHR(resourceTracker, reinterpret_cast<VkPhysicalDevicePushDescriptorPropertiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_incremental_present
        case VK_STRUCTURE_TYPE_PRESENT_REGIONS_KHR:
        {
            transform_tohost_VkPresentRegionsKHR(resourceTracker, reinterpret_cast<VkPresentRegionsKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_shared_presentable_image
        case VK_STRUCTURE_TYPE_SHARED_PRESENT_SURFACE_CAPABILITIES_KHR:
        {
            transform_tohost_VkSharedPresentSurfaceCapabilitiesKHR(resourceTracker, reinterpret_cast<VkSharedPresentSurfaceCapabilitiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_fence_win32
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_WIN32_HANDLE_INFO_KHR:
        {
            transform_tohost_VkExportFenceWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkExportFenceWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_image_format_list
        case VK_STRUCTURE_TYPE_IMAGE_FORMAT_LIST_CREATE_INFO_KHR:
        {
            transform_tohost_VkImageFormatListCreateInfoKHR(resourceTracker, reinterpret_cast<VkImageFormatListCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_8bit_storage
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_8BIT_STORAGE_FEATURES_KHR:
        {
            transform_tohost_VkPhysicalDevice8BitStorageFeaturesKHR(resourceTracker, reinterpret_cast<VkPhysicalDevice8BitStorageFeaturesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_native_buffer
        case VK_STRUCTURE_TYPE_NATIVE_BUFFER_ANDROID:
        {
            transform_tohost_VkNativeBufferANDROID(resourceTracker, reinterpret_cast<VkNativeBufferANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_report
        case VK_STRUCTURE_TYPE_DEBUG_REPORT_CALLBACK_CREATE_INFO_EXT:
        {
            transform_tohost_VkDebugReportCallbackCreateInfoEXT(resourceTracker, reinterpret_cast<VkDebugReportCallbackCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_rasterization_order
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_RASTERIZATION_ORDER_AMD:
        {
            transform_tohost_VkPipelineRasterizationStateRasterizationOrderAMD(resourceTracker, reinterpret_cast<VkPipelineRasterizationStateRasterizationOrderAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_dedicated_allocation
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_IMAGE_CREATE_INFO_NV:
        {
            transform_tohost_VkDedicatedAllocationImageCreateInfoNV(resourceTracker, reinterpret_cast<VkDedicatedAllocationImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_BUFFER_CREATE_INFO_NV:
        {
            transform_tohost_VkDedicatedAllocationBufferCreateInfoNV(resourceTracker, reinterpret_cast<VkDedicatedAllocationBufferCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_MEMORY_ALLOCATE_INFO_NV:
        {
            transform_tohost_VkDedicatedAllocationMemoryAllocateInfoNV(resourceTracker, reinterpret_cast<VkDedicatedAllocationMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_texture_gather_bias_lod
        case VK_STRUCTURE_TYPE_TEXTURE_LOD_GATHER_FORMAT_PROPERTIES_AMD:
        {
            transform_tohost_VkTextureLODGatherFormatPropertiesAMD(resourceTracker, reinterpret_cast<VkTextureLODGatherFormatPropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO_NV:
        {
            transform_tohost_VkExternalMemoryImageCreateInfoNV(resourceTracker, reinterpret_cast<VkExternalMemoryImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO_NV:
        {
            transform_tohost_VkExportMemoryAllocateInfoNV(resourceTracker, reinterpret_cast<VkExportMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            transform_tohost_VkImportMemoryWin32HandleInfoNV(resourceTracker, reinterpret_cast<VkImportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            transform_tohost_VkExportMemoryWin32HandleInfoNV(resourceTracker, reinterpret_cast<VkExportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_NV:
        {
            transform_tohost_VkWin32KeyedMutexAcquireReleaseInfoNV(resourceTracker, reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_flags
        case VK_STRUCTURE_TYPE_VALIDATION_FLAGS_EXT:
        {
            transform_tohost_VkValidationFlagsEXT(resourceTracker, reinterpret_cast<VkValidationFlagsEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conditional_rendering
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONDITIONAL_RENDERING_FEATURES_EXT:
        {
            transform_tohost_VkPhysicalDeviceConditionalRenderingFeaturesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceConditionalRenderingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_COMMAND_BUFFER_INHERITANCE_CONDITIONAL_RENDERING_INFO_EXT:
        {
            transform_tohost_VkCommandBufferInheritanceConditionalRenderingInfoEXT(resourceTracker, reinterpret_cast<VkCommandBufferInheritanceConditionalRenderingInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_clip_space_w_scaling
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_W_SCALING_STATE_CREATE_INFO_NV:
        {
            transform_tohost_VkPipelineViewportWScalingStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineViewportWScalingStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_display_control
        case VK_STRUCTURE_TYPE_SWAPCHAIN_COUNTER_CREATE_INFO_EXT:
        {
            transform_tohost_VkSwapchainCounterCreateInfoEXT(resourceTracker, reinterpret_cast<VkSwapchainCounterCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_display_timing
        case VK_STRUCTURE_TYPE_PRESENT_TIMES_INFO_GOOGLE:
        {
            transform_tohost_VkPresentTimesInfoGOOGLE(resourceTracker, reinterpret_cast<VkPresentTimesInfoGOOGLE*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NVX_multiview_per_view_attributes
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PER_VIEW_ATTRIBUTES_PROPERTIES_NVX:
        {
            transform_tohost_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(resourceTracker, reinterpret_cast<VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_viewport_swizzle
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_SWIZZLE_STATE_CREATE_INFO_NV:
        {
            transform_tohost_VkPipelineViewportSwizzleStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineViewportSwizzleStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_discard_rectangles
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DISCARD_RECTANGLE_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceDiscardRectanglePropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceDiscardRectanglePropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_DISCARD_RECTANGLE_STATE_CREATE_INFO_EXT:
        {
            transform_tohost_VkPipelineDiscardRectangleStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineDiscardRectangleStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conservative_rasterization
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONSERVATIVE_RASTERIZATION_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceConservativeRasterizationPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_CONSERVATIVE_STATE_CREATE_INFO_EXT:
        {
            transform_tohost_VkPipelineRasterizationConservativeStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineRasterizationConservativeStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_utils
        case VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT:
        {
            transform_tohost_VkDebugUtilsMessengerCreateInfoEXT(resourceTracker, reinterpret_cast<VkDebugUtilsMessengerCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_USAGE_ANDROID:
        {
            transform_tohost_VkAndroidHardwareBufferUsageANDROID(resourceTracker, reinterpret_cast<VkAndroidHardwareBufferUsageANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_FORMAT_PROPERTIES_ANDROID:
        {
            transform_tohost_VkAndroidHardwareBufferFormatPropertiesANDROID(resourceTracker, reinterpret_cast<VkAndroidHardwareBufferFormatPropertiesANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_ANDROID_HARDWARE_BUFFER_INFO_ANDROID:
        {
            transform_tohost_VkImportAndroidHardwareBufferInfoANDROID(resourceTracker, reinterpret_cast<VkImportAndroidHardwareBufferInfoANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_FORMAT_ANDROID:
        {
            transform_tohost_VkExternalFormatANDROID(resourceTracker, reinterpret_cast<VkExternalFormatANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sampler_filter_minmax
        case VK_STRUCTURE_TYPE_SAMPLER_REDUCTION_MODE_CREATE_INFO_EXT:
        {
            transform_tohost_VkSamplerReductionModeCreateInfoEXT(resourceTracker, reinterpret_cast<VkSamplerReductionModeCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_FILTER_MINMAX_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sample_locations
        case VK_STRUCTURE_TYPE_SAMPLE_LOCATIONS_INFO_EXT:
        {
            transform_tohost_VkSampleLocationsInfoEXT(resourceTracker, reinterpret_cast<VkSampleLocationsInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_SAMPLE_LOCATIONS_BEGIN_INFO_EXT:
        {
            transform_tohost_VkRenderPassSampleLocationsBeginInfoEXT(resourceTracker, reinterpret_cast<VkRenderPassSampleLocationsBeginInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_SAMPLE_LOCATIONS_STATE_CREATE_INFO_EXT:
        {
            transform_tohost_VkPipelineSampleLocationsStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineSampleLocationsStateCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLE_LOCATIONS_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceSampleLocationsPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceSampleLocationsPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_blend_operation_advanced
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_FEATURES_EXT:
        {
            transform_tohost_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_ADVANCED_STATE_CREATE_INFO_EXT:
        {
            transform_tohost_VkPipelineColorBlendAdvancedStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineColorBlendAdvancedStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_fragment_coverage_to_color
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_TO_COLOR_STATE_CREATE_INFO_NV:
        {
            transform_tohost_VkPipelineCoverageToColorStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineCoverageToColorStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_framebuffer_mixed_samples
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_MODULATION_STATE_CREATE_INFO_NV:
        {
            transform_tohost_VkPipelineCoverageModulationStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineCoverageModulationStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_cache
        case VK_STRUCTURE_TYPE_SHADER_MODULE_VALIDATION_CACHE_CREATE_INFO_EXT:
        {
            transform_tohost_VkShaderModuleValidationCacheCreateInfoEXT(resourceTracker, reinterpret_cast<VkShaderModuleValidationCacheCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_descriptor_indexing
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_BINDING_FLAGS_CREATE_INFO_EXT:
        {
            transform_tohost_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(resourceTracker, reinterpret_cast<VkDescriptorSetLayoutBindingFlagsCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_FEATURES_EXT:
        {
            transform_tohost_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceDescriptorIndexingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceDescriptorIndexingPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_ALLOCATE_INFO_EXT:
        {
            transform_tohost_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(resourceTracker, reinterpret_cast<VkDescriptorSetVariableDescriptorCountAllocateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_LAYOUT_SUPPORT_EXT:
        {
            transform_tohost_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(resourceTracker, reinterpret_cast<VkDescriptorSetVariableDescriptorCountLayoutSupportEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_global_priority
        case VK_STRUCTURE_TYPE_DEVICE_QUEUE_GLOBAL_PRIORITY_CREATE_INFO_EXT:
        {
            transform_tohost_VkDeviceQueueGlobalPriorityCreateInfoEXT(resourceTracker, reinterpret_cast<VkDeviceQueueGlobalPriorityCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_external_memory_host
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_HOST_POINTER_INFO_EXT:
        {
            transform_tohost_VkImportMemoryHostPointerInfoEXT(resourceTracker, reinterpret_cast<VkImportMemoryHostPointerInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_MEMORY_HOST_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceExternalMemoryHostPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_shader_core_properties
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_CORE_PROPERTIES_AMD:
        {
            transform_tohost_VkPhysicalDeviceShaderCorePropertiesAMD(resourceTracker, reinterpret_cast<VkPhysicalDeviceShaderCorePropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_vertex_attribute_divisor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VERTEX_ATTRIBUTE_DIVISOR_PROPERTIES_EXT:
        {
            transform_tohost_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_DIVISOR_STATE_CREATE_INFO_EXT:
        {
            transform_tohost_VkPipelineVertexInputDivisorStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineVertexInputDivisorStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
        case VK_STRUCTURE_TYPE_QUEUE_FAMILY_CHECKPOINT_PROPERTIES_NV:
        {
            transform_tohost_VkQueueFamilyCheckpointPropertiesNV(resourceTracker, reinterpret_cast<VkQueueFamilyCheckpointPropertiesNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_color_buffer
        case VK_STRUCTURE_TYPE_IMPORT_COLOR_BUFFER_GOOGLE:
        {
            transform_tohost_VkImportColorBufferGOOGLE(resourceTracker, reinterpret_cast<VkImportColorBufferGOOGLE*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_PHYSICAL_ADDRESS_GOOGLE:
        {
            transform_tohost_VkImportPhysicalAddressGOOGLE(resourceTracker, reinterpret_cast<VkImportPhysicalAddressGOOGLE*>(structExtension_out));
            break;
        }
#endif
        default:
        {
            return;
        }
    }
}

void transform_fromhost_extension_struct(
    ResourceTracker* resourceTracker,
    void* structExtension_out)
{
    if (!structExtension_out)
    {
        return;
    }
    uint32_t structType = (uint32_t)goldfish_vk_struct_type(structExtension_out);
    switch(structType)
    {
#ifdef VK_VERSION_1_1
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SUBGROUP_PROPERTIES:
        {
            transform_fromhost_VkPhysicalDeviceSubgroupProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceSubgroupProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_16BIT_STORAGE_FEATURES:
        {
            transform_fromhost_VkPhysicalDevice16BitStorageFeatures(resourceTracker, reinterpret_cast<VkPhysicalDevice16BitStorageFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_REQUIREMENTS:
        {
            transform_fromhost_VkMemoryDedicatedRequirements(resourceTracker, reinterpret_cast<VkMemoryDedicatedRequirements*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO:
        {
            transform_fromhost_VkMemoryDedicatedAllocateInfo(resourceTracker, reinterpret_cast<VkMemoryDedicatedAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_FLAGS_INFO:
        {
            transform_fromhost_VkMemoryAllocateFlagsInfo(resourceTracker, reinterpret_cast<VkMemoryAllocateFlagsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_RENDER_PASS_BEGIN_INFO:
        {
            transform_fromhost_VkDeviceGroupRenderPassBeginInfo(resourceTracker, reinterpret_cast<VkDeviceGroupRenderPassBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_COMMAND_BUFFER_BEGIN_INFO:
        {
            transform_fromhost_VkDeviceGroupCommandBufferBeginInfo(resourceTracker, reinterpret_cast<VkDeviceGroupCommandBufferBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SUBMIT_INFO:
        {
            transform_fromhost_VkDeviceGroupSubmitInfo(resourceTracker, reinterpret_cast<VkDeviceGroupSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_BIND_SPARSE_INFO:
        {
            transform_fromhost_VkDeviceGroupBindSparseInfo(resourceTracker, reinterpret_cast<VkDeviceGroupBindSparseInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_BUFFER_MEMORY_DEVICE_GROUP_INFO:
        {
            transform_fromhost_VkBindBufferMemoryDeviceGroupInfo(resourceTracker, reinterpret_cast<VkBindBufferMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_DEVICE_GROUP_INFO:
        {
            transform_fromhost_VkBindImageMemoryDeviceGroupInfo(resourceTracker, reinterpret_cast<VkBindImageMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_DEVICE_CREATE_INFO:
        {
            transform_fromhost_VkDeviceGroupDeviceCreateInfo(resourceTracker, reinterpret_cast<VkDeviceGroupDeviceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2:
        {
            transform_fromhost_VkPhysicalDeviceFeatures2(resourceTracker, reinterpret_cast<VkPhysicalDeviceFeatures2*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_POINT_CLIPPING_PROPERTIES:
        {
            transform_fromhost_VkPhysicalDevicePointClippingProperties(resourceTracker, reinterpret_cast<VkPhysicalDevicePointClippingProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_INPUT_ATTACHMENT_ASPECT_CREATE_INFO:
        {
            transform_fromhost_VkRenderPassInputAttachmentAspectCreateInfo(resourceTracker, reinterpret_cast<VkRenderPassInputAttachmentAspectCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_VIEW_USAGE_CREATE_INFO:
        {
            transform_fromhost_VkImageViewUsageCreateInfo(resourceTracker, reinterpret_cast<VkImageViewUsageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_DOMAIN_ORIGIN_STATE_CREATE_INFO:
        {
            transform_fromhost_VkPipelineTessellationDomainOriginStateCreateInfo(resourceTracker, reinterpret_cast<VkPipelineTessellationDomainOriginStateCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_MULTIVIEW_CREATE_INFO:
        {
            transform_fromhost_VkRenderPassMultiviewCreateInfo(resourceTracker, reinterpret_cast<VkRenderPassMultiviewCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_FEATURES:
        {
            transform_fromhost_VkPhysicalDeviceMultiviewFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceMultiviewFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PROPERTIES:
        {
            transform_fromhost_VkPhysicalDeviceMultiviewProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceMultiviewProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTER_FEATURES:
        {
            transform_fromhost_VkPhysicalDeviceVariablePointerFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceVariablePointerFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_FEATURES:
        {
            transform_fromhost_VkPhysicalDeviceProtectedMemoryFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceProtectedMemoryFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_PROPERTIES:
        {
            transform_fromhost_VkPhysicalDeviceProtectedMemoryProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceProtectedMemoryProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PROTECTED_SUBMIT_INFO:
        {
            transform_fromhost_VkProtectedSubmitInfo(resourceTracker, reinterpret_cast<VkProtectedSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_INFO:
        {
            transform_fromhost_VkSamplerYcbcrConversionInfo(resourceTracker, reinterpret_cast<VkSamplerYcbcrConversionInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_PLANE_MEMORY_INFO:
        {
            transform_fromhost_VkBindImagePlaneMemoryInfo(resourceTracker, reinterpret_cast<VkBindImagePlaneMemoryInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_PLANE_MEMORY_REQUIREMENTS_INFO:
        {
            transform_fromhost_VkImagePlaneMemoryRequirementsInfo(resourceTracker, reinterpret_cast<VkImagePlaneMemoryRequirementsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_YCBCR_CONVERSION_FEATURES:
        {
            transform_fromhost_VkPhysicalDeviceSamplerYcbcrConversionFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceSamplerYcbcrConversionFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_IMAGE_FORMAT_PROPERTIES:
        {
            transform_fromhost_VkSamplerYcbcrConversionImageFormatProperties(resourceTracker, reinterpret_cast<VkSamplerYcbcrConversionImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_IMAGE_FORMAT_INFO:
        {
            resourceTracker->transformImpl_VkPhysicalDeviceExternalImageFormatInfo_fromhost(reinterpret_cast<VkPhysicalDeviceExternalImageFormatInfo*>(structExtension_out), 1);
            transform_fromhost_VkPhysicalDeviceExternalImageFormatInfo(resourceTracker, reinterpret_cast<VkPhysicalDeviceExternalImageFormatInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_IMAGE_FORMAT_PROPERTIES:
        {
            resourceTracker->transformImpl_VkExternalImageFormatProperties_fromhost(reinterpret_cast<VkExternalImageFormatProperties*>(structExtension_out), 1);
            transform_fromhost_VkExternalImageFormatProperties(resourceTracker, reinterpret_cast<VkExternalImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ID_PROPERTIES:
        {
            transform_fromhost_VkPhysicalDeviceIDProperties(resourceTracker, reinterpret_cast<VkPhysicalDeviceIDProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO:
        {
            resourceTracker->transformImpl_VkExternalMemoryImageCreateInfo_fromhost(reinterpret_cast<VkExternalMemoryImageCreateInfo*>(structExtension_out), 1);
            transform_fromhost_VkExternalMemoryImageCreateInfo(resourceTracker, reinterpret_cast<VkExternalMemoryImageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_BUFFER_CREATE_INFO:
        {
            resourceTracker->transformImpl_VkExternalMemoryBufferCreateInfo_fromhost(reinterpret_cast<VkExternalMemoryBufferCreateInfo*>(structExtension_out), 1);
            transform_fromhost_VkExternalMemoryBufferCreateInfo(resourceTracker, reinterpret_cast<VkExternalMemoryBufferCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO:
        {
            resourceTracker->transformImpl_VkExportMemoryAllocateInfo_fromhost(reinterpret_cast<VkExportMemoryAllocateInfo*>(structExtension_out), 1);
            transform_fromhost_VkExportMemoryAllocateInfo(resourceTracker, reinterpret_cast<VkExportMemoryAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_CREATE_INFO:
        {
            transform_fromhost_VkExportFenceCreateInfo(resourceTracker, reinterpret_cast<VkExportFenceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_CREATE_INFO:
        {
            transform_fromhost_VkExportSemaphoreCreateInfo(resourceTracker, reinterpret_cast<VkExportSemaphoreCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MAINTENANCE_3_PROPERTIES:
        {
            transform_fromhost_VkPhysicalDeviceMaintenance3Properties(resourceTracker, reinterpret_cast<VkPhysicalDeviceMaintenance3Properties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETER_FEATURES:
        {
            transform_fromhost_VkPhysicalDeviceShaderDrawParameterFeatures(resourceTracker, reinterpret_cast<VkPhysicalDeviceShaderDrawParameterFeatures*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_swapchain
        case VK_STRUCTURE_TYPE_IMAGE_SWAPCHAIN_CREATE_INFO_KHR:
        {
            transform_fromhost_VkImageSwapchainCreateInfoKHR(resourceTracker, reinterpret_cast<VkImageSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_SWAPCHAIN_INFO_KHR:
        {
            transform_fromhost_VkBindImageMemorySwapchainInfoKHR(resourceTracker, reinterpret_cast<VkBindImageMemorySwapchainInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_PRESENT_INFO_KHR:
        {
            transform_fromhost_VkDeviceGroupPresentInfoKHR(resourceTracker, reinterpret_cast<VkDeviceGroupPresentInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SWAPCHAIN_CREATE_INFO_KHR:
        {
            transform_fromhost_VkDeviceGroupSwapchainCreateInfoKHR(resourceTracker, reinterpret_cast<VkDeviceGroupSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_display_swapchain
        case VK_STRUCTURE_TYPE_DISPLAY_PRESENT_INFO_KHR:
        {
            transform_fromhost_VkDisplayPresentInfoKHR(resourceTracker, reinterpret_cast<VkDisplayPresentInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            transform_fromhost_VkImportMemoryWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkImportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            transform_fromhost_VkExportMemoryWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkExportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_fd
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_FD_INFO_KHR:
        {
            transform_fromhost_VkImportMemoryFdInfoKHR(resourceTracker, reinterpret_cast<VkImportMemoryFdInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_KHR:
        {
            transform_fromhost_VkWin32KeyedMutexAcquireReleaseInfoKHR(resourceTracker, reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_semaphore_win32
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_WIN32_HANDLE_INFO_KHR:
        {
            transform_fromhost_VkExportSemaphoreWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkExportSemaphoreWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_D3D12_FENCE_SUBMIT_INFO_KHR:
        {
            transform_fromhost_VkD3D12FenceSubmitInfoKHR(resourceTracker, reinterpret_cast<VkD3D12FenceSubmitInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_push_descriptor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PUSH_DESCRIPTOR_PROPERTIES_KHR:
        {
            transform_fromhost_VkPhysicalDevicePushDescriptorPropertiesKHR(resourceTracker, reinterpret_cast<VkPhysicalDevicePushDescriptorPropertiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_incremental_present
        case VK_STRUCTURE_TYPE_PRESENT_REGIONS_KHR:
        {
            transform_fromhost_VkPresentRegionsKHR(resourceTracker, reinterpret_cast<VkPresentRegionsKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_shared_presentable_image
        case VK_STRUCTURE_TYPE_SHARED_PRESENT_SURFACE_CAPABILITIES_KHR:
        {
            transform_fromhost_VkSharedPresentSurfaceCapabilitiesKHR(resourceTracker, reinterpret_cast<VkSharedPresentSurfaceCapabilitiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_fence_win32
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_WIN32_HANDLE_INFO_KHR:
        {
            transform_fromhost_VkExportFenceWin32HandleInfoKHR(resourceTracker, reinterpret_cast<VkExportFenceWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_image_format_list
        case VK_STRUCTURE_TYPE_IMAGE_FORMAT_LIST_CREATE_INFO_KHR:
        {
            transform_fromhost_VkImageFormatListCreateInfoKHR(resourceTracker, reinterpret_cast<VkImageFormatListCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_8bit_storage
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_8BIT_STORAGE_FEATURES_KHR:
        {
            transform_fromhost_VkPhysicalDevice8BitStorageFeaturesKHR(resourceTracker, reinterpret_cast<VkPhysicalDevice8BitStorageFeaturesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_native_buffer
        case VK_STRUCTURE_TYPE_NATIVE_BUFFER_ANDROID:
        {
            transform_fromhost_VkNativeBufferANDROID(resourceTracker, reinterpret_cast<VkNativeBufferANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_report
        case VK_STRUCTURE_TYPE_DEBUG_REPORT_CALLBACK_CREATE_INFO_EXT:
        {
            transform_fromhost_VkDebugReportCallbackCreateInfoEXT(resourceTracker, reinterpret_cast<VkDebugReportCallbackCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_rasterization_order
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_RASTERIZATION_ORDER_AMD:
        {
            transform_fromhost_VkPipelineRasterizationStateRasterizationOrderAMD(resourceTracker, reinterpret_cast<VkPipelineRasterizationStateRasterizationOrderAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_dedicated_allocation
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_IMAGE_CREATE_INFO_NV:
        {
            transform_fromhost_VkDedicatedAllocationImageCreateInfoNV(resourceTracker, reinterpret_cast<VkDedicatedAllocationImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_BUFFER_CREATE_INFO_NV:
        {
            transform_fromhost_VkDedicatedAllocationBufferCreateInfoNV(resourceTracker, reinterpret_cast<VkDedicatedAllocationBufferCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_MEMORY_ALLOCATE_INFO_NV:
        {
            transform_fromhost_VkDedicatedAllocationMemoryAllocateInfoNV(resourceTracker, reinterpret_cast<VkDedicatedAllocationMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_texture_gather_bias_lod
        case VK_STRUCTURE_TYPE_TEXTURE_LOD_GATHER_FORMAT_PROPERTIES_AMD:
        {
            transform_fromhost_VkTextureLODGatherFormatPropertiesAMD(resourceTracker, reinterpret_cast<VkTextureLODGatherFormatPropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO_NV:
        {
            transform_fromhost_VkExternalMemoryImageCreateInfoNV(resourceTracker, reinterpret_cast<VkExternalMemoryImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO_NV:
        {
            transform_fromhost_VkExportMemoryAllocateInfoNV(resourceTracker, reinterpret_cast<VkExportMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            transform_fromhost_VkImportMemoryWin32HandleInfoNV(resourceTracker, reinterpret_cast<VkImportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            transform_fromhost_VkExportMemoryWin32HandleInfoNV(resourceTracker, reinterpret_cast<VkExportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_NV:
        {
            transform_fromhost_VkWin32KeyedMutexAcquireReleaseInfoNV(resourceTracker, reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_flags
        case VK_STRUCTURE_TYPE_VALIDATION_FLAGS_EXT:
        {
            transform_fromhost_VkValidationFlagsEXT(resourceTracker, reinterpret_cast<VkValidationFlagsEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conditional_rendering
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONDITIONAL_RENDERING_FEATURES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceConditionalRenderingFeaturesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceConditionalRenderingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_COMMAND_BUFFER_INHERITANCE_CONDITIONAL_RENDERING_INFO_EXT:
        {
            transform_fromhost_VkCommandBufferInheritanceConditionalRenderingInfoEXT(resourceTracker, reinterpret_cast<VkCommandBufferInheritanceConditionalRenderingInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_clip_space_w_scaling
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_W_SCALING_STATE_CREATE_INFO_NV:
        {
            transform_fromhost_VkPipelineViewportWScalingStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineViewportWScalingStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_display_control
        case VK_STRUCTURE_TYPE_SWAPCHAIN_COUNTER_CREATE_INFO_EXT:
        {
            transform_fromhost_VkSwapchainCounterCreateInfoEXT(resourceTracker, reinterpret_cast<VkSwapchainCounterCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_display_timing
        case VK_STRUCTURE_TYPE_PRESENT_TIMES_INFO_GOOGLE:
        {
            transform_fromhost_VkPresentTimesInfoGOOGLE(resourceTracker, reinterpret_cast<VkPresentTimesInfoGOOGLE*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NVX_multiview_per_view_attributes
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PER_VIEW_ATTRIBUTES_PROPERTIES_NVX:
        {
            transform_fromhost_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(resourceTracker, reinterpret_cast<VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_viewport_swizzle
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_SWIZZLE_STATE_CREATE_INFO_NV:
        {
            transform_fromhost_VkPipelineViewportSwizzleStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineViewportSwizzleStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_discard_rectangles
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DISCARD_RECTANGLE_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceDiscardRectanglePropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceDiscardRectanglePropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_DISCARD_RECTANGLE_STATE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkPipelineDiscardRectangleStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineDiscardRectangleStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conservative_rasterization
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONSERVATIVE_RASTERIZATION_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceConservativeRasterizationPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_CONSERVATIVE_STATE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkPipelineRasterizationConservativeStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineRasterizationConservativeStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_utils
        case VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT:
        {
            transform_fromhost_VkDebugUtilsMessengerCreateInfoEXT(resourceTracker, reinterpret_cast<VkDebugUtilsMessengerCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_USAGE_ANDROID:
        {
            transform_fromhost_VkAndroidHardwareBufferUsageANDROID(resourceTracker, reinterpret_cast<VkAndroidHardwareBufferUsageANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_FORMAT_PROPERTIES_ANDROID:
        {
            transform_fromhost_VkAndroidHardwareBufferFormatPropertiesANDROID(resourceTracker, reinterpret_cast<VkAndroidHardwareBufferFormatPropertiesANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_ANDROID_HARDWARE_BUFFER_INFO_ANDROID:
        {
            transform_fromhost_VkImportAndroidHardwareBufferInfoANDROID(resourceTracker, reinterpret_cast<VkImportAndroidHardwareBufferInfoANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_FORMAT_ANDROID:
        {
            transform_fromhost_VkExternalFormatANDROID(resourceTracker, reinterpret_cast<VkExternalFormatANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sampler_filter_minmax
        case VK_STRUCTURE_TYPE_SAMPLER_REDUCTION_MODE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkSamplerReductionModeCreateInfoEXT(resourceTracker, reinterpret_cast<VkSamplerReductionModeCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_FILTER_MINMAX_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sample_locations
        case VK_STRUCTURE_TYPE_SAMPLE_LOCATIONS_INFO_EXT:
        {
            transform_fromhost_VkSampleLocationsInfoEXT(resourceTracker, reinterpret_cast<VkSampleLocationsInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_SAMPLE_LOCATIONS_BEGIN_INFO_EXT:
        {
            transform_fromhost_VkRenderPassSampleLocationsBeginInfoEXT(resourceTracker, reinterpret_cast<VkRenderPassSampleLocationsBeginInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_SAMPLE_LOCATIONS_STATE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkPipelineSampleLocationsStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineSampleLocationsStateCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLE_LOCATIONS_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceSampleLocationsPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceSampleLocationsPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_blend_operation_advanced
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_FEATURES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_ADVANCED_STATE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkPipelineColorBlendAdvancedStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineColorBlendAdvancedStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_fragment_coverage_to_color
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_TO_COLOR_STATE_CREATE_INFO_NV:
        {
            transform_fromhost_VkPipelineCoverageToColorStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineCoverageToColorStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_framebuffer_mixed_samples
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_MODULATION_STATE_CREATE_INFO_NV:
        {
            transform_fromhost_VkPipelineCoverageModulationStateCreateInfoNV(resourceTracker, reinterpret_cast<VkPipelineCoverageModulationStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_cache
        case VK_STRUCTURE_TYPE_SHADER_MODULE_VALIDATION_CACHE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkShaderModuleValidationCacheCreateInfoEXT(resourceTracker, reinterpret_cast<VkShaderModuleValidationCacheCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_descriptor_indexing
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_BINDING_FLAGS_CREATE_INFO_EXT:
        {
            transform_fromhost_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(resourceTracker, reinterpret_cast<VkDescriptorSetLayoutBindingFlagsCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_FEATURES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceDescriptorIndexingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceDescriptorIndexingPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_ALLOCATE_INFO_EXT:
        {
            transform_fromhost_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(resourceTracker, reinterpret_cast<VkDescriptorSetVariableDescriptorCountAllocateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_LAYOUT_SUPPORT_EXT:
        {
            transform_fromhost_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(resourceTracker, reinterpret_cast<VkDescriptorSetVariableDescriptorCountLayoutSupportEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_global_priority
        case VK_STRUCTURE_TYPE_DEVICE_QUEUE_GLOBAL_PRIORITY_CREATE_INFO_EXT:
        {
            transform_fromhost_VkDeviceQueueGlobalPriorityCreateInfoEXT(resourceTracker, reinterpret_cast<VkDeviceQueueGlobalPriorityCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_external_memory_host
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_HOST_POINTER_INFO_EXT:
        {
            transform_fromhost_VkImportMemoryHostPointerInfoEXT(resourceTracker, reinterpret_cast<VkImportMemoryHostPointerInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_MEMORY_HOST_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceExternalMemoryHostPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_shader_core_properties
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_CORE_PROPERTIES_AMD:
        {
            transform_fromhost_VkPhysicalDeviceShaderCorePropertiesAMD(resourceTracker, reinterpret_cast<VkPhysicalDeviceShaderCorePropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_vertex_attribute_divisor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VERTEX_ATTRIBUTE_DIVISOR_PROPERTIES_EXT:
        {
            transform_fromhost_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(resourceTracker, reinterpret_cast<VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_DIVISOR_STATE_CREATE_INFO_EXT:
        {
            transform_fromhost_VkPipelineVertexInputDivisorStateCreateInfoEXT(resourceTracker, reinterpret_cast<VkPipelineVertexInputDivisorStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
        case VK_STRUCTURE_TYPE_QUEUE_FAMILY_CHECKPOINT_PROPERTIES_NV:
        {
            transform_fromhost_VkQueueFamilyCheckpointPropertiesNV(resourceTracker, reinterpret_cast<VkQueueFamilyCheckpointPropertiesNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_color_buffer
        case VK_STRUCTURE_TYPE_IMPORT_COLOR_BUFFER_GOOGLE:
        {
            transform_fromhost_VkImportColorBufferGOOGLE(resourceTracker, reinterpret_cast<VkImportColorBufferGOOGLE*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_PHYSICAL_ADDRESS_GOOGLE:
        {
            transform_fromhost_VkImportPhysicalAddressGOOGLE(resourceTracker, reinterpret_cast<VkImportPhysicalAddressGOOGLE*>(structExtension_out));
            break;
        }
#endif
        default:
        {
            return;
        }
    }
}


} // namespace goldfish_vk
