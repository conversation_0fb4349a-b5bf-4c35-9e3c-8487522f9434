// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_transform_guest
// (header) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#pragma once

#include <vulkan/vulkan.h>


#include "vk_platform_compat.h"

#include "goldfish_vk_private_defs.h"


namespace goldfish_vk {

class ResourceTracker;
#define LIST_TRANSFORMED_TYPES(f) \
f(VkExternalMemoryProperties) \
f(VkPhysicalDeviceExternalImageFormatInfo) \
f(VkPhysicalDeviceExternalBufferInfo) \
f(VkExternalMemoryImageCreateInfo) \
f(VkExternalMemoryBufferCreateInfo) \
f(VkExportMemoryAllocateInfo) \
f(VkExternalImageFormatProperties) \
f(VkExternalBufferProperties) \

#ifdef VK_VERSION_1_0
void transform_tohost_VkApplicationInfo(
    ResourceTracker* resourceTracker,
    VkApplicationInfo* toTransform);

void transform_fromhost_VkApplicationInfo(
    ResourceTracker* resourceTracker,
    VkApplicationInfo* toTransform);

void transform_tohost_VkInstanceCreateInfo(
    ResourceTracker* resourceTracker,
    VkInstanceCreateInfo* toTransform);

void transform_fromhost_VkInstanceCreateInfo(
    ResourceTracker* resourceTracker,
    VkInstanceCreateInfo* toTransform);

void transform_tohost_VkAllocationCallbacks(
    ResourceTracker* resourceTracker,
    VkAllocationCallbacks* toTransform);

void transform_fromhost_VkAllocationCallbacks(
    ResourceTracker* resourceTracker,
    VkAllocationCallbacks* toTransform);

void transform_tohost_VkPhysicalDeviceFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures* toTransform);

void transform_fromhost_VkPhysicalDeviceFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures* toTransform);

void transform_tohost_VkFormatProperties(
    ResourceTracker* resourceTracker,
    VkFormatProperties* toTransform);

void transform_fromhost_VkFormatProperties(
    ResourceTracker* resourceTracker,
    VkFormatProperties* toTransform);

void transform_tohost_VkExtent3D(
    ResourceTracker* resourceTracker,
    VkExtent3D* toTransform);

void transform_fromhost_VkExtent3D(
    ResourceTracker* resourceTracker,
    VkExtent3D* toTransform);

void transform_tohost_VkImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties* toTransform);

void transform_fromhost_VkImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties* toTransform);

void transform_tohost_VkPhysicalDeviceLimits(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceLimits* toTransform);

void transform_fromhost_VkPhysicalDeviceLimits(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceLimits* toTransform);

void transform_tohost_VkPhysicalDeviceSparseProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceSparseProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseProperties* toTransform);

void transform_tohost_VkPhysicalDeviceProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties* toTransform);

void transform_tohost_VkQueueFamilyProperties(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties* toTransform);

void transform_fromhost_VkQueueFamilyProperties(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties* toTransform);

void transform_tohost_VkMemoryType(
    ResourceTracker* resourceTracker,
    VkMemoryType* toTransform);

void transform_fromhost_VkMemoryType(
    ResourceTracker* resourceTracker,
    VkMemoryType* toTransform);

void transform_tohost_VkMemoryHeap(
    ResourceTracker* resourceTracker,
    VkMemoryHeap* toTransform);

void transform_fromhost_VkMemoryHeap(
    ResourceTracker* resourceTracker,
    VkMemoryHeap* toTransform);

void transform_tohost_VkPhysicalDeviceMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties* toTransform);

void transform_tohost_VkDeviceQueueCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceQueueCreateInfo* toTransform);

void transform_fromhost_VkDeviceQueueCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceQueueCreateInfo* toTransform);

void transform_tohost_VkDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceCreateInfo* toTransform);

void transform_fromhost_VkDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceCreateInfo* toTransform);

void transform_tohost_VkExtensionProperties(
    ResourceTracker* resourceTracker,
    VkExtensionProperties* toTransform);

void transform_fromhost_VkExtensionProperties(
    ResourceTracker* resourceTracker,
    VkExtensionProperties* toTransform);

void transform_tohost_VkLayerProperties(
    ResourceTracker* resourceTracker,
    VkLayerProperties* toTransform);

void transform_fromhost_VkLayerProperties(
    ResourceTracker* resourceTracker,
    VkLayerProperties* toTransform);

void transform_tohost_VkSubmitInfo(
    ResourceTracker* resourceTracker,
    VkSubmitInfo* toTransform);

void transform_fromhost_VkSubmitInfo(
    ResourceTracker* resourceTracker,
    VkSubmitInfo* toTransform);

void transform_tohost_VkMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateInfo* toTransform);

void transform_fromhost_VkMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateInfo* toTransform);

void transform_tohost_VkMappedMemoryRange(
    ResourceTracker* resourceTracker,
    VkMappedMemoryRange* toTransform);

void transform_fromhost_VkMappedMemoryRange(
    ResourceTracker* resourceTracker,
    VkMappedMemoryRange* toTransform);

void transform_tohost_VkMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements* toTransform);

void transform_fromhost_VkMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements* toTransform);

void transform_tohost_VkSparseImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties* toTransform);

void transform_fromhost_VkSparseImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties* toTransform);

void transform_tohost_VkSparseImageMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements* toTransform);

void transform_fromhost_VkSparseImageMemoryRequirements(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements* toTransform);

void transform_tohost_VkSparseMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseMemoryBind* toTransform);

void transform_fromhost_VkSparseMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseMemoryBind* toTransform);

void transform_tohost_VkSparseBufferMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseBufferMemoryBindInfo* toTransform);

void transform_fromhost_VkSparseBufferMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseBufferMemoryBindInfo* toTransform);

void transform_tohost_VkSparseImageOpaqueMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageOpaqueMemoryBindInfo* toTransform);

void transform_fromhost_VkSparseImageOpaqueMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageOpaqueMemoryBindInfo* toTransform);

void transform_tohost_VkImageSubresource(
    ResourceTracker* resourceTracker,
    VkImageSubresource* toTransform);

void transform_fromhost_VkImageSubresource(
    ResourceTracker* resourceTracker,
    VkImageSubresource* toTransform);

void transform_tohost_VkOffset3D(
    ResourceTracker* resourceTracker,
    VkOffset3D* toTransform);

void transform_fromhost_VkOffset3D(
    ResourceTracker* resourceTracker,
    VkOffset3D* toTransform);

void transform_tohost_VkSparseImageMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBind* toTransform);

void transform_fromhost_VkSparseImageMemoryBind(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBind* toTransform);

void transform_tohost_VkSparseImageMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBindInfo* toTransform);

void transform_fromhost_VkSparseImageMemoryBindInfo(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryBindInfo* toTransform);

void transform_tohost_VkBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkBindSparseInfo* toTransform);

void transform_fromhost_VkBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkBindSparseInfo* toTransform);

void transform_tohost_VkFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkFenceCreateInfo* toTransform);

void transform_fromhost_VkFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkFenceCreateInfo* toTransform);

void transform_tohost_VkSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkSemaphoreCreateInfo* toTransform);

void transform_fromhost_VkSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkSemaphoreCreateInfo* toTransform);

void transform_tohost_VkEventCreateInfo(
    ResourceTracker* resourceTracker,
    VkEventCreateInfo* toTransform);

void transform_fromhost_VkEventCreateInfo(
    ResourceTracker* resourceTracker,
    VkEventCreateInfo* toTransform);

void transform_tohost_VkQueryPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkQueryPoolCreateInfo* toTransform);

void transform_fromhost_VkQueryPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkQueryPoolCreateInfo* toTransform);

void transform_tohost_VkBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferCreateInfo* toTransform);

void transform_fromhost_VkBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferCreateInfo* toTransform);

void transform_tohost_VkBufferViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferViewCreateInfo* toTransform);

void transform_fromhost_VkBufferViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkBufferViewCreateInfo* toTransform);

void transform_tohost_VkImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageCreateInfo* toTransform);

void transform_fromhost_VkImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageCreateInfo* toTransform);

void transform_tohost_VkSubresourceLayout(
    ResourceTracker* resourceTracker,
    VkSubresourceLayout* toTransform);

void transform_fromhost_VkSubresourceLayout(
    ResourceTracker* resourceTracker,
    VkSubresourceLayout* toTransform);

void transform_tohost_VkComponentMapping(
    ResourceTracker* resourceTracker,
    VkComponentMapping* toTransform);

void transform_fromhost_VkComponentMapping(
    ResourceTracker* resourceTracker,
    VkComponentMapping* toTransform);

void transform_tohost_VkImageSubresourceRange(
    ResourceTracker* resourceTracker,
    VkImageSubresourceRange* toTransform);

void transform_fromhost_VkImageSubresourceRange(
    ResourceTracker* resourceTracker,
    VkImageSubresourceRange* toTransform);

void transform_tohost_VkImageViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewCreateInfo* toTransform);

void transform_fromhost_VkImageViewCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewCreateInfo* toTransform);

void transform_tohost_VkShaderModuleCreateInfo(
    ResourceTracker* resourceTracker,
    VkShaderModuleCreateInfo* toTransform);

void transform_fromhost_VkShaderModuleCreateInfo(
    ResourceTracker* resourceTracker,
    VkShaderModuleCreateInfo* toTransform);

void transform_tohost_VkPipelineCacheCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineCacheCreateInfo* toTransform);

void transform_fromhost_VkPipelineCacheCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineCacheCreateInfo* toTransform);

void transform_tohost_VkSpecializationMapEntry(
    ResourceTracker* resourceTracker,
    VkSpecializationMapEntry* toTransform);

void transform_fromhost_VkSpecializationMapEntry(
    ResourceTracker* resourceTracker,
    VkSpecializationMapEntry* toTransform);

void transform_tohost_VkSpecializationInfo(
    ResourceTracker* resourceTracker,
    VkSpecializationInfo* toTransform);

void transform_fromhost_VkSpecializationInfo(
    ResourceTracker* resourceTracker,
    VkSpecializationInfo* toTransform);

void transform_tohost_VkPipelineShaderStageCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineShaderStageCreateInfo* toTransform);

void transform_fromhost_VkPipelineShaderStageCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineShaderStageCreateInfo* toTransform);

void transform_tohost_VkVertexInputBindingDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDescription* toTransform);

void transform_fromhost_VkVertexInputBindingDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDescription* toTransform);

void transform_tohost_VkVertexInputAttributeDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputAttributeDescription* toTransform);

void transform_fromhost_VkVertexInputAttributeDescription(
    ResourceTracker* resourceTracker,
    VkVertexInputAttributeDescription* toTransform);

void transform_tohost_VkPipelineVertexInputStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineVertexInputStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputStateCreateInfo* toTransform);

void transform_tohost_VkPipelineInputAssemblyStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineInputAssemblyStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineInputAssemblyStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineInputAssemblyStateCreateInfo* toTransform);

void transform_tohost_VkPipelineTessellationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineTessellationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationStateCreateInfo* toTransform);

void transform_tohost_VkViewport(
    ResourceTracker* resourceTracker,
    VkViewport* toTransform);

void transform_fromhost_VkViewport(
    ResourceTracker* resourceTracker,
    VkViewport* toTransform);

void transform_tohost_VkOffset2D(
    ResourceTracker* resourceTracker,
    VkOffset2D* toTransform);

void transform_fromhost_VkOffset2D(
    ResourceTracker* resourceTracker,
    VkOffset2D* toTransform);

void transform_tohost_VkExtent2D(
    ResourceTracker* resourceTracker,
    VkExtent2D* toTransform);

void transform_fromhost_VkExtent2D(
    ResourceTracker* resourceTracker,
    VkExtent2D* toTransform);

void transform_tohost_VkRect2D(
    ResourceTracker* resourceTracker,
    VkRect2D* toTransform);

void transform_fromhost_VkRect2D(
    ResourceTracker* resourceTracker,
    VkRect2D* toTransform);

void transform_tohost_VkPipelineViewportStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineViewportStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineViewportStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineViewportStateCreateInfo* toTransform);

void transform_tohost_VkPipelineRasterizationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineRasterizationStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateCreateInfo* toTransform);

void transform_tohost_VkPipelineMultisampleStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineMultisampleStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineMultisampleStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineMultisampleStateCreateInfo* toTransform);

void transform_tohost_VkStencilOpState(
    ResourceTracker* resourceTracker,
    VkStencilOpState* toTransform);

void transform_fromhost_VkStencilOpState(
    ResourceTracker* resourceTracker,
    VkStencilOpState* toTransform);

void transform_tohost_VkPipelineDepthStencilStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDepthStencilStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineDepthStencilStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDepthStencilStateCreateInfo* toTransform);

void transform_tohost_VkPipelineColorBlendAttachmentState(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAttachmentState* toTransform);

void transform_fromhost_VkPipelineColorBlendAttachmentState(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAttachmentState* toTransform);

void transform_tohost_VkPipelineColorBlendStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineColorBlendStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendStateCreateInfo* toTransform);

void transform_tohost_VkPipelineDynamicStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDynamicStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineDynamicStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineDynamicStateCreateInfo* toTransform);

void transform_tohost_VkGraphicsPipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkGraphicsPipelineCreateInfo* toTransform);

void transform_fromhost_VkGraphicsPipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkGraphicsPipelineCreateInfo* toTransform);

void transform_tohost_VkComputePipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkComputePipelineCreateInfo* toTransform);

void transform_fromhost_VkComputePipelineCreateInfo(
    ResourceTracker* resourceTracker,
    VkComputePipelineCreateInfo* toTransform);

void transform_tohost_VkPushConstantRange(
    ResourceTracker* resourceTracker,
    VkPushConstantRange* toTransform);

void transform_fromhost_VkPushConstantRange(
    ResourceTracker* resourceTracker,
    VkPushConstantRange* toTransform);

void transform_tohost_VkPipelineLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineLayoutCreateInfo* toTransform);

void transform_fromhost_VkPipelineLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineLayoutCreateInfo* toTransform);

void transform_tohost_VkSamplerCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerCreateInfo* toTransform);

void transform_fromhost_VkSamplerCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerCreateInfo* toTransform);

void transform_tohost_VkDescriptorSetLayoutBinding(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBinding* toTransform);

void transform_fromhost_VkDescriptorSetLayoutBinding(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBinding* toTransform);

void transform_tohost_VkDescriptorSetLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutCreateInfo* toTransform);

void transform_fromhost_VkDescriptorSetLayoutCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutCreateInfo* toTransform);

void transform_tohost_VkDescriptorPoolSize(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolSize* toTransform);

void transform_fromhost_VkDescriptorPoolSize(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolSize* toTransform);

void transform_tohost_VkDescriptorPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolCreateInfo* toTransform);

void transform_fromhost_VkDescriptorPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorPoolCreateInfo* toTransform);

void transform_tohost_VkDescriptorSetAllocateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetAllocateInfo* toTransform);

void transform_fromhost_VkDescriptorSetAllocateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorSetAllocateInfo* toTransform);

void transform_tohost_VkDescriptorImageInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorImageInfo* toTransform);

void transform_fromhost_VkDescriptorImageInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorImageInfo* toTransform);

void transform_tohost_VkDescriptorBufferInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorBufferInfo* toTransform);

void transform_fromhost_VkDescriptorBufferInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorBufferInfo* toTransform);

void transform_tohost_VkWriteDescriptorSet(
    ResourceTracker* resourceTracker,
    VkWriteDescriptorSet* toTransform);

void transform_fromhost_VkWriteDescriptorSet(
    ResourceTracker* resourceTracker,
    VkWriteDescriptorSet* toTransform);

void transform_tohost_VkCopyDescriptorSet(
    ResourceTracker* resourceTracker,
    VkCopyDescriptorSet* toTransform);

void transform_fromhost_VkCopyDescriptorSet(
    ResourceTracker* resourceTracker,
    VkCopyDescriptorSet* toTransform);

void transform_tohost_VkFramebufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkFramebufferCreateInfo* toTransform);

void transform_fromhost_VkFramebufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkFramebufferCreateInfo* toTransform);

void transform_tohost_VkAttachmentDescription(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription* toTransform);

void transform_fromhost_VkAttachmentDescription(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription* toTransform);

void transform_tohost_VkAttachmentReference(
    ResourceTracker* resourceTracker,
    VkAttachmentReference* toTransform);

void transform_fromhost_VkAttachmentReference(
    ResourceTracker* resourceTracker,
    VkAttachmentReference* toTransform);

void transform_tohost_VkSubpassDescription(
    ResourceTracker* resourceTracker,
    VkSubpassDescription* toTransform);

void transform_fromhost_VkSubpassDescription(
    ResourceTracker* resourceTracker,
    VkSubpassDescription* toTransform);

void transform_tohost_VkSubpassDependency(
    ResourceTracker* resourceTracker,
    VkSubpassDependency* toTransform);

void transform_fromhost_VkSubpassDependency(
    ResourceTracker* resourceTracker,
    VkSubpassDependency* toTransform);

void transform_tohost_VkRenderPassCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo* toTransform);

void transform_fromhost_VkRenderPassCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo* toTransform);

void transform_tohost_VkCommandPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkCommandPoolCreateInfo* toTransform);

void transform_fromhost_VkCommandPoolCreateInfo(
    ResourceTracker* resourceTracker,
    VkCommandPoolCreateInfo* toTransform);

void transform_tohost_VkCommandBufferAllocateInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferAllocateInfo* toTransform);

void transform_fromhost_VkCommandBufferAllocateInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferAllocateInfo* toTransform);

void transform_tohost_VkCommandBufferInheritanceInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceInfo* toTransform);

void transform_fromhost_VkCommandBufferInheritanceInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceInfo* toTransform);

void transform_tohost_VkCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferBeginInfo* toTransform);

void transform_fromhost_VkCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkCommandBufferBeginInfo* toTransform);

void transform_tohost_VkBufferCopy(
    ResourceTracker* resourceTracker,
    VkBufferCopy* toTransform);

void transform_fromhost_VkBufferCopy(
    ResourceTracker* resourceTracker,
    VkBufferCopy* toTransform);

void transform_tohost_VkImageSubresourceLayers(
    ResourceTracker* resourceTracker,
    VkImageSubresourceLayers* toTransform);

void transform_fromhost_VkImageSubresourceLayers(
    ResourceTracker* resourceTracker,
    VkImageSubresourceLayers* toTransform);

void transform_tohost_VkImageCopy(
    ResourceTracker* resourceTracker,
    VkImageCopy* toTransform);

void transform_fromhost_VkImageCopy(
    ResourceTracker* resourceTracker,
    VkImageCopy* toTransform);

void transform_tohost_VkImageBlit(
    ResourceTracker* resourceTracker,
    VkImageBlit* toTransform);

void transform_fromhost_VkImageBlit(
    ResourceTracker* resourceTracker,
    VkImageBlit* toTransform);

void transform_tohost_VkBufferImageCopy(
    ResourceTracker* resourceTracker,
    VkBufferImageCopy* toTransform);

void transform_fromhost_VkBufferImageCopy(
    ResourceTracker* resourceTracker,
    VkBufferImageCopy* toTransform);

void transform_tohost_VkClearColorValue(
    ResourceTracker* resourceTracker,
    VkClearColorValue* toTransform);

void transform_fromhost_VkClearColorValue(
    ResourceTracker* resourceTracker,
    VkClearColorValue* toTransform);

void transform_tohost_VkClearDepthStencilValue(
    ResourceTracker* resourceTracker,
    VkClearDepthStencilValue* toTransform);

void transform_fromhost_VkClearDepthStencilValue(
    ResourceTracker* resourceTracker,
    VkClearDepthStencilValue* toTransform);

void transform_tohost_VkClearValue(
    ResourceTracker* resourceTracker,
    VkClearValue* toTransform);

void transform_fromhost_VkClearValue(
    ResourceTracker* resourceTracker,
    VkClearValue* toTransform);

void transform_tohost_VkClearAttachment(
    ResourceTracker* resourceTracker,
    VkClearAttachment* toTransform);

void transform_fromhost_VkClearAttachment(
    ResourceTracker* resourceTracker,
    VkClearAttachment* toTransform);

void transform_tohost_VkClearRect(
    ResourceTracker* resourceTracker,
    VkClearRect* toTransform);

void transform_fromhost_VkClearRect(
    ResourceTracker* resourceTracker,
    VkClearRect* toTransform);

void transform_tohost_VkImageResolve(
    ResourceTracker* resourceTracker,
    VkImageResolve* toTransform);

void transform_fromhost_VkImageResolve(
    ResourceTracker* resourceTracker,
    VkImageResolve* toTransform);

void transform_tohost_VkMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkMemoryBarrier* toTransform);

void transform_fromhost_VkMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkMemoryBarrier* toTransform);

void transform_tohost_VkBufferMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkBufferMemoryBarrier* toTransform);

void transform_fromhost_VkBufferMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkBufferMemoryBarrier* toTransform);

void transform_tohost_VkImageMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkImageMemoryBarrier* toTransform);

void transform_fromhost_VkImageMemoryBarrier(
    ResourceTracker* resourceTracker,
    VkImageMemoryBarrier* toTransform);

void transform_tohost_VkRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassBeginInfo* toTransform);

void transform_fromhost_VkRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassBeginInfo* toTransform);

void transform_tohost_VkDispatchIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDispatchIndirectCommand* toTransform);

void transform_fromhost_VkDispatchIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDispatchIndirectCommand* toTransform);

void transform_tohost_VkDrawIndexedIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndexedIndirectCommand* toTransform);

void transform_fromhost_VkDrawIndexedIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndexedIndirectCommand* toTransform);

void transform_tohost_VkDrawIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndirectCommand* toTransform);

void transform_fromhost_VkDrawIndirectCommand(
    ResourceTracker* resourceTracker,
    VkDrawIndirectCommand* toTransform);

void transform_tohost_VkBaseOutStructure(
    ResourceTracker* resourceTracker,
    VkBaseOutStructure* toTransform);

void transform_fromhost_VkBaseOutStructure(
    ResourceTracker* resourceTracker,
    VkBaseOutStructure* toTransform);

void transform_tohost_VkBaseInStructure(
    ResourceTracker* resourceTracker,
    VkBaseInStructure* toTransform);

void transform_fromhost_VkBaseInStructure(
    ResourceTracker* resourceTracker,
    VkBaseInStructure* toTransform);

#endif
#ifdef VK_VERSION_1_1
void transform_tohost_VkPhysicalDeviceSubgroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSubgroupProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceSubgroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSubgroupProperties* toTransform);

void transform_tohost_VkBindBufferMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryInfo* toTransform);

void transform_fromhost_VkBindBufferMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryInfo* toTransform);

void transform_tohost_VkBindImageMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryInfo* toTransform);

void transform_fromhost_VkBindImageMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryInfo* toTransform);

void transform_tohost_VkPhysicalDevice16BitStorageFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice16BitStorageFeatures* toTransform);

void transform_fromhost_VkPhysicalDevice16BitStorageFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice16BitStorageFeatures* toTransform);

void transform_tohost_VkMemoryDedicatedRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedRequirements* toTransform);

void transform_fromhost_VkMemoryDedicatedRequirements(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedRequirements* toTransform);

void transform_tohost_VkMemoryDedicatedAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedAllocateInfo* toTransform);

void transform_fromhost_VkMemoryDedicatedAllocateInfo(
    ResourceTracker* resourceTracker,
    VkMemoryDedicatedAllocateInfo* toTransform);

void transform_tohost_VkMemoryAllocateFlagsInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateFlagsInfo* toTransform);

void transform_fromhost_VkMemoryAllocateFlagsInfo(
    ResourceTracker* resourceTracker,
    VkMemoryAllocateFlagsInfo* toTransform);

void transform_tohost_VkDeviceGroupRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupRenderPassBeginInfo* toTransform);

void transform_fromhost_VkDeviceGroupRenderPassBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupRenderPassBeginInfo* toTransform);

void transform_tohost_VkDeviceGroupCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupCommandBufferBeginInfo* toTransform);

void transform_fromhost_VkDeviceGroupCommandBufferBeginInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupCommandBufferBeginInfo* toTransform);

void transform_tohost_VkDeviceGroupSubmitInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSubmitInfo* toTransform);

void transform_fromhost_VkDeviceGroupSubmitInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSubmitInfo* toTransform);

void transform_tohost_VkDeviceGroupBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupBindSparseInfo* toTransform);

void transform_fromhost_VkDeviceGroupBindSparseInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupBindSparseInfo* toTransform);

void transform_tohost_VkBindBufferMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryDeviceGroupInfo* toTransform);

void transform_fromhost_VkBindBufferMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindBufferMemoryDeviceGroupInfo* toTransform);

void transform_tohost_VkBindImageMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryDeviceGroupInfo* toTransform);

void transform_fromhost_VkBindImageMemoryDeviceGroupInfo(
    ResourceTracker* resourceTracker,
    VkBindImageMemoryDeviceGroupInfo* toTransform);

void transform_tohost_VkPhysicalDeviceGroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceGroupProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceGroupProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceGroupProperties* toTransform);

void transform_tohost_VkDeviceGroupDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupDeviceCreateInfo* toTransform);

void transform_fromhost_VkDeviceGroupDeviceCreateInfo(
    ResourceTracker* resourceTracker,
    VkDeviceGroupDeviceCreateInfo* toTransform);

void transform_tohost_VkBufferMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkBufferMemoryRequirementsInfo2* toTransform);

void transform_fromhost_VkBufferMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkBufferMemoryRequirementsInfo2* toTransform);

void transform_tohost_VkImageMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageMemoryRequirementsInfo2* toTransform);

void transform_fromhost_VkImageMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageMemoryRequirementsInfo2* toTransform);

void transform_tohost_VkImageSparseMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageSparseMemoryRequirementsInfo2* toTransform);

void transform_fromhost_VkImageSparseMemoryRequirementsInfo2(
    ResourceTracker* resourceTracker,
    VkImageSparseMemoryRequirementsInfo2* toTransform);

void transform_tohost_VkMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements2* toTransform);

void transform_fromhost_VkMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkMemoryRequirements2* toTransform);

void transform_tohost_VkSparseImageMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements2* toTransform);

void transform_fromhost_VkSparseImageMemoryRequirements2(
    ResourceTracker* resourceTracker,
    VkSparseImageMemoryRequirements2* toTransform);

void transform_tohost_VkPhysicalDeviceFeatures2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures2* toTransform);

void transform_fromhost_VkPhysicalDeviceFeatures2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceFeatures2* toTransform);

void transform_tohost_VkPhysicalDeviceProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties2* toTransform);

void transform_fromhost_VkPhysicalDeviceProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProperties2* toTransform);

void transform_tohost_VkFormatProperties2(
    ResourceTracker* resourceTracker,
    VkFormatProperties2* toTransform);

void transform_fromhost_VkFormatProperties2(
    ResourceTracker* resourceTracker,
    VkFormatProperties2* toTransform);

void transform_tohost_VkImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties2* toTransform);

void transform_fromhost_VkImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkImageFormatProperties2* toTransform);

void transform_tohost_VkPhysicalDeviceImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceImageFormatInfo2* toTransform);

void transform_fromhost_VkPhysicalDeviceImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceImageFormatInfo2* toTransform);

void transform_tohost_VkQueueFamilyProperties2(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties2* toTransform);

void transform_fromhost_VkQueueFamilyProperties2(
    ResourceTracker* resourceTracker,
    VkQueueFamilyProperties2* toTransform);

void transform_tohost_VkPhysicalDeviceMemoryProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties2* toTransform);

void transform_fromhost_VkPhysicalDeviceMemoryProperties2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMemoryProperties2* toTransform);

void transform_tohost_VkSparseImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties2* toTransform);

void transform_fromhost_VkSparseImageFormatProperties2(
    ResourceTracker* resourceTracker,
    VkSparseImageFormatProperties2* toTransform);

void transform_tohost_VkPhysicalDeviceSparseImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseImageFormatInfo2* toTransform);

void transform_fromhost_VkPhysicalDeviceSparseImageFormatInfo2(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSparseImageFormatInfo2* toTransform);

void transform_tohost_VkPhysicalDevicePointClippingProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePointClippingProperties* toTransform);

void transform_fromhost_VkPhysicalDevicePointClippingProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePointClippingProperties* toTransform);

void transform_tohost_VkInputAttachmentAspectReference(
    ResourceTracker* resourceTracker,
    VkInputAttachmentAspectReference* toTransform);

void transform_fromhost_VkInputAttachmentAspectReference(
    ResourceTracker* resourceTracker,
    VkInputAttachmentAspectReference* toTransform);

void transform_tohost_VkRenderPassInputAttachmentAspectCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassInputAttachmentAspectCreateInfo* toTransform);

void transform_fromhost_VkRenderPassInputAttachmentAspectCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassInputAttachmentAspectCreateInfo* toTransform);

void transform_tohost_VkImageViewUsageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewUsageCreateInfo* toTransform);

void transform_fromhost_VkImageViewUsageCreateInfo(
    ResourceTracker* resourceTracker,
    VkImageViewUsageCreateInfo* toTransform);

void transform_tohost_VkPipelineTessellationDomainOriginStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationDomainOriginStateCreateInfo* toTransform);

void transform_fromhost_VkPipelineTessellationDomainOriginStateCreateInfo(
    ResourceTracker* resourceTracker,
    VkPipelineTessellationDomainOriginStateCreateInfo* toTransform);

void transform_tohost_VkRenderPassMultiviewCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassMultiviewCreateInfo* toTransform);

void transform_fromhost_VkRenderPassMultiviewCreateInfo(
    ResourceTracker* resourceTracker,
    VkRenderPassMultiviewCreateInfo* toTransform);

void transform_tohost_VkPhysicalDeviceMultiviewFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewFeatures* toTransform);

void transform_fromhost_VkPhysicalDeviceMultiviewFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewFeatures* toTransform);

void transform_tohost_VkPhysicalDeviceMultiviewProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceMultiviewProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewProperties* toTransform);

void transform_tohost_VkPhysicalDeviceVariablePointerFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVariablePointerFeatures* toTransform);

void transform_fromhost_VkPhysicalDeviceVariablePointerFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVariablePointerFeatures* toTransform);

void transform_tohost_VkPhysicalDeviceProtectedMemoryFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryFeatures* toTransform);

void transform_fromhost_VkPhysicalDeviceProtectedMemoryFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryFeatures* toTransform);

void transform_tohost_VkPhysicalDeviceProtectedMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceProtectedMemoryProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceProtectedMemoryProperties* toTransform);

void transform_tohost_VkDeviceQueueInfo2(
    ResourceTracker* resourceTracker,
    VkDeviceQueueInfo2* toTransform);

void transform_fromhost_VkDeviceQueueInfo2(
    ResourceTracker* resourceTracker,
    VkDeviceQueueInfo2* toTransform);

void transform_tohost_VkProtectedSubmitInfo(
    ResourceTracker* resourceTracker,
    VkProtectedSubmitInfo* toTransform);

void transform_fromhost_VkProtectedSubmitInfo(
    ResourceTracker* resourceTracker,
    VkProtectedSubmitInfo* toTransform);

void transform_tohost_VkSamplerYcbcrConversionCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionCreateInfo* toTransform);

void transform_fromhost_VkSamplerYcbcrConversionCreateInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionCreateInfo* toTransform);

void transform_tohost_VkSamplerYcbcrConversionInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionInfo* toTransform);

void transform_fromhost_VkSamplerYcbcrConversionInfo(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionInfo* toTransform);

void transform_tohost_VkBindImagePlaneMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImagePlaneMemoryInfo* toTransform);

void transform_fromhost_VkBindImagePlaneMemoryInfo(
    ResourceTracker* resourceTracker,
    VkBindImagePlaneMemoryInfo* toTransform);

void transform_tohost_VkImagePlaneMemoryRequirementsInfo(
    ResourceTracker* resourceTracker,
    VkImagePlaneMemoryRequirementsInfo* toTransform);

void transform_fromhost_VkImagePlaneMemoryRequirementsInfo(
    ResourceTracker* resourceTracker,
    VkImagePlaneMemoryRequirementsInfo* toTransform);

void transform_tohost_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* toTransform);

void transform_fromhost_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* toTransform);

void transform_tohost_VkSamplerYcbcrConversionImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionImageFormatProperties* toTransform);

void transform_fromhost_VkSamplerYcbcrConversionImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkSamplerYcbcrConversionImageFormatProperties* toTransform);

void transform_tohost_VkDescriptorUpdateTemplateEntry(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateEntry* toTransform);

void transform_fromhost_VkDescriptorUpdateTemplateEntry(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateEntry* toTransform);

void transform_tohost_VkDescriptorUpdateTemplateCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateCreateInfo* toTransform);

void transform_fromhost_VkDescriptorUpdateTemplateCreateInfo(
    ResourceTracker* resourceTracker,
    VkDescriptorUpdateTemplateCreateInfo* toTransform);

void transform_tohost_VkExternalMemoryProperties(
    ResourceTracker* resourceTracker,
    VkExternalMemoryProperties* toTransform);

void transform_fromhost_VkExternalMemoryProperties(
    ResourceTracker* resourceTracker,
    VkExternalMemoryProperties* toTransform);

void transform_tohost_VkPhysicalDeviceExternalImageFormatInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalImageFormatInfo* toTransform);

void transform_fromhost_VkPhysicalDeviceExternalImageFormatInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalImageFormatInfo* toTransform);

void transform_tohost_VkExternalImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatProperties* toTransform);

void transform_fromhost_VkExternalImageFormatProperties(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatProperties* toTransform);

void transform_tohost_VkPhysicalDeviceExternalBufferInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalBufferInfo* toTransform);

void transform_fromhost_VkPhysicalDeviceExternalBufferInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalBufferInfo* toTransform);

void transform_tohost_VkExternalBufferProperties(
    ResourceTracker* resourceTracker,
    VkExternalBufferProperties* toTransform);

void transform_fromhost_VkExternalBufferProperties(
    ResourceTracker* resourceTracker,
    VkExternalBufferProperties* toTransform);

void transform_tohost_VkPhysicalDeviceIDProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceIDProperties* toTransform);

void transform_fromhost_VkPhysicalDeviceIDProperties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceIDProperties* toTransform);

void transform_tohost_VkExternalMemoryImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfo* toTransform);

void transform_fromhost_VkExternalMemoryImageCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfo* toTransform);

void transform_tohost_VkExternalMemoryBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryBufferCreateInfo* toTransform);

void transform_fromhost_VkExternalMemoryBufferCreateInfo(
    ResourceTracker* resourceTracker,
    VkExternalMemoryBufferCreateInfo* toTransform);

void transform_tohost_VkExportMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfo* toTransform);

void transform_fromhost_VkExportMemoryAllocateInfo(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfo* toTransform);

void transform_tohost_VkPhysicalDeviceExternalFenceInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalFenceInfo* toTransform);

void transform_fromhost_VkPhysicalDeviceExternalFenceInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalFenceInfo* toTransform);

void transform_tohost_VkExternalFenceProperties(
    ResourceTracker* resourceTracker,
    VkExternalFenceProperties* toTransform);

void transform_fromhost_VkExternalFenceProperties(
    ResourceTracker* resourceTracker,
    VkExternalFenceProperties* toTransform);

void transform_tohost_VkExportFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportFenceCreateInfo* toTransform);

void transform_fromhost_VkExportFenceCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportFenceCreateInfo* toTransform);

void transform_tohost_VkExportSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreCreateInfo* toTransform);

void transform_fromhost_VkExportSemaphoreCreateInfo(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreCreateInfo* toTransform);

void transform_tohost_VkPhysicalDeviceExternalSemaphoreInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalSemaphoreInfo* toTransform);

void transform_fromhost_VkPhysicalDeviceExternalSemaphoreInfo(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalSemaphoreInfo* toTransform);

void transform_tohost_VkExternalSemaphoreProperties(
    ResourceTracker* resourceTracker,
    VkExternalSemaphoreProperties* toTransform);

void transform_fromhost_VkExternalSemaphoreProperties(
    ResourceTracker* resourceTracker,
    VkExternalSemaphoreProperties* toTransform);

void transform_tohost_VkPhysicalDeviceMaintenance3Properties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMaintenance3Properties* toTransform);

void transform_fromhost_VkPhysicalDeviceMaintenance3Properties(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMaintenance3Properties* toTransform);

void transform_tohost_VkDescriptorSetLayoutSupport(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutSupport* toTransform);

void transform_fromhost_VkDescriptorSetLayoutSupport(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutSupport* toTransform);

void transform_tohost_VkPhysicalDeviceShaderDrawParameterFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderDrawParameterFeatures* toTransform);

void transform_fromhost_VkPhysicalDeviceShaderDrawParameterFeatures(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderDrawParameterFeatures* toTransform);

#endif
#ifdef VK_KHR_surface
void transform_tohost_VkSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilitiesKHR* toTransform);

void transform_fromhost_VkSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilitiesKHR* toTransform);

void transform_tohost_VkSurfaceFormatKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormatKHR* toTransform);

void transform_fromhost_VkSurfaceFormatKHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormatKHR* toTransform);

#endif
#ifdef VK_KHR_swapchain
void transform_tohost_VkSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkSwapchainCreateInfoKHR* toTransform);

void transform_fromhost_VkSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkSwapchainCreateInfoKHR* toTransform);

void transform_tohost_VkPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkPresentInfoKHR* toTransform);

void transform_fromhost_VkPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkPresentInfoKHR* toTransform);

void transform_tohost_VkImageSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageSwapchainCreateInfoKHR* toTransform);

void transform_fromhost_VkImageSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageSwapchainCreateInfoKHR* toTransform);

void transform_tohost_VkBindImageMemorySwapchainInfoKHR(
    ResourceTracker* resourceTracker,
    VkBindImageMemorySwapchainInfoKHR* toTransform);

void transform_fromhost_VkBindImageMemorySwapchainInfoKHR(
    ResourceTracker* resourceTracker,
    VkBindImageMemorySwapchainInfoKHR* toTransform);

void transform_tohost_VkAcquireNextImageInfoKHR(
    ResourceTracker* resourceTracker,
    VkAcquireNextImageInfoKHR* toTransform);

void transform_fromhost_VkAcquireNextImageInfoKHR(
    ResourceTracker* resourceTracker,
    VkAcquireNextImageInfoKHR* toTransform);

void transform_tohost_VkDeviceGroupPresentCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentCapabilitiesKHR* toTransform);

void transform_fromhost_VkDeviceGroupPresentCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentCapabilitiesKHR* toTransform);

void transform_tohost_VkDeviceGroupPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentInfoKHR* toTransform);

void transform_fromhost_VkDeviceGroupPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupPresentInfoKHR* toTransform);

void transform_tohost_VkDeviceGroupSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSwapchainCreateInfoKHR* toTransform);

void transform_fromhost_VkDeviceGroupSwapchainCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDeviceGroupSwapchainCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_display
void transform_tohost_VkDisplayPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPropertiesKHR* toTransform);

void transform_fromhost_VkDisplayPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPropertiesKHR* toTransform);

void transform_tohost_VkDisplayModeParametersKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeParametersKHR* toTransform);

void transform_fromhost_VkDisplayModeParametersKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeParametersKHR* toTransform);

void transform_tohost_VkDisplayModePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModePropertiesKHR* toTransform);

void transform_fromhost_VkDisplayModePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModePropertiesKHR* toTransform);

void transform_tohost_VkDisplayModeCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeCreateInfoKHR* toTransform);

void transform_fromhost_VkDisplayModeCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeCreateInfoKHR* toTransform);

void transform_tohost_VkDisplayPlaneCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilitiesKHR* toTransform);

void transform_fromhost_VkDisplayPlaneCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilitiesKHR* toTransform);

void transform_tohost_VkDisplayPlanePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlanePropertiesKHR* toTransform);

void transform_fromhost_VkDisplayPlanePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlanePropertiesKHR* toTransform);

void transform_tohost_VkDisplaySurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplaySurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkDisplaySurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplaySurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_display_swapchain
void transform_tohost_VkDisplayPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPresentInfoKHR* toTransform);

void transform_fromhost_VkDisplayPresentInfoKHR(
    ResourceTracker* resourceTracker,
    VkDisplayPresentInfoKHR* toTransform);

#endif
#ifdef VK_KHR_xlib_surface
void transform_tohost_VkXlibSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXlibSurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkXlibSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXlibSurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_xcb_surface
void transform_tohost_VkXcbSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXcbSurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkXcbSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkXcbSurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_wayland_surface
void transform_tohost_VkWaylandSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWaylandSurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkWaylandSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWaylandSurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_mir_surface
void transform_tohost_VkMirSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkMirSurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkMirSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkMirSurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_android_surface
void transform_tohost_VkAndroidSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkAndroidSurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkAndroidSurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkAndroidSurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_win32_surface
void transform_tohost_VkWin32SurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32SurfaceCreateInfoKHR* toTransform);

void transform_fromhost_VkWin32SurfaceCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32SurfaceCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#endif
#ifdef VK_KHR_device_group
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#endif
#ifdef VK_KHR_device_group_creation
#endif
#ifdef VK_KHR_external_memory_capabilities
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void transform_tohost_VkImportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkImportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoKHR* toTransform);

void transform_tohost_VkExportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkExportMemoryWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoKHR* toTransform);

void transform_tohost_VkMemoryWin32HandlePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryWin32HandlePropertiesKHR* toTransform);

void transform_fromhost_VkMemoryWin32HandlePropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryWin32HandlePropertiesKHR* toTransform);

void transform_tohost_VkMemoryGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkMemoryGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetWin32HandleInfoKHR* toTransform);

#endif
#ifdef VK_KHR_external_memory_fd
void transform_tohost_VkImportMemoryFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryFdInfoKHR* toTransform);

void transform_fromhost_VkImportMemoryFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportMemoryFdInfoKHR* toTransform);

void transform_tohost_VkMemoryFdPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryFdPropertiesKHR* toTransform);

void transform_fromhost_VkMemoryFdPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkMemoryFdPropertiesKHR* toTransform);

void transform_tohost_VkMemoryGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetFdInfoKHR* toTransform);

void transform_fromhost_VkMemoryGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkMemoryGetFdInfoKHR* toTransform);

#endif
#ifdef VK_KHR_win32_keyed_mutex
void transform_tohost_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* toTransform);

void transform_fromhost_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* toTransform);

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void transform_tohost_VkImportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkImportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreWin32HandleInfoKHR* toTransform);

void transform_tohost_VkExportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkExportSemaphoreWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportSemaphoreWin32HandleInfoKHR* toTransform);

void transform_tohost_VkD3D12FenceSubmitInfoKHR(
    ResourceTracker* resourceTracker,
    VkD3D12FenceSubmitInfoKHR* toTransform);

void transform_fromhost_VkD3D12FenceSubmitInfoKHR(
    ResourceTracker* resourceTracker,
    VkD3D12FenceSubmitInfoKHR* toTransform);

void transform_tohost_VkSemaphoreGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkSemaphoreGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetWin32HandleInfoKHR* toTransform);

#endif
#ifdef VK_KHR_external_semaphore_fd
void transform_tohost_VkImportSemaphoreFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreFdInfoKHR* toTransform);

void transform_fromhost_VkImportSemaphoreFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportSemaphoreFdInfoKHR* toTransform);

void transform_tohost_VkSemaphoreGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetFdInfoKHR* toTransform);

void transform_fromhost_VkSemaphoreGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkSemaphoreGetFdInfoKHR* toTransform);

#endif
#ifdef VK_KHR_push_descriptor
void transform_tohost_VkPhysicalDevicePushDescriptorPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePushDescriptorPropertiesKHR* toTransform);

void transform_fromhost_VkPhysicalDevicePushDescriptorPropertiesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevicePushDescriptorPropertiesKHR* toTransform);

#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void transform_tohost_VkRectLayerKHR(
    ResourceTracker* resourceTracker,
    VkRectLayerKHR* toTransform);

void transform_fromhost_VkRectLayerKHR(
    ResourceTracker* resourceTracker,
    VkRectLayerKHR* toTransform);

void transform_tohost_VkPresentRegionKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionKHR* toTransform);

void transform_fromhost_VkPresentRegionKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionKHR* toTransform);

void transform_tohost_VkPresentRegionsKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionsKHR* toTransform);

void transform_fromhost_VkPresentRegionsKHR(
    ResourceTracker* resourceTracker,
    VkPresentRegionsKHR* toTransform);

#endif
#ifdef VK_KHR_descriptor_update_template
#endif
#ifdef VK_KHR_create_renderpass2
void transform_tohost_VkAttachmentDescription2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription2KHR* toTransform);

void transform_fromhost_VkAttachmentDescription2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentDescription2KHR* toTransform);

void transform_tohost_VkAttachmentReference2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentReference2KHR* toTransform);

void transform_fromhost_VkAttachmentReference2KHR(
    ResourceTracker* resourceTracker,
    VkAttachmentReference2KHR* toTransform);

void transform_tohost_VkSubpassDescription2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDescription2KHR* toTransform);

void transform_fromhost_VkSubpassDescription2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDescription2KHR* toTransform);

void transform_tohost_VkSubpassDependency2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDependency2KHR* toTransform);

void transform_fromhost_VkSubpassDependency2KHR(
    ResourceTracker* resourceTracker,
    VkSubpassDependency2KHR* toTransform);

void transform_tohost_VkRenderPassCreateInfo2KHR(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo2KHR* toTransform);

void transform_fromhost_VkRenderPassCreateInfo2KHR(
    ResourceTracker* resourceTracker,
    VkRenderPassCreateInfo2KHR* toTransform);

void transform_tohost_VkSubpassBeginInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassBeginInfoKHR* toTransform);

void transform_fromhost_VkSubpassBeginInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassBeginInfoKHR* toTransform);

void transform_tohost_VkSubpassEndInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassEndInfoKHR* toTransform);

void transform_fromhost_VkSubpassEndInfoKHR(
    ResourceTracker* resourceTracker,
    VkSubpassEndInfoKHR* toTransform);

#endif
#ifdef VK_KHR_shared_presentable_image
void transform_tohost_VkSharedPresentSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSharedPresentSurfaceCapabilitiesKHR* toTransform);

void transform_fromhost_VkSharedPresentSurfaceCapabilitiesKHR(
    ResourceTracker* resourceTracker,
    VkSharedPresentSurfaceCapabilitiesKHR* toTransform);

#endif
#ifdef VK_KHR_external_fence_capabilities
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void transform_tohost_VkImportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkImportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceWin32HandleInfoKHR* toTransform);

void transform_tohost_VkExportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportFenceWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkExportFenceWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkExportFenceWin32HandleInfoKHR* toTransform);

void transform_tohost_VkFenceGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetWin32HandleInfoKHR* toTransform);

void transform_fromhost_VkFenceGetWin32HandleInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetWin32HandleInfoKHR* toTransform);

#endif
#ifdef VK_KHR_external_fence_fd
void transform_tohost_VkImportFenceFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceFdInfoKHR* toTransform);

void transform_fromhost_VkImportFenceFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkImportFenceFdInfoKHR* toTransform);

void transform_tohost_VkFenceGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetFdInfoKHR* toTransform);

void transform_fromhost_VkFenceGetFdInfoKHR(
    ResourceTracker* resourceTracker,
    VkFenceGetFdInfoKHR* toTransform);

#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void transform_tohost_VkPhysicalDeviceSurfaceInfo2KHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSurfaceInfo2KHR* toTransform);

void transform_fromhost_VkPhysicalDeviceSurfaceInfo2KHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSurfaceInfo2KHR* toTransform);

void transform_tohost_VkSurfaceCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2KHR* toTransform);

void transform_fromhost_VkSurfaceCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2KHR* toTransform);

void transform_tohost_VkSurfaceFormat2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormat2KHR* toTransform);

void transform_fromhost_VkSurfaceFormat2KHR(
    ResourceTracker* resourceTracker,
    VkSurfaceFormat2KHR* toTransform);

#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void transform_tohost_VkDisplayProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayProperties2KHR* toTransform);

void transform_fromhost_VkDisplayProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayProperties2KHR* toTransform);

void transform_tohost_VkDisplayPlaneProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneProperties2KHR* toTransform);

void transform_fromhost_VkDisplayPlaneProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneProperties2KHR* toTransform);

void transform_tohost_VkDisplayModeProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeProperties2KHR* toTransform);

void transform_fromhost_VkDisplayModeProperties2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayModeProperties2KHR* toTransform);

void transform_tohost_VkDisplayPlaneInfo2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneInfo2KHR* toTransform);

void transform_fromhost_VkDisplayPlaneInfo2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneInfo2KHR* toTransform);

void transform_tohost_VkDisplayPlaneCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilities2KHR* toTransform);

void transform_fromhost_VkDisplayPlaneCapabilities2KHR(
    ResourceTracker* resourceTracker,
    VkDisplayPlaneCapabilities2KHR* toTransform);

#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#endif
#ifdef VK_KHR_image_format_list
void transform_tohost_VkImageFormatListCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageFormatListCreateInfoKHR* toTransform);

void transform_fromhost_VkImageFormatListCreateInfoKHR(
    ResourceTracker* resourceTracker,
    VkImageFormatListCreateInfoKHR* toTransform);

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#endif
#ifdef VK_KHR_bind_memory2
#endif
#ifdef VK_KHR_maintenance3
#endif
#ifdef VK_KHR_draw_indirect_count
#endif
#ifdef VK_KHR_8bit_storage
void transform_tohost_VkPhysicalDevice8BitStorageFeaturesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice8BitStorageFeaturesKHR* toTransform);

void transform_fromhost_VkPhysicalDevice8BitStorageFeaturesKHR(
    ResourceTracker* resourceTracker,
    VkPhysicalDevice8BitStorageFeaturesKHR* toTransform);

#endif
#ifdef VK_ANDROID_native_buffer
void transform_tohost_VkNativeBufferANDROID(
    ResourceTracker* resourceTracker,
    VkNativeBufferANDROID* toTransform);

void transform_fromhost_VkNativeBufferANDROID(
    ResourceTracker* resourceTracker,
    VkNativeBufferANDROID* toTransform);

#endif
#ifdef VK_EXT_debug_report
void transform_tohost_VkDebugReportCallbackCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugReportCallbackCreateInfoEXT* toTransform);

void transform_fromhost_VkDebugReportCallbackCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugReportCallbackCreateInfoEXT* toTransform);

#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void transform_tohost_VkPipelineRasterizationStateRasterizationOrderAMD(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateRasterizationOrderAMD* toTransform);

void transform_fromhost_VkPipelineRasterizationStateRasterizationOrderAMD(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationStateRasterizationOrderAMD* toTransform);

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void transform_tohost_VkDebugMarkerObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectNameInfoEXT* toTransform);

void transform_fromhost_VkDebugMarkerObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectNameInfoEXT* toTransform);

void transform_tohost_VkDebugMarkerObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectTagInfoEXT* toTransform);

void transform_fromhost_VkDebugMarkerObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerObjectTagInfoEXT* toTransform);

void transform_tohost_VkDebugMarkerMarkerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerMarkerInfoEXT* toTransform);

void transform_fromhost_VkDebugMarkerMarkerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugMarkerMarkerInfoEXT* toTransform);

#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void transform_tohost_VkDedicatedAllocationImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationImageCreateInfoNV* toTransform);

void transform_fromhost_VkDedicatedAllocationImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationImageCreateInfoNV* toTransform);

void transform_tohost_VkDedicatedAllocationBufferCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationBufferCreateInfoNV* toTransform);

void transform_fromhost_VkDedicatedAllocationBufferCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationBufferCreateInfoNV* toTransform);

void transform_tohost_VkDedicatedAllocationMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationMemoryAllocateInfoNV* toTransform);

void transform_fromhost_VkDedicatedAllocationMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkDedicatedAllocationMemoryAllocateInfoNV* toTransform);

#endif
#ifdef VK_AMD_draw_indirect_count
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void transform_tohost_VkTextureLODGatherFormatPropertiesAMD(
    ResourceTracker* resourceTracker,
    VkTextureLODGatherFormatPropertiesAMD* toTransform);

void transform_fromhost_VkTextureLODGatherFormatPropertiesAMD(
    ResourceTracker* resourceTracker,
    VkTextureLODGatherFormatPropertiesAMD* toTransform);

#endif
#ifdef VK_AMD_shader_info
void transform_tohost_VkShaderResourceUsageAMD(
    ResourceTracker* resourceTracker,
    VkShaderResourceUsageAMD* toTransform);

void transform_fromhost_VkShaderResourceUsageAMD(
    ResourceTracker* resourceTracker,
    VkShaderResourceUsageAMD* toTransform);

void transform_tohost_VkShaderStatisticsInfoAMD(
    ResourceTracker* resourceTracker,
    VkShaderStatisticsInfoAMD* toTransform);

void transform_fromhost_VkShaderStatisticsInfoAMD(
    ResourceTracker* resourceTracker,
    VkShaderStatisticsInfoAMD* toTransform);

#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void transform_tohost_VkExternalImageFormatPropertiesNV(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatPropertiesNV* toTransform);

void transform_fromhost_VkExternalImageFormatPropertiesNV(
    ResourceTracker* resourceTracker,
    VkExternalImageFormatPropertiesNV* toTransform);

#endif
#ifdef VK_NV_external_memory
void transform_tohost_VkExternalMemoryImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfoNV* toTransform);

void transform_fromhost_VkExternalMemoryImageCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkExternalMemoryImageCreateInfoNV* toTransform);

void transform_tohost_VkExportMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfoNV* toTransform);

void transform_fromhost_VkExportMemoryAllocateInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryAllocateInfoNV* toTransform);

#endif
#ifdef VK_NV_external_memory_win32
void transform_tohost_VkImportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoNV* toTransform);

void transform_fromhost_VkImportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkImportMemoryWin32HandleInfoNV* toTransform);

void transform_tohost_VkExportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoNV* toTransform);

void transform_fromhost_VkExportMemoryWin32HandleInfoNV(
    ResourceTracker* resourceTracker,
    VkExportMemoryWin32HandleInfoNV* toTransform);

#endif
#ifdef VK_NV_win32_keyed_mutex
void transform_tohost_VkWin32KeyedMutexAcquireReleaseInfoNV(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoNV* toTransform);

void transform_fromhost_VkWin32KeyedMutexAcquireReleaseInfoNV(
    ResourceTracker* resourceTracker,
    VkWin32KeyedMutexAcquireReleaseInfoNV* toTransform);

#endif
#ifdef VK_EXT_validation_flags
void transform_tohost_VkValidationFlagsEXT(
    ResourceTracker* resourceTracker,
    VkValidationFlagsEXT* toTransform);

void transform_fromhost_VkValidationFlagsEXT(
    ResourceTracker* resourceTracker,
    VkValidationFlagsEXT* toTransform);

#endif
#ifdef VK_NN_vi_surface
void transform_tohost_VkViSurfaceCreateInfoNN(
    ResourceTracker* resourceTracker,
    VkViSurfaceCreateInfoNN* toTransform);

void transform_fromhost_VkViSurfaceCreateInfoNN(
    ResourceTracker* resourceTracker,
    VkViSurfaceCreateInfoNN* toTransform);

#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void transform_tohost_VkConditionalRenderingBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkConditionalRenderingBeginInfoEXT* toTransform);

void transform_fromhost_VkConditionalRenderingBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkConditionalRenderingBeginInfoEXT* toTransform);

void transform_tohost_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* toTransform);

void transform_tohost_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* toTransform);

void transform_fromhost_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    ResourceTracker* resourceTracker,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* toTransform);

#endif
#ifdef VK_NVX_device_generated_commands
void transform_tohost_VkDeviceGeneratedCommandsFeaturesNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsFeaturesNVX* toTransform);

void transform_fromhost_VkDeviceGeneratedCommandsFeaturesNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsFeaturesNVX* toTransform);

void transform_tohost_VkDeviceGeneratedCommandsLimitsNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsLimitsNVX* toTransform);

void transform_fromhost_VkDeviceGeneratedCommandsLimitsNVX(
    ResourceTracker* resourceTracker,
    VkDeviceGeneratedCommandsLimitsNVX* toTransform);

void transform_tohost_VkIndirectCommandsTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsTokenNVX* toTransform);

void transform_fromhost_VkIndirectCommandsTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsTokenNVX* toTransform);

void transform_tohost_VkIndirectCommandsLayoutTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutTokenNVX* toTransform);

void transform_fromhost_VkIndirectCommandsLayoutTokenNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutTokenNVX* toTransform);

void transform_tohost_VkIndirectCommandsLayoutCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutCreateInfoNVX* toTransform);

void transform_fromhost_VkIndirectCommandsLayoutCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkIndirectCommandsLayoutCreateInfoNVX* toTransform);

void transform_tohost_VkCmdProcessCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdProcessCommandsInfoNVX* toTransform);

void transform_fromhost_VkCmdProcessCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdProcessCommandsInfoNVX* toTransform);

void transform_tohost_VkCmdReserveSpaceForCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdReserveSpaceForCommandsInfoNVX* toTransform);

void transform_fromhost_VkCmdReserveSpaceForCommandsInfoNVX(
    ResourceTracker* resourceTracker,
    VkCmdReserveSpaceForCommandsInfoNVX* toTransform);

void transform_tohost_VkObjectTableCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableCreateInfoNVX* toTransform);

void transform_fromhost_VkObjectTableCreateInfoNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableCreateInfoNVX* toTransform);

void transform_tohost_VkObjectTableEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableEntryNVX* toTransform);

void transform_fromhost_VkObjectTableEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableEntryNVX* toTransform);

void transform_tohost_VkObjectTablePipelineEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePipelineEntryNVX* toTransform);

void transform_fromhost_VkObjectTablePipelineEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePipelineEntryNVX* toTransform);

void transform_tohost_VkObjectTableDescriptorSetEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableDescriptorSetEntryNVX* toTransform);

void transform_fromhost_VkObjectTableDescriptorSetEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableDescriptorSetEntryNVX* toTransform);

void transform_tohost_VkObjectTableVertexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableVertexBufferEntryNVX* toTransform);

void transform_fromhost_VkObjectTableVertexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableVertexBufferEntryNVX* toTransform);

void transform_tohost_VkObjectTableIndexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableIndexBufferEntryNVX* toTransform);

void transform_fromhost_VkObjectTableIndexBufferEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTableIndexBufferEntryNVX* toTransform);

void transform_tohost_VkObjectTablePushConstantEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePushConstantEntryNVX* toTransform);

void transform_fromhost_VkObjectTablePushConstantEntryNVX(
    ResourceTracker* resourceTracker,
    VkObjectTablePushConstantEntryNVX* toTransform);

#endif
#ifdef VK_NV_clip_space_w_scaling
void transform_tohost_VkViewportWScalingNV(
    ResourceTracker* resourceTracker,
    VkViewportWScalingNV* toTransform);

void transform_fromhost_VkViewportWScalingNV(
    ResourceTracker* resourceTracker,
    VkViewportWScalingNV* toTransform);

void transform_tohost_VkPipelineViewportWScalingStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportWScalingStateCreateInfoNV* toTransform);

void transform_fromhost_VkPipelineViewportWScalingStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportWScalingStateCreateInfoNV* toTransform);

#endif
#ifdef VK_EXT_direct_mode_display
#endif
#ifdef VK_EXT_acquire_xlib_display
#endif
#ifdef VK_EXT_display_surface_counter
void transform_tohost_VkSurfaceCapabilities2EXT(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2EXT* toTransform);

void transform_fromhost_VkSurfaceCapabilities2EXT(
    ResourceTracker* resourceTracker,
    VkSurfaceCapabilities2EXT* toTransform);

#endif
#ifdef VK_EXT_display_control
void transform_tohost_VkDisplayPowerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayPowerInfoEXT* toTransform);

void transform_fromhost_VkDisplayPowerInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayPowerInfoEXT* toTransform);

void transform_tohost_VkDeviceEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceEventInfoEXT* toTransform);

void transform_fromhost_VkDeviceEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceEventInfoEXT* toTransform);

void transform_tohost_VkDisplayEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayEventInfoEXT* toTransform);

void transform_fromhost_VkDisplayEventInfoEXT(
    ResourceTracker* resourceTracker,
    VkDisplayEventInfoEXT* toTransform);

void transform_tohost_VkSwapchainCounterCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSwapchainCounterCreateInfoEXT* toTransform);

void transform_fromhost_VkSwapchainCounterCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSwapchainCounterCreateInfoEXT* toTransform);

#endif
#ifdef VK_GOOGLE_display_timing
void transform_tohost_VkRefreshCycleDurationGOOGLE(
    ResourceTracker* resourceTracker,
    VkRefreshCycleDurationGOOGLE* toTransform);

void transform_fromhost_VkRefreshCycleDurationGOOGLE(
    ResourceTracker* resourceTracker,
    VkRefreshCycleDurationGOOGLE* toTransform);

void transform_tohost_VkPastPresentationTimingGOOGLE(
    ResourceTracker* resourceTracker,
    VkPastPresentationTimingGOOGLE* toTransform);

void transform_fromhost_VkPastPresentationTimingGOOGLE(
    ResourceTracker* resourceTracker,
    VkPastPresentationTimingGOOGLE* toTransform);

void transform_tohost_VkPresentTimeGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimeGOOGLE* toTransform);

void transform_fromhost_VkPresentTimeGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimeGOOGLE* toTransform);

void transform_tohost_VkPresentTimesInfoGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimesInfoGOOGLE* toTransform);

void transform_fromhost_VkPresentTimesInfoGOOGLE(
    ResourceTracker* resourceTracker,
    VkPresentTimesInfoGOOGLE* toTransform);

#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void transform_tohost_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* toTransform);

void transform_fromhost_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* toTransform);

#endif
#ifdef VK_NV_viewport_swizzle
void transform_tohost_VkViewportSwizzleNV(
    ResourceTracker* resourceTracker,
    VkViewportSwizzleNV* toTransform);

void transform_fromhost_VkViewportSwizzleNV(
    ResourceTracker* resourceTracker,
    VkViewportSwizzleNV* toTransform);

void transform_tohost_VkPipelineViewportSwizzleStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportSwizzleStateCreateInfoNV* toTransform);

void transform_fromhost_VkPipelineViewportSwizzleStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineViewportSwizzleStateCreateInfoNV* toTransform);

#endif
#ifdef VK_EXT_discard_rectangles
void transform_tohost_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* toTransform);

void transform_tohost_VkPipelineDiscardRectangleStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineDiscardRectangleStateCreateInfoEXT* toTransform);

void transform_fromhost_VkPipelineDiscardRectangleStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineDiscardRectangleStateCreateInfoEXT* toTransform);

#endif
#ifdef VK_EXT_conservative_rasterization
void transform_tohost_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* toTransform);

void transform_tohost_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* toTransform);

void transform_fromhost_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* toTransform);

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void transform_tohost_VkXYColorEXT(
    ResourceTracker* resourceTracker,
    VkXYColorEXT* toTransform);

void transform_fromhost_VkXYColorEXT(
    ResourceTracker* resourceTracker,
    VkXYColorEXT* toTransform);

void transform_tohost_VkHdrMetadataEXT(
    ResourceTracker* resourceTracker,
    VkHdrMetadataEXT* toTransform);

void transform_fromhost_VkHdrMetadataEXT(
    ResourceTracker* resourceTracker,
    VkHdrMetadataEXT* toTransform);

#endif
#ifdef VK_MVK_ios_surface
void transform_tohost_VkIOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkIOSSurfaceCreateInfoMVK* toTransform);

void transform_fromhost_VkIOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkIOSSurfaceCreateInfoMVK* toTransform);

#endif
#ifdef VK_MVK_macos_surface
void transform_tohost_VkMacOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkMacOSSurfaceCreateInfoMVK* toTransform);

void transform_fromhost_VkMacOSSurfaceCreateInfoMVK(
    ResourceTracker* resourceTracker,
    VkMacOSSurfaceCreateInfoMVK* toTransform);

#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void transform_tohost_VkDebugUtilsObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectNameInfoEXT* toTransform);

void transform_fromhost_VkDebugUtilsObjectNameInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectNameInfoEXT* toTransform);

void transform_tohost_VkDebugUtilsObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectTagInfoEXT* toTransform);

void transform_fromhost_VkDebugUtilsObjectTagInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsObjectTagInfoEXT* toTransform);

void transform_tohost_VkDebugUtilsLabelEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsLabelEXT* toTransform);

void transform_fromhost_VkDebugUtilsLabelEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsLabelEXT* toTransform);

void transform_tohost_VkDebugUtilsMessengerCallbackDataEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCallbackDataEXT* toTransform);

void transform_fromhost_VkDebugUtilsMessengerCallbackDataEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCallbackDataEXT* toTransform);

void transform_tohost_VkDebugUtilsMessengerCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCreateInfoEXT* toTransform);

void transform_fromhost_VkDebugUtilsMessengerCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDebugUtilsMessengerCreateInfoEXT* toTransform);

#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void transform_tohost_VkAndroidHardwareBufferUsageANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferUsageANDROID* toTransform);

void transform_fromhost_VkAndroidHardwareBufferUsageANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferUsageANDROID* toTransform);

void transform_tohost_VkAndroidHardwareBufferPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferPropertiesANDROID* toTransform);

void transform_fromhost_VkAndroidHardwareBufferPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferPropertiesANDROID* toTransform);

void transform_tohost_VkAndroidHardwareBufferFormatPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferFormatPropertiesANDROID* toTransform);

void transform_fromhost_VkAndroidHardwareBufferFormatPropertiesANDROID(
    ResourceTracker* resourceTracker,
    VkAndroidHardwareBufferFormatPropertiesANDROID* toTransform);

void transform_tohost_VkImportAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkImportAndroidHardwareBufferInfoANDROID* toTransform);

void transform_fromhost_VkImportAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkImportAndroidHardwareBufferInfoANDROID* toTransform);

void transform_tohost_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* toTransform);

void transform_fromhost_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    ResourceTracker* resourceTracker,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* toTransform);

void transform_tohost_VkExternalFormatANDROID(
    ResourceTracker* resourceTracker,
    VkExternalFormatANDROID* toTransform);

void transform_fromhost_VkExternalFormatANDROID(
    ResourceTracker* resourceTracker,
    VkExternalFormatANDROID* toTransform);

#endif
#ifdef VK_EXT_sampler_filter_minmax
void transform_tohost_VkSamplerReductionModeCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSamplerReductionModeCreateInfoEXT* toTransform);

void transform_fromhost_VkSamplerReductionModeCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkSamplerReductionModeCreateInfoEXT* toTransform);

void transform_tohost_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* toTransform);

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void transform_tohost_VkSampleLocationEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationEXT* toTransform);

void transform_fromhost_VkSampleLocationEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationEXT* toTransform);

void transform_tohost_VkSampleLocationsInfoEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationsInfoEXT* toTransform);

void transform_fromhost_VkSampleLocationsInfoEXT(
    ResourceTracker* resourceTracker,
    VkSampleLocationsInfoEXT* toTransform);

void transform_tohost_VkAttachmentSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkAttachmentSampleLocationsEXT* toTransform);

void transform_fromhost_VkAttachmentSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkAttachmentSampleLocationsEXT* toTransform);

void transform_tohost_VkSubpassSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkSubpassSampleLocationsEXT* toTransform);

void transform_fromhost_VkSubpassSampleLocationsEXT(
    ResourceTracker* resourceTracker,
    VkSubpassSampleLocationsEXT* toTransform);

void transform_tohost_VkRenderPassSampleLocationsBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkRenderPassSampleLocationsBeginInfoEXT* toTransform);

void transform_fromhost_VkRenderPassSampleLocationsBeginInfoEXT(
    ResourceTracker* resourceTracker,
    VkRenderPassSampleLocationsBeginInfoEXT* toTransform);

void transform_tohost_VkPipelineSampleLocationsStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineSampleLocationsStateCreateInfoEXT* toTransform);

void transform_fromhost_VkPipelineSampleLocationsStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineSampleLocationsStateCreateInfoEXT* toTransform);

void transform_tohost_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* toTransform);

void transform_tohost_VkMultisamplePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMultisamplePropertiesEXT* toTransform);

void transform_fromhost_VkMultisamplePropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMultisamplePropertiesEXT* toTransform);

#endif
#ifdef VK_EXT_blend_operation_advanced
void transform_tohost_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* toTransform);

void transform_tohost_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* toTransform);

void transform_tohost_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* toTransform);

void transform_fromhost_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* toTransform);

#endif
#ifdef VK_NV_fragment_coverage_to_color
void transform_tohost_VkPipelineCoverageToColorStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageToColorStateCreateInfoNV* toTransform);

void transform_fromhost_VkPipelineCoverageToColorStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageToColorStateCreateInfoNV* toTransform);

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void transform_tohost_VkPipelineCoverageModulationStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageModulationStateCreateInfoNV* toTransform);

void transform_fromhost_VkPipelineCoverageModulationStateCreateInfoNV(
    ResourceTracker* resourceTracker,
    VkPipelineCoverageModulationStateCreateInfoNV* toTransform);

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void transform_tohost_VkValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkValidationCacheCreateInfoEXT* toTransform);

void transform_fromhost_VkValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkValidationCacheCreateInfoEXT* toTransform);

void transform_tohost_VkShaderModuleValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkShaderModuleValidationCacheCreateInfoEXT* toTransform);

void transform_fromhost_VkShaderModuleValidationCacheCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkShaderModuleValidationCacheCreateInfoEXT* toTransform);

#endif
#ifdef VK_EXT_descriptor_indexing
void transform_tohost_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* toTransform);

void transform_fromhost_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* toTransform);

void transform_tohost_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* toTransform);

void transform_tohost_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* toTransform);

void transform_tohost_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* toTransform);

void transform_fromhost_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* toTransform);

void transform_tohost_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* toTransform);

void transform_fromhost_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    ResourceTracker* resourceTracker,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* toTransform);

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void transform_tohost_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* toTransform);

void transform_fromhost_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* toTransform);

#endif
#ifdef VK_EXT_external_memory_host
void transform_tohost_VkImportMemoryHostPointerInfoEXT(
    ResourceTracker* resourceTracker,
    VkImportMemoryHostPointerInfoEXT* toTransform);

void transform_fromhost_VkImportMemoryHostPointerInfoEXT(
    ResourceTracker* resourceTracker,
    VkImportMemoryHostPointerInfoEXT* toTransform);

void transform_tohost_VkMemoryHostPointerPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMemoryHostPointerPropertiesEXT* toTransform);

void transform_fromhost_VkMemoryHostPointerPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkMemoryHostPointerPropertiesEXT* toTransform);

void transform_tohost_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* toTransform);

#endif
#ifdef VK_AMD_buffer_marker
#endif
#ifdef VK_AMD_shader_core_properties
void transform_tohost_VkPhysicalDeviceShaderCorePropertiesAMD(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderCorePropertiesAMD* toTransform);

void transform_fromhost_VkPhysicalDeviceShaderCorePropertiesAMD(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceShaderCorePropertiesAMD* toTransform);

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void transform_tohost_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* toTransform);

void transform_fromhost_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    ResourceTracker* resourceTracker,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* toTransform);

void transform_tohost_VkVertexInputBindingDivisorDescriptionEXT(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDivisorDescriptionEXT* toTransform);

void transform_fromhost_VkVertexInputBindingDivisorDescriptionEXT(
    ResourceTracker* resourceTracker,
    VkVertexInputBindingDivisorDescriptionEXT* toTransform);

void transform_tohost_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* toTransform);

void transform_fromhost_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    ResourceTracker* resourceTracker,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* toTransform);

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void transform_tohost_VkQueueFamilyCheckpointPropertiesNV(
    ResourceTracker* resourceTracker,
    VkQueueFamilyCheckpointPropertiesNV* toTransform);

void transform_fromhost_VkQueueFamilyCheckpointPropertiesNV(
    ResourceTracker* resourceTracker,
    VkQueueFamilyCheckpointPropertiesNV* toTransform);

void transform_tohost_VkCheckpointDataNV(
    ResourceTracker* resourceTracker,
    VkCheckpointDataNV* toTransform);

void transform_fromhost_VkCheckpointDataNV(
    ResourceTracker* resourceTracker,
    VkCheckpointDataNV* toTransform);

#endif
#ifdef VK_GOOGLE_address_space
#endif
#ifdef VK_GOOGLE_color_buffer
void transform_tohost_VkImportColorBufferGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportColorBufferGOOGLE* toTransform);

void transform_fromhost_VkImportColorBufferGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportColorBufferGOOGLE* toTransform);

void transform_tohost_VkImportPhysicalAddressGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportPhysicalAddressGOOGLE* toTransform);

void transform_fromhost_VkImportPhysicalAddressGOOGLE(
    ResourceTracker* resourceTracker,
    VkImportPhysicalAddressGOOGLE* toTransform);

#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#endif
#ifdef VK_GOOGLE_async_command_buffers
#endif

} // namespace goldfish_vk
