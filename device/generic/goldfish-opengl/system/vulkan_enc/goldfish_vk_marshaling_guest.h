// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_marshaling_guest
// (header) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#pragma once

#include <vulkan/vulkan.h>


#include "vk_platform_compat.h"

#include "goldfish_vk_marshaling_guest.h"
#include "goldfish_vk_private_defs.h"
#include "VulkanStreamGuest.h"

// Stuff we are not going to use but if included,
// will cause compile errors. These are Android Vulkan
// required extensions, but the approach will be to
// implement them completely on the guest side.
#undef VK_KHR_android_surface
#undef VK_ANDROID_external_memory_android_hardware_buffer


namespace goldfish_vk {

#ifdef VK_VERSION_1_0
void marshal_VkApplicationInfo(
    VulkanStreamGuest* vkStream,
    const VkApplicationInfo* forMarshaling);

void unmarshal_VkApplicationInfo(
    VulkanStreamGuest* vkStream,
    VkApplicationInfo* forUnmarshaling);

void marshal_VkInstanceCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkInstanceCreateInfo* forMarshaling);

void unmarshal_VkInstanceCreateInfo(
    VulkanStreamGuest* vkStream,
    VkInstanceCreateInfo* forUnmarshaling);

void marshal_VkAllocationCallbacks(
    VulkanStreamGuest* vkStream,
    const VkAllocationCallbacks* forMarshaling);

void unmarshal_VkAllocationCallbacks(
    VulkanStreamGuest* vkStream,
    VkAllocationCallbacks* forUnmarshaling);

#define OP_vkCreateInstance 20000
#define OP_vkDestroyInstance 20001
#define OP_vkEnumeratePhysicalDevices 20002
void marshal_VkPhysicalDeviceFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceFeatures* forMarshaling);

void unmarshal_VkPhysicalDeviceFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceFeatures* forUnmarshaling);

#define OP_vkGetPhysicalDeviceFeatures 20003
void marshal_VkFormatProperties(
    VulkanStreamGuest* vkStream,
    const VkFormatProperties* forMarshaling);

void unmarshal_VkFormatProperties(
    VulkanStreamGuest* vkStream,
    VkFormatProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceFormatProperties 20004
void marshal_VkExtent3D(
    VulkanStreamGuest* vkStream,
    const VkExtent3D* forMarshaling);

void unmarshal_VkExtent3D(
    VulkanStreamGuest* vkStream,
    VkExtent3D* forUnmarshaling);

void marshal_VkImageFormatProperties(
    VulkanStreamGuest* vkStream,
    const VkImageFormatProperties* forMarshaling);

void unmarshal_VkImageFormatProperties(
    VulkanStreamGuest* vkStream,
    VkImageFormatProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceImageFormatProperties 20005
void marshal_VkPhysicalDeviceLimits(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceLimits* forMarshaling);

void unmarshal_VkPhysicalDeviceLimits(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceLimits* forUnmarshaling);

void marshal_VkPhysicalDeviceSparseProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSparseProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceSparseProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSparseProperties* forUnmarshaling);

void marshal_VkPhysicalDeviceProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceProperties 20006
void marshal_VkQueueFamilyProperties(
    VulkanStreamGuest* vkStream,
    const VkQueueFamilyProperties* forMarshaling);

void unmarshal_VkQueueFamilyProperties(
    VulkanStreamGuest* vkStream,
    VkQueueFamilyProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceQueueFamilyProperties 20007
void marshal_VkMemoryType(
    VulkanStreamGuest* vkStream,
    const VkMemoryType* forMarshaling);

void unmarshal_VkMemoryType(
    VulkanStreamGuest* vkStream,
    VkMemoryType* forUnmarshaling);

void marshal_VkMemoryHeap(
    VulkanStreamGuest* vkStream,
    const VkMemoryHeap* forMarshaling);

void unmarshal_VkMemoryHeap(
    VulkanStreamGuest* vkStream,
    VkMemoryHeap* forUnmarshaling);

void marshal_VkPhysicalDeviceMemoryProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceMemoryProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceMemoryProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceMemoryProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceMemoryProperties 20008
#define OP_vkGetInstanceProcAddr 20009
#define OP_vkGetDeviceProcAddr 20010
void marshal_VkDeviceQueueCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceQueueCreateInfo* forMarshaling);

void unmarshal_VkDeviceQueueCreateInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceQueueCreateInfo* forUnmarshaling);

void marshal_VkDeviceCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceCreateInfo* forMarshaling);

void unmarshal_VkDeviceCreateInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceCreateInfo* forUnmarshaling);

#define OP_vkCreateDevice 20011
#define OP_vkDestroyDevice 20012
void marshal_VkExtensionProperties(
    VulkanStreamGuest* vkStream,
    const VkExtensionProperties* forMarshaling);

void unmarshal_VkExtensionProperties(
    VulkanStreamGuest* vkStream,
    VkExtensionProperties* forUnmarshaling);

#define OP_vkEnumerateInstanceExtensionProperties 20013
#define OP_vkEnumerateDeviceExtensionProperties 20014
void marshal_VkLayerProperties(
    VulkanStreamGuest* vkStream,
    const VkLayerProperties* forMarshaling);

void unmarshal_VkLayerProperties(
    VulkanStreamGuest* vkStream,
    VkLayerProperties* forUnmarshaling);

#define OP_vkEnumerateInstanceLayerProperties 20015
#define OP_vkEnumerateDeviceLayerProperties 20016
#define OP_vkGetDeviceQueue 20017
void marshal_VkSubmitInfo(
    VulkanStreamGuest* vkStream,
    const VkSubmitInfo* forMarshaling);

void unmarshal_VkSubmitInfo(
    VulkanStreamGuest* vkStream,
    VkSubmitInfo* forUnmarshaling);

#define OP_vkQueueSubmit 20018
#define OP_vkQueueWaitIdle 20019
#define OP_vkDeviceWaitIdle 20020
void marshal_VkMemoryAllocateInfo(
    VulkanStreamGuest* vkStream,
    const VkMemoryAllocateInfo* forMarshaling);

void unmarshal_VkMemoryAllocateInfo(
    VulkanStreamGuest* vkStream,
    VkMemoryAllocateInfo* forUnmarshaling);

#define OP_vkAllocateMemory 20021
#define OP_vkFreeMemory 20022
#define OP_vkMapMemory 20023
#define OP_vkUnmapMemory 20024
void marshal_VkMappedMemoryRange(
    VulkanStreamGuest* vkStream,
    const VkMappedMemoryRange* forMarshaling);

void unmarshal_VkMappedMemoryRange(
    VulkanStreamGuest* vkStream,
    VkMappedMemoryRange* forUnmarshaling);

#define OP_vkFlushMappedMemoryRanges 20025
#define OP_vkInvalidateMappedMemoryRanges 20026
#define OP_vkGetDeviceMemoryCommitment 20027
#define OP_vkBindBufferMemory 20028
#define OP_vkBindImageMemory 20029
void marshal_VkMemoryRequirements(
    VulkanStreamGuest* vkStream,
    const VkMemoryRequirements* forMarshaling);

void unmarshal_VkMemoryRequirements(
    VulkanStreamGuest* vkStream,
    VkMemoryRequirements* forUnmarshaling);

#define OP_vkGetBufferMemoryRequirements 20030
#define OP_vkGetImageMemoryRequirements 20031
void marshal_VkSparseImageFormatProperties(
    VulkanStreamGuest* vkStream,
    const VkSparseImageFormatProperties* forMarshaling);

void unmarshal_VkSparseImageFormatProperties(
    VulkanStreamGuest* vkStream,
    VkSparseImageFormatProperties* forUnmarshaling);

void marshal_VkSparseImageMemoryRequirements(
    VulkanStreamGuest* vkStream,
    const VkSparseImageMemoryRequirements* forMarshaling);

void unmarshal_VkSparseImageMemoryRequirements(
    VulkanStreamGuest* vkStream,
    VkSparseImageMemoryRequirements* forUnmarshaling);

#define OP_vkGetImageSparseMemoryRequirements 20032
#define OP_vkGetPhysicalDeviceSparseImageFormatProperties 20033
void marshal_VkSparseMemoryBind(
    VulkanStreamGuest* vkStream,
    const VkSparseMemoryBind* forMarshaling);

void unmarshal_VkSparseMemoryBind(
    VulkanStreamGuest* vkStream,
    VkSparseMemoryBind* forUnmarshaling);

void marshal_VkSparseBufferMemoryBindInfo(
    VulkanStreamGuest* vkStream,
    const VkSparseBufferMemoryBindInfo* forMarshaling);

void unmarshal_VkSparseBufferMemoryBindInfo(
    VulkanStreamGuest* vkStream,
    VkSparseBufferMemoryBindInfo* forUnmarshaling);

void marshal_VkSparseImageOpaqueMemoryBindInfo(
    VulkanStreamGuest* vkStream,
    const VkSparseImageOpaqueMemoryBindInfo* forMarshaling);

void unmarshal_VkSparseImageOpaqueMemoryBindInfo(
    VulkanStreamGuest* vkStream,
    VkSparseImageOpaqueMemoryBindInfo* forUnmarshaling);

void marshal_VkImageSubresource(
    VulkanStreamGuest* vkStream,
    const VkImageSubresource* forMarshaling);

void unmarshal_VkImageSubresource(
    VulkanStreamGuest* vkStream,
    VkImageSubresource* forUnmarshaling);

void marshal_VkOffset3D(
    VulkanStreamGuest* vkStream,
    const VkOffset3D* forMarshaling);

void unmarshal_VkOffset3D(
    VulkanStreamGuest* vkStream,
    VkOffset3D* forUnmarshaling);

void marshal_VkSparseImageMemoryBind(
    VulkanStreamGuest* vkStream,
    const VkSparseImageMemoryBind* forMarshaling);

void unmarshal_VkSparseImageMemoryBind(
    VulkanStreamGuest* vkStream,
    VkSparseImageMemoryBind* forUnmarshaling);

void marshal_VkSparseImageMemoryBindInfo(
    VulkanStreamGuest* vkStream,
    const VkSparseImageMemoryBindInfo* forMarshaling);

void unmarshal_VkSparseImageMemoryBindInfo(
    VulkanStreamGuest* vkStream,
    VkSparseImageMemoryBindInfo* forUnmarshaling);

void marshal_VkBindSparseInfo(
    VulkanStreamGuest* vkStream,
    const VkBindSparseInfo* forMarshaling);

void unmarshal_VkBindSparseInfo(
    VulkanStreamGuest* vkStream,
    VkBindSparseInfo* forUnmarshaling);

#define OP_vkQueueBindSparse 20034
void marshal_VkFenceCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkFenceCreateInfo* forMarshaling);

void unmarshal_VkFenceCreateInfo(
    VulkanStreamGuest* vkStream,
    VkFenceCreateInfo* forUnmarshaling);

#define OP_vkCreateFence 20035
#define OP_vkDestroyFence 20036
#define OP_vkResetFences 20037
#define OP_vkGetFenceStatus 20038
#define OP_vkWaitForFences 20039
void marshal_VkSemaphoreCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkSemaphoreCreateInfo* forMarshaling);

void unmarshal_VkSemaphoreCreateInfo(
    VulkanStreamGuest* vkStream,
    VkSemaphoreCreateInfo* forUnmarshaling);

#define OP_vkCreateSemaphore 20040
#define OP_vkDestroySemaphore 20041
void marshal_VkEventCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkEventCreateInfo* forMarshaling);

void unmarshal_VkEventCreateInfo(
    VulkanStreamGuest* vkStream,
    VkEventCreateInfo* forUnmarshaling);

#define OP_vkCreateEvent 20042
#define OP_vkDestroyEvent 20043
#define OP_vkGetEventStatus 20044
#define OP_vkSetEvent 20045
#define OP_vkResetEvent 20046
void marshal_VkQueryPoolCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkQueryPoolCreateInfo* forMarshaling);

void unmarshal_VkQueryPoolCreateInfo(
    VulkanStreamGuest* vkStream,
    VkQueryPoolCreateInfo* forUnmarshaling);

#define OP_vkCreateQueryPool 20047
#define OP_vkDestroyQueryPool 20048
#define OP_vkGetQueryPoolResults 20049
void marshal_VkBufferCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkBufferCreateInfo* forMarshaling);

void unmarshal_VkBufferCreateInfo(
    VulkanStreamGuest* vkStream,
    VkBufferCreateInfo* forUnmarshaling);

#define OP_vkCreateBuffer 20050
#define OP_vkDestroyBuffer 20051
void marshal_VkBufferViewCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkBufferViewCreateInfo* forMarshaling);

void unmarshal_VkBufferViewCreateInfo(
    VulkanStreamGuest* vkStream,
    VkBufferViewCreateInfo* forUnmarshaling);

#define OP_vkCreateBufferView 20052
#define OP_vkDestroyBufferView 20053
void marshal_VkImageCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkImageCreateInfo* forMarshaling);

void unmarshal_VkImageCreateInfo(
    VulkanStreamGuest* vkStream,
    VkImageCreateInfo* forUnmarshaling);

#define OP_vkCreateImage 20054
#define OP_vkDestroyImage 20055
void marshal_VkSubresourceLayout(
    VulkanStreamGuest* vkStream,
    const VkSubresourceLayout* forMarshaling);

void unmarshal_VkSubresourceLayout(
    VulkanStreamGuest* vkStream,
    VkSubresourceLayout* forUnmarshaling);

#define OP_vkGetImageSubresourceLayout 20056
void marshal_VkComponentMapping(
    VulkanStreamGuest* vkStream,
    const VkComponentMapping* forMarshaling);

void unmarshal_VkComponentMapping(
    VulkanStreamGuest* vkStream,
    VkComponentMapping* forUnmarshaling);

void marshal_VkImageSubresourceRange(
    VulkanStreamGuest* vkStream,
    const VkImageSubresourceRange* forMarshaling);

void unmarshal_VkImageSubresourceRange(
    VulkanStreamGuest* vkStream,
    VkImageSubresourceRange* forUnmarshaling);

void marshal_VkImageViewCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkImageViewCreateInfo* forMarshaling);

void unmarshal_VkImageViewCreateInfo(
    VulkanStreamGuest* vkStream,
    VkImageViewCreateInfo* forUnmarshaling);

#define OP_vkCreateImageView 20057
#define OP_vkDestroyImageView 20058
void marshal_VkShaderModuleCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkShaderModuleCreateInfo* forMarshaling);

void unmarshal_VkShaderModuleCreateInfo(
    VulkanStreamGuest* vkStream,
    VkShaderModuleCreateInfo* forUnmarshaling);

#define OP_vkCreateShaderModule 20059
#define OP_vkDestroyShaderModule 20060
void marshal_VkPipelineCacheCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineCacheCreateInfo* forMarshaling);

void unmarshal_VkPipelineCacheCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineCacheCreateInfo* forUnmarshaling);

#define OP_vkCreatePipelineCache 20061
#define OP_vkDestroyPipelineCache 20062
#define OP_vkGetPipelineCacheData 20063
#define OP_vkMergePipelineCaches 20064
void marshal_VkSpecializationMapEntry(
    VulkanStreamGuest* vkStream,
    const VkSpecializationMapEntry* forMarshaling);

void unmarshal_VkSpecializationMapEntry(
    VulkanStreamGuest* vkStream,
    VkSpecializationMapEntry* forUnmarshaling);

void marshal_VkSpecializationInfo(
    VulkanStreamGuest* vkStream,
    const VkSpecializationInfo* forMarshaling);

void unmarshal_VkSpecializationInfo(
    VulkanStreamGuest* vkStream,
    VkSpecializationInfo* forUnmarshaling);

void marshal_VkPipelineShaderStageCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineShaderStageCreateInfo* forMarshaling);

void unmarshal_VkPipelineShaderStageCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineShaderStageCreateInfo* forUnmarshaling);

void marshal_VkVertexInputBindingDescription(
    VulkanStreamGuest* vkStream,
    const VkVertexInputBindingDescription* forMarshaling);

void unmarshal_VkVertexInputBindingDescription(
    VulkanStreamGuest* vkStream,
    VkVertexInputBindingDescription* forUnmarshaling);

void marshal_VkVertexInputAttributeDescription(
    VulkanStreamGuest* vkStream,
    const VkVertexInputAttributeDescription* forMarshaling);

void unmarshal_VkVertexInputAttributeDescription(
    VulkanStreamGuest* vkStream,
    VkVertexInputAttributeDescription* forUnmarshaling);

void marshal_VkPipelineVertexInputStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineVertexInputStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineVertexInputStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineVertexInputStateCreateInfo* forUnmarshaling);

void marshal_VkPipelineInputAssemblyStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineInputAssemblyStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineInputAssemblyStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineInputAssemblyStateCreateInfo* forUnmarshaling);

void marshal_VkPipelineTessellationStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineTessellationStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineTessellationStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineTessellationStateCreateInfo* forUnmarshaling);

void marshal_VkViewport(
    VulkanStreamGuest* vkStream,
    const VkViewport* forMarshaling);

void unmarshal_VkViewport(
    VulkanStreamGuest* vkStream,
    VkViewport* forUnmarshaling);

void marshal_VkOffset2D(
    VulkanStreamGuest* vkStream,
    const VkOffset2D* forMarshaling);

void unmarshal_VkOffset2D(
    VulkanStreamGuest* vkStream,
    VkOffset2D* forUnmarshaling);

void marshal_VkExtent2D(
    VulkanStreamGuest* vkStream,
    const VkExtent2D* forMarshaling);

void unmarshal_VkExtent2D(
    VulkanStreamGuest* vkStream,
    VkExtent2D* forUnmarshaling);

void marshal_VkRect2D(
    VulkanStreamGuest* vkStream,
    const VkRect2D* forMarshaling);

void unmarshal_VkRect2D(
    VulkanStreamGuest* vkStream,
    VkRect2D* forUnmarshaling);

void marshal_VkPipelineViewportStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineViewportStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineViewportStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineViewportStateCreateInfo* forUnmarshaling);

void marshal_VkPipelineRasterizationStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineRasterizationStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineRasterizationStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineRasterizationStateCreateInfo* forUnmarshaling);

void marshal_VkPipelineMultisampleStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineMultisampleStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineMultisampleStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineMultisampleStateCreateInfo* forUnmarshaling);

void marshal_VkStencilOpState(
    VulkanStreamGuest* vkStream,
    const VkStencilOpState* forMarshaling);

void unmarshal_VkStencilOpState(
    VulkanStreamGuest* vkStream,
    VkStencilOpState* forUnmarshaling);

void marshal_VkPipelineDepthStencilStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineDepthStencilStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineDepthStencilStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineDepthStencilStateCreateInfo* forUnmarshaling);

void marshal_VkPipelineColorBlendAttachmentState(
    VulkanStreamGuest* vkStream,
    const VkPipelineColorBlendAttachmentState* forMarshaling);

void unmarshal_VkPipelineColorBlendAttachmentState(
    VulkanStreamGuest* vkStream,
    VkPipelineColorBlendAttachmentState* forUnmarshaling);

void marshal_VkPipelineColorBlendStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineColorBlendStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineColorBlendStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineColorBlendStateCreateInfo* forUnmarshaling);

void marshal_VkPipelineDynamicStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineDynamicStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineDynamicStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineDynamicStateCreateInfo* forUnmarshaling);

void marshal_VkGraphicsPipelineCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkGraphicsPipelineCreateInfo* forMarshaling);

void unmarshal_VkGraphicsPipelineCreateInfo(
    VulkanStreamGuest* vkStream,
    VkGraphicsPipelineCreateInfo* forUnmarshaling);

#define OP_vkCreateGraphicsPipelines 20065
void marshal_VkComputePipelineCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkComputePipelineCreateInfo* forMarshaling);

void unmarshal_VkComputePipelineCreateInfo(
    VulkanStreamGuest* vkStream,
    VkComputePipelineCreateInfo* forUnmarshaling);

#define OP_vkCreateComputePipelines 20066
#define OP_vkDestroyPipeline 20067
void marshal_VkPushConstantRange(
    VulkanStreamGuest* vkStream,
    const VkPushConstantRange* forMarshaling);

void unmarshal_VkPushConstantRange(
    VulkanStreamGuest* vkStream,
    VkPushConstantRange* forUnmarshaling);

void marshal_VkPipelineLayoutCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineLayoutCreateInfo* forMarshaling);

void unmarshal_VkPipelineLayoutCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineLayoutCreateInfo* forUnmarshaling);

#define OP_vkCreatePipelineLayout 20068
#define OP_vkDestroyPipelineLayout 20069
void marshal_VkSamplerCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkSamplerCreateInfo* forMarshaling);

void unmarshal_VkSamplerCreateInfo(
    VulkanStreamGuest* vkStream,
    VkSamplerCreateInfo* forUnmarshaling);

#define OP_vkCreateSampler 20070
#define OP_vkDestroySampler 20071
void marshal_VkDescriptorSetLayoutBinding(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetLayoutBinding* forMarshaling);

void unmarshal_VkDescriptorSetLayoutBinding(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetLayoutBinding* forUnmarshaling);

void marshal_VkDescriptorSetLayoutCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetLayoutCreateInfo* forMarshaling);

void unmarshal_VkDescriptorSetLayoutCreateInfo(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetLayoutCreateInfo* forUnmarshaling);

#define OP_vkCreateDescriptorSetLayout 20072
#define OP_vkDestroyDescriptorSetLayout 20073
void marshal_VkDescriptorPoolSize(
    VulkanStreamGuest* vkStream,
    const VkDescriptorPoolSize* forMarshaling);

void unmarshal_VkDescriptorPoolSize(
    VulkanStreamGuest* vkStream,
    VkDescriptorPoolSize* forUnmarshaling);

void marshal_VkDescriptorPoolCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkDescriptorPoolCreateInfo* forMarshaling);

void unmarshal_VkDescriptorPoolCreateInfo(
    VulkanStreamGuest* vkStream,
    VkDescriptorPoolCreateInfo* forUnmarshaling);

#define OP_vkCreateDescriptorPool 20074
#define OP_vkDestroyDescriptorPool 20075
#define OP_vkResetDescriptorPool 20076
void marshal_VkDescriptorSetAllocateInfo(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetAllocateInfo* forMarshaling);

void unmarshal_VkDescriptorSetAllocateInfo(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetAllocateInfo* forUnmarshaling);

#define OP_vkAllocateDescriptorSets 20077
#define OP_vkFreeDescriptorSets 20078
void marshal_VkDescriptorImageInfo(
    VulkanStreamGuest* vkStream,
    const VkDescriptorImageInfo* forMarshaling);

void unmarshal_VkDescriptorImageInfo(
    VulkanStreamGuest* vkStream,
    VkDescriptorImageInfo* forUnmarshaling);

void marshal_VkDescriptorBufferInfo(
    VulkanStreamGuest* vkStream,
    const VkDescriptorBufferInfo* forMarshaling);

void unmarshal_VkDescriptorBufferInfo(
    VulkanStreamGuest* vkStream,
    VkDescriptorBufferInfo* forUnmarshaling);

void marshal_VkWriteDescriptorSet(
    VulkanStreamGuest* vkStream,
    const VkWriteDescriptorSet* forMarshaling);

void unmarshal_VkWriteDescriptorSet(
    VulkanStreamGuest* vkStream,
    VkWriteDescriptorSet* forUnmarshaling);

void marshal_VkCopyDescriptorSet(
    VulkanStreamGuest* vkStream,
    const VkCopyDescriptorSet* forMarshaling);

void unmarshal_VkCopyDescriptorSet(
    VulkanStreamGuest* vkStream,
    VkCopyDescriptorSet* forUnmarshaling);

#define OP_vkUpdateDescriptorSets 20079
void marshal_VkFramebufferCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkFramebufferCreateInfo* forMarshaling);

void unmarshal_VkFramebufferCreateInfo(
    VulkanStreamGuest* vkStream,
    VkFramebufferCreateInfo* forUnmarshaling);

#define OP_vkCreateFramebuffer 20080
#define OP_vkDestroyFramebuffer 20081
void marshal_VkAttachmentDescription(
    VulkanStreamGuest* vkStream,
    const VkAttachmentDescription* forMarshaling);

void unmarshal_VkAttachmentDescription(
    VulkanStreamGuest* vkStream,
    VkAttachmentDescription* forUnmarshaling);

void marshal_VkAttachmentReference(
    VulkanStreamGuest* vkStream,
    const VkAttachmentReference* forMarshaling);

void unmarshal_VkAttachmentReference(
    VulkanStreamGuest* vkStream,
    VkAttachmentReference* forUnmarshaling);

void marshal_VkSubpassDescription(
    VulkanStreamGuest* vkStream,
    const VkSubpassDescription* forMarshaling);

void unmarshal_VkSubpassDescription(
    VulkanStreamGuest* vkStream,
    VkSubpassDescription* forUnmarshaling);

void marshal_VkSubpassDependency(
    VulkanStreamGuest* vkStream,
    const VkSubpassDependency* forMarshaling);

void unmarshal_VkSubpassDependency(
    VulkanStreamGuest* vkStream,
    VkSubpassDependency* forUnmarshaling);

void marshal_VkRenderPassCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkRenderPassCreateInfo* forMarshaling);

void unmarshal_VkRenderPassCreateInfo(
    VulkanStreamGuest* vkStream,
    VkRenderPassCreateInfo* forUnmarshaling);

#define OP_vkCreateRenderPass 20082
#define OP_vkDestroyRenderPass 20083
#define OP_vkGetRenderAreaGranularity 20084
void marshal_VkCommandPoolCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkCommandPoolCreateInfo* forMarshaling);

void unmarshal_VkCommandPoolCreateInfo(
    VulkanStreamGuest* vkStream,
    VkCommandPoolCreateInfo* forUnmarshaling);

#define OP_vkCreateCommandPool 20085
#define OP_vkDestroyCommandPool 20086
#define OP_vkResetCommandPool 20087
void marshal_VkCommandBufferAllocateInfo(
    VulkanStreamGuest* vkStream,
    const VkCommandBufferAllocateInfo* forMarshaling);

void unmarshal_VkCommandBufferAllocateInfo(
    VulkanStreamGuest* vkStream,
    VkCommandBufferAllocateInfo* forUnmarshaling);

#define OP_vkAllocateCommandBuffers 20088
#define OP_vkFreeCommandBuffers 20089
void marshal_VkCommandBufferInheritanceInfo(
    VulkanStreamGuest* vkStream,
    const VkCommandBufferInheritanceInfo* forMarshaling);

void unmarshal_VkCommandBufferInheritanceInfo(
    VulkanStreamGuest* vkStream,
    VkCommandBufferInheritanceInfo* forUnmarshaling);

void marshal_VkCommandBufferBeginInfo(
    VulkanStreamGuest* vkStream,
    const VkCommandBufferBeginInfo* forMarshaling);

void unmarshal_VkCommandBufferBeginInfo(
    VulkanStreamGuest* vkStream,
    VkCommandBufferBeginInfo* forUnmarshaling);

#define OP_vkBeginCommandBuffer 20090
#define OP_vkEndCommandBuffer 20091
#define OP_vkResetCommandBuffer 20092
#define OP_vkCmdBindPipeline 20093
#define OP_vkCmdSetViewport 20094
#define OP_vkCmdSetScissor 20095
#define OP_vkCmdSetLineWidth 20096
#define OP_vkCmdSetDepthBias 20097
#define OP_vkCmdSetBlendConstants 20098
#define OP_vkCmdSetDepthBounds 20099
#define OP_vkCmdSetStencilCompareMask 20100
#define OP_vkCmdSetStencilWriteMask 20101
#define OP_vkCmdSetStencilReference 20102
#define OP_vkCmdBindDescriptorSets 20103
#define OP_vkCmdBindIndexBuffer 20104
#define OP_vkCmdBindVertexBuffers 20105
#define OP_vkCmdDraw 20106
#define OP_vkCmdDrawIndexed 20107
#define OP_vkCmdDrawIndirect 20108
#define OP_vkCmdDrawIndexedIndirect 20109
#define OP_vkCmdDispatch 20110
#define OP_vkCmdDispatchIndirect 20111
void marshal_VkBufferCopy(
    VulkanStreamGuest* vkStream,
    const VkBufferCopy* forMarshaling);

void unmarshal_VkBufferCopy(
    VulkanStreamGuest* vkStream,
    VkBufferCopy* forUnmarshaling);

#define OP_vkCmdCopyBuffer 20112
void marshal_VkImageSubresourceLayers(
    VulkanStreamGuest* vkStream,
    const VkImageSubresourceLayers* forMarshaling);

void unmarshal_VkImageSubresourceLayers(
    VulkanStreamGuest* vkStream,
    VkImageSubresourceLayers* forUnmarshaling);

void marshal_VkImageCopy(
    VulkanStreamGuest* vkStream,
    const VkImageCopy* forMarshaling);

void unmarshal_VkImageCopy(
    VulkanStreamGuest* vkStream,
    VkImageCopy* forUnmarshaling);

#define OP_vkCmdCopyImage 20113
void marshal_VkImageBlit(
    VulkanStreamGuest* vkStream,
    const VkImageBlit* forMarshaling);

void unmarshal_VkImageBlit(
    VulkanStreamGuest* vkStream,
    VkImageBlit* forUnmarshaling);

#define OP_vkCmdBlitImage 20114
void marshal_VkBufferImageCopy(
    VulkanStreamGuest* vkStream,
    const VkBufferImageCopy* forMarshaling);

void unmarshal_VkBufferImageCopy(
    VulkanStreamGuest* vkStream,
    VkBufferImageCopy* forUnmarshaling);

#define OP_vkCmdCopyBufferToImage 20115
#define OP_vkCmdCopyImageToBuffer 20116
#define OP_vkCmdUpdateBuffer 20117
#define OP_vkCmdFillBuffer 20118
void marshal_VkClearColorValue(
    VulkanStreamGuest* vkStream,
    const VkClearColorValue* forMarshaling);

void unmarshal_VkClearColorValue(
    VulkanStreamGuest* vkStream,
    VkClearColorValue* forUnmarshaling);

#define OP_vkCmdClearColorImage 20119
void marshal_VkClearDepthStencilValue(
    VulkanStreamGuest* vkStream,
    const VkClearDepthStencilValue* forMarshaling);

void unmarshal_VkClearDepthStencilValue(
    VulkanStreamGuest* vkStream,
    VkClearDepthStencilValue* forUnmarshaling);

#define OP_vkCmdClearDepthStencilImage 20120
void marshal_VkClearValue(
    VulkanStreamGuest* vkStream,
    const VkClearValue* forMarshaling);

void unmarshal_VkClearValue(
    VulkanStreamGuest* vkStream,
    VkClearValue* forUnmarshaling);

void marshal_VkClearAttachment(
    VulkanStreamGuest* vkStream,
    const VkClearAttachment* forMarshaling);

void unmarshal_VkClearAttachment(
    VulkanStreamGuest* vkStream,
    VkClearAttachment* forUnmarshaling);

void marshal_VkClearRect(
    VulkanStreamGuest* vkStream,
    const VkClearRect* forMarshaling);

void unmarshal_VkClearRect(
    VulkanStreamGuest* vkStream,
    VkClearRect* forUnmarshaling);

#define OP_vkCmdClearAttachments 20121
void marshal_VkImageResolve(
    VulkanStreamGuest* vkStream,
    const VkImageResolve* forMarshaling);

void unmarshal_VkImageResolve(
    VulkanStreamGuest* vkStream,
    VkImageResolve* forUnmarshaling);

#define OP_vkCmdResolveImage 20122
#define OP_vkCmdSetEvent 20123
#define OP_vkCmdResetEvent 20124
void marshal_VkMemoryBarrier(
    VulkanStreamGuest* vkStream,
    const VkMemoryBarrier* forMarshaling);

void unmarshal_VkMemoryBarrier(
    VulkanStreamGuest* vkStream,
    VkMemoryBarrier* forUnmarshaling);

void marshal_VkBufferMemoryBarrier(
    VulkanStreamGuest* vkStream,
    const VkBufferMemoryBarrier* forMarshaling);

void unmarshal_VkBufferMemoryBarrier(
    VulkanStreamGuest* vkStream,
    VkBufferMemoryBarrier* forUnmarshaling);

void marshal_VkImageMemoryBarrier(
    VulkanStreamGuest* vkStream,
    const VkImageMemoryBarrier* forMarshaling);

void unmarshal_VkImageMemoryBarrier(
    VulkanStreamGuest* vkStream,
    VkImageMemoryBarrier* forUnmarshaling);

#define OP_vkCmdWaitEvents 20125
#define OP_vkCmdPipelineBarrier 20126
#define OP_vkCmdBeginQuery 20127
#define OP_vkCmdEndQuery 20128
#define OP_vkCmdResetQueryPool 20129
#define OP_vkCmdWriteTimestamp 20130
#define OP_vkCmdCopyQueryPoolResults 20131
#define OP_vkCmdPushConstants 20132
void marshal_VkRenderPassBeginInfo(
    VulkanStreamGuest* vkStream,
    const VkRenderPassBeginInfo* forMarshaling);

void unmarshal_VkRenderPassBeginInfo(
    VulkanStreamGuest* vkStream,
    VkRenderPassBeginInfo* forUnmarshaling);

#define OP_vkCmdBeginRenderPass 20133
#define OP_vkCmdNextSubpass 20134
#define OP_vkCmdEndRenderPass 20135
#define OP_vkCmdExecuteCommands 20136
void marshal_VkDispatchIndirectCommand(
    VulkanStreamGuest* vkStream,
    const VkDispatchIndirectCommand* forMarshaling);

void unmarshal_VkDispatchIndirectCommand(
    VulkanStreamGuest* vkStream,
    VkDispatchIndirectCommand* forUnmarshaling);

void marshal_VkDrawIndexedIndirectCommand(
    VulkanStreamGuest* vkStream,
    const VkDrawIndexedIndirectCommand* forMarshaling);

void unmarshal_VkDrawIndexedIndirectCommand(
    VulkanStreamGuest* vkStream,
    VkDrawIndexedIndirectCommand* forUnmarshaling);

void marshal_VkDrawIndirectCommand(
    VulkanStreamGuest* vkStream,
    const VkDrawIndirectCommand* forMarshaling);

void unmarshal_VkDrawIndirectCommand(
    VulkanStreamGuest* vkStream,
    VkDrawIndirectCommand* forUnmarshaling);

void marshal_VkBaseOutStructure(
    VulkanStreamGuest* vkStream,
    const VkBaseOutStructure* forMarshaling);

void unmarshal_VkBaseOutStructure(
    VulkanStreamGuest* vkStream,
    VkBaseOutStructure* forUnmarshaling);

void marshal_VkBaseInStructure(
    VulkanStreamGuest* vkStream,
    const VkBaseInStructure* forMarshaling);

void unmarshal_VkBaseInStructure(
    VulkanStreamGuest* vkStream,
    VkBaseInStructure* forUnmarshaling);

#endif
#ifdef VK_VERSION_1_1
#define OP_vkEnumerateInstanceVersion 20137
void marshal_VkPhysicalDeviceSubgroupProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSubgroupProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceSubgroupProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSubgroupProperties* forUnmarshaling);

void marshal_VkBindBufferMemoryInfo(
    VulkanStreamGuest* vkStream,
    const VkBindBufferMemoryInfo* forMarshaling);

void unmarshal_VkBindBufferMemoryInfo(
    VulkanStreamGuest* vkStream,
    VkBindBufferMemoryInfo* forUnmarshaling);

void marshal_VkBindImageMemoryInfo(
    VulkanStreamGuest* vkStream,
    const VkBindImageMemoryInfo* forMarshaling);

void unmarshal_VkBindImageMemoryInfo(
    VulkanStreamGuest* vkStream,
    VkBindImageMemoryInfo* forUnmarshaling);

#define OP_vkBindBufferMemory2 20138
#define OP_vkBindImageMemory2 20139
void marshal_VkPhysicalDevice16BitStorageFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDevice16BitStorageFeatures* forMarshaling);

void unmarshal_VkPhysicalDevice16BitStorageFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDevice16BitStorageFeatures* forUnmarshaling);

void marshal_VkMemoryDedicatedRequirements(
    VulkanStreamGuest* vkStream,
    const VkMemoryDedicatedRequirements* forMarshaling);

void unmarshal_VkMemoryDedicatedRequirements(
    VulkanStreamGuest* vkStream,
    VkMemoryDedicatedRequirements* forUnmarshaling);

void marshal_VkMemoryDedicatedAllocateInfo(
    VulkanStreamGuest* vkStream,
    const VkMemoryDedicatedAllocateInfo* forMarshaling);

void unmarshal_VkMemoryDedicatedAllocateInfo(
    VulkanStreamGuest* vkStream,
    VkMemoryDedicatedAllocateInfo* forUnmarshaling);

void marshal_VkMemoryAllocateFlagsInfo(
    VulkanStreamGuest* vkStream,
    const VkMemoryAllocateFlagsInfo* forMarshaling);

void unmarshal_VkMemoryAllocateFlagsInfo(
    VulkanStreamGuest* vkStream,
    VkMemoryAllocateFlagsInfo* forUnmarshaling);

void marshal_VkDeviceGroupRenderPassBeginInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupRenderPassBeginInfo* forMarshaling);

void unmarshal_VkDeviceGroupRenderPassBeginInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupRenderPassBeginInfo* forUnmarshaling);

void marshal_VkDeviceGroupCommandBufferBeginInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupCommandBufferBeginInfo* forMarshaling);

void unmarshal_VkDeviceGroupCommandBufferBeginInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupCommandBufferBeginInfo* forUnmarshaling);

void marshal_VkDeviceGroupSubmitInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupSubmitInfo* forMarshaling);

void unmarshal_VkDeviceGroupSubmitInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupSubmitInfo* forUnmarshaling);

void marshal_VkDeviceGroupBindSparseInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupBindSparseInfo* forMarshaling);

void unmarshal_VkDeviceGroupBindSparseInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupBindSparseInfo* forUnmarshaling);

#define OP_vkGetDeviceGroupPeerMemoryFeatures 20140
#define OP_vkCmdSetDeviceMask 20141
#define OP_vkCmdDispatchBase 20142
void marshal_VkBindBufferMemoryDeviceGroupInfo(
    VulkanStreamGuest* vkStream,
    const VkBindBufferMemoryDeviceGroupInfo* forMarshaling);

void unmarshal_VkBindBufferMemoryDeviceGroupInfo(
    VulkanStreamGuest* vkStream,
    VkBindBufferMemoryDeviceGroupInfo* forUnmarshaling);

void marshal_VkBindImageMemoryDeviceGroupInfo(
    VulkanStreamGuest* vkStream,
    const VkBindImageMemoryDeviceGroupInfo* forMarshaling);

void unmarshal_VkBindImageMemoryDeviceGroupInfo(
    VulkanStreamGuest* vkStream,
    VkBindImageMemoryDeviceGroupInfo* forUnmarshaling);

void marshal_VkPhysicalDeviceGroupProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceGroupProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceGroupProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceGroupProperties* forUnmarshaling);

void marshal_VkDeviceGroupDeviceCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupDeviceCreateInfo* forMarshaling);

void unmarshal_VkDeviceGroupDeviceCreateInfo(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupDeviceCreateInfo* forUnmarshaling);

#define OP_vkEnumeratePhysicalDeviceGroups 20143
void marshal_VkBufferMemoryRequirementsInfo2(
    VulkanStreamGuest* vkStream,
    const VkBufferMemoryRequirementsInfo2* forMarshaling);

void unmarshal_VkBufferMemoryRequirementsInfo2(
    VulkanStreamGuest* vkStream,
    VkBufferMemoryRequirementsInfo2* forUnmarshaling);

void marshal_VkImageMemoryRequirementsInfo2(
    VulkanStreamGuest* vkStream,
    const VkImageMemoryRequirementsInfo2* forMarshaling);

void unmarshal_VkImageMemoryRequirementsInfo2(
    VulkanStreamGuest* vkStream,
    VkImageMemoryRequirementsInfo2* forUnmarshaling);

void marshal_VkImageSparseMemoryRequirementsInfo2(
    VulkanStreamGuest* vkStream,
    const VkImageSparseMemoryRequirementsInfo2* forMarshaling);

void unmarshal_VkImageSparseMemoryRequirementsInfo2(
    VulkanStreamGuest* vkStream,
    VkImageSparseMemoryRequirementsInfo2* forUnmarshaling);

void marshal_VkMemoryRequirements2(
    VulkanStreamGuest* vkStream,
    const VkMemoryRequirements2* forMarshaling);

void unmarshal_VkMemoryRequirements2(
    VulkanStreamGuest* vkStream,
    VkMemoryRequirements2* forUnmarshaling);

void marshal_VkSparseImageMemoryRequirements2(
    VulkanStreamGuest* vkStream,
    const VkSparseImageMemoryRequirements2* forMarshaling);

void unmarshal_VkSparseImageMemoryRequirements2(
    VulkanStreamGuest* vkStream,
    VkSparseImageMemoryRequirements2* forUnmarshaling);

#define OP_vkGetImageMemoryRequirements2 20144
#define OP_vkGetBufferMemoryRequirements2 20145
#define OP_vkGetImageSparseMemoryRequirements2 20146
void marshal_VkPhysicalDeviceFeatures2(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceFeatures2* forMarshaling);

void unmarshal_VkPhysicalDeviceFeatures2(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceFeatures2* forUnmarshaling);

void marshal_VkPhysicalDeviceProperties2(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceProperties2* forMarshaling);

void unmarshal_VkPhysicalDeviceProperties2(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceProperties2* forUnmarshaling);

void marshal_VkFormatProperties2(
    VulkanStreamGuest* vkStream,
    const VkFormatProperties2* forMarshaling);

void unmarshal_VkFormatProperties2(
    VulkanStreamGuest* vkStream,
    VkFormatProperties2* forUnmarshaling);

void marshal_VkImageFormatProperties2(
    VulkanStreamGuest* vkStream,
    const VkImageFormatProperties2* forMarshaling);

void unmarshal_VkImageFormatProperties2(
    VulkanStreamGuest* vkStream,
    VkImageFormatProperties2* forUnmarshaling);

void marshal_VkPhysicalDeviceImageFormatInfo2(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceImageFormatInfo2* forMarshaling);

void unmarshal_VkPhysicalDeviceImageFormatInfo2(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceImageFormatInfo2* forUnmarshaling);

void marshal_VkQueueFamilyProperties2(
    VulkanStreamGuest* vkStream,
    const VkQueueFamilyProperties2* forMarshaling);

void unmarshal_VkQueueFamilyProperties2(
    VulkanStreamGuest* vkStream,
    VkQueueFamilyProperties2* forUnmarshaling);

void marshal_VkPhysicalDeviceMemoryProperties2(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceMemoryProperties2* forMarshaling);

void unmarshal_VkPhysicalDeviceMemoryProperties2(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceMemoryProperties2* forUnmarshaling);

void marshal_VkSparseImageFormatProperties2(
    VulkanStreamGuest* vkStream,
    const VkSparseImageFormatProperties2* forMarshaling);

void unmarshal_VkSparseImageFormatProperties2(
    VulkanStreamGuest* vkStream,
    VkSparseImageFormatProperties2* forUnmarshaling);

void marshal_VkPhysicalDeviceSparseImageFormatInfo2(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSparseImageFormatInfo2* forMarshaling);

void unmarshal_VkPhysicalDeviceSparseImageFormatInfo2(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSparseImageFormatInfo2* forUnmarshaling);

#define OP_vkGetPhysicalDeviceFeatures2 20147
#define OP_vkGetPhysicalDeviceProperties2 20148
#define OP_vkGetPhysicalDeviceFormatProperties2 20149
#define OP_vkGetPhysicalDeviceImageFormatProperties2 20150
#define OP_vkGetPhysicalDeviceQueueFamilyProperties2 20151
#define OP_vkGetPhysicalDeviceMemoryProperties2 20152
#define OP_vkGetPhysicalDeviceSparseImageFormatProperties2 20153
#define OP_vkTrimCommandPool 20154
void marshal_VkPhysicalDevicePointClippingProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDevicePointClippingProperties* forMarshaling);

void unmarshal_VkPhysicalDevicePointClippingProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDevicePointClippingProperties* forUnmarshaling);

void marshal_VkInputAttachmentAspectReference(
    VulkanStreamGuest* vkStream,
    const VkInputAttachmentAspectReference* forMarshaling);

void unmarshal_VkInputAttachmentAspectReference(
    VulkanStreamGuest* vkStream,
    VkInputAttachmentAspectReference* forUnmarshaling);

void marshal_VkRenderPassInputAttachmentAspectCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkRenderPassInputAttachmentAspectCreateInfo* forMarshaling);

void unmarshal_VkRenderPassInputAttachmentAspectCreateInfo(
    VulkanStreamGuest* vkStream,
    VkRenderPassInputAttachmentAspectCreateInfo* forUnmarshaling);

void marshal_VkImageViewUsageCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkImageViewUsageCreateInfo* forMarshaling);

void unmarshal_VkImageViewUsageCreateInfo(
    VulkanStreamGuest* vkStream,
    VkImageViewUsageCreateInfo* forUnmarshaling);

void marshal_VkPipelineTessellationDomainOriginStateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkPipelineTessellationDomainOriginStateCreateInfo* forMarshaling);

void unmarshal_VkPipelineTessellationDomainOriginStateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkPipelineTessellationDomainOriginStateCreateInfo* forUnmarshaling);

void marshal_VkRenderPassMultiviewCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkRenderPassMultiviewCreateInfo* forMarshaling);

void unmarshal_VkRenderPassMultiviewCreateInfo(
    VulkanStreamGuest* vkStream,
    VkRenderPassMultiviewCreateInfo* forUnmarshaling);

void marshal_VkPhysicalDeviceMultiviewFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceMultiviewFeatures* forMarshaling);

void unmarshal_VkPhysicalDeviceMultiviewFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceMultiviewFeatures* forUnmarshaling);

void marshal_VkPhysicalDeviceMultiviewProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceMultiviewProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceMultiviewProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceMultiviewProperties* forUnmarshaling);

void marshal_VkPhysicalDeviceVariablePointerFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceVariablePointerFeatures* forMarshaling);

void unmarshal_VkPhysicalDeviceVariablePointerFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceVariablePointerFeatures* forUnmarshaling);

void marshal_VkPhysicalDeviceProtectedMemoryFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceProtectedMemoryFeatures* forMarshaling);

void unmarshal_VkPhysicalDeviceProtectedMemoryFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceProtectedMemoryFeatures* forUnmarshaling);

void marshal_VkPhysicalDeviceProtectedMemoryProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceProtectedMemoryProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceProtectedMemoryProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceProtectedMemoryProperties* forUnmarshaling);

void marshal_VkDeviceQueueInfo2(
    VulkanStreamGuest* vkStream,
    const VkDeviceQueueInfo2* forMarshaling);

void unmarshal_VkDeviceQueueInfo2(
    VulkanStreamGuest* vkStream,
    VkDeviceQueueInfo2* forUnmarshaling);

void marshal_VkProtectedSubmitInfo(
    VulkanStreamGuest* vkStream,
    const VkProtectedSubmitInfo* forMarshaling);

void unmarshal_VkProtectedSubmitInfo(
    VulkanStreamGuest* vkStream,
    VkProtectedSubmitInfo* forUnmarshaling);

#define OP_vkGetDeviceQueue2 20155
void marshal_VkSamplerYcbcrConversionCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkSamplerYcbcrConversionCreateInfo* forMarshaling);

void unmarshal_VkSamplerYcbcrConversionCreateInfo(
    VulkanStreamGuest* vkStream,
    VkSamplerYcbcrConversionCreateInfo* forUnmarshaling);

void marshal_VkSamplerYcbcrConversionInfo(
    VulkanStreamGuest* vkStream,
    const VkSamplerYcbcrConversionInfo* forMarshaling);

void unmarshal_VkSamplerYcbcrConversionInfo(
    VulkanStreamGuest* vkStream,
    VkSamplerYcbcrConversionInfo* forUnmarshaling);

void marshal_VkBindImagePlaneMemoryInfo(
    VulkanStreamGuest* vkStream,
    const VkBindImagePlaneMemoryInfo* forMarshaling);

void unmarshal_VkBindImagePlaneMemoryInfo(
    VulkanStreamGuest* vkStream,
    VkBindImagePlaneMemoryInfo* forUnmarshaling);

void marshal_VkImagePlaneMemoryRequirementsInfo(
    VulkanStreamGuest* vkStream,
    const VkImagePlaneMemoryRequirementsInfo* forMarshaling);

void unmarshal_VkImagePlaneMemoryRequirementsInfo(
    VulkanStreamGuest* vkStream,
    VkImagePlaneMemoryRequirementsInfo* forUnmarshaling);

void marshal_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSamplerYcbcrConversionFeatures* forMarshaling);

void unmarshal_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* forUnmarshaling);

void marshal_VkSamplerYcbcrConversionImageFormatProperties(
    VulkanStreamGuest* vkStream,
    const VkSamplerYcbcrConversionImageFormatProperties* forMarshaling);

void unmarshal_VkSamplerYcbcrConversionImageFormatProperties(
    VulkanStreamGuest* vkStream,
    VkSamplerYcbcrConversionImageFormatProperties* forUnmarshaling);

#define OP_vkCreateSamplerYcbcrConversion 20156
#define OP_vkDestroySamplerYcbcrConversion 20157
void marshal_VkDescriptorUpdateTemplateEntry(
    VulkanStreamGuest* vkStream,
    const VkDescriptorUpdateTemplateEntry* forMarshaling);

void unmarshal_VkDescriptorUpdateTemplateEntry(
    VulkanStreamGuest* vkStream,
    VkDescriptorUpdateTemplateEntry* forUnmarshaling);

void marshal_VkDescriptorUpdateTemplateCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkDescriptorUpdateTemplateCreateInfo* forMarshaling);

void unmarshal_VkDescriptorUpdateTemplateCreateInfo(
    VulkanStreamGuest* vkStream,
    VkDescriptorUpdateTemplateCreateInfo* forUnmarshaling);

#define OP_vkCreateDescriptorUpdateTemplate 20158
#define OP_vkDestroyDescriptorUpdateTemplate 20159
#define OP_vkUpdateDescriptorSetWithTemplate 20160
void marshal_VkExternalMemoryProperties(
    VulkanStreamGuest* vkStream,
    const VkExternalMemoryProperties* forMarshaling);

void unmarshal_VkExternalMemoryProperties(
    VulkanStreamGuest* vkStream,
    VkExternalMemoryProperties* forUnmarshaling);

void marshal_VkPhysicalDeviceExternalImageFormatInfo(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceExternalImageFormatInfo* forMarshaling);

void unmarshal_VkPhysicalDeviceExternalImageFormatInfo(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceExternalImageFormatInfo* forUnmarshaling);

void marshal_VkExternalImageFormatProperties(
    VulkanStreamGuest* vkStream,
    const VkExternalImageFormatProperties* forMarshaling);

void unmarshal_VkExternalImageFormatProperties(
    VulkanStreamGuest* vkStream,
    VkExternalImageFormatProperties* forUnmarshaling);

void marshal_VkPhysicalDeviceExternalBufferInfo(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceExternalBufferInfo* forMarshaling);

void unmarshal_VkPhysicalDeviceExternalBufferInfo(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceExternalBufferInfo* forUnmarshaling);

void marshal_VkExternalBufferProperties(
    VulkanStreamGuest* vkStream,
    const VkExternalBufferProperties* forMarshaling);

void unmarshal_VkExternalBufferProperties(
    VulkanStreamGuest* vkStream,
    VkExternalBufferProperties* forUnmarshaling);

void marshal_VkPhysicalDeviceIDProperties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceIDProperties* forMarshaling);

void unmarshal_VkPhysicalDeviceIDProperties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceIDProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceExternalBufferProperties 20161
void marshal_VkExternalMemoryImageCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkExternalMemoryImageCreateInfo* forMarshaling);

void unmarshal_VkExternalMemoryImageCreateInfo(
    VulkanStreamGuest* vkStream,
    VkExternalMemoryImageCreateInfo* forUnmarshaling);

void marshal_VkExternalMemoryBufferCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkExternalMemoryBufferCreateInfo* forMarshaling);

void unmarshal_VkExternalMemoryBufferCreateInfo(
    VulkanStreamGuest* vkStream,
    VkExternalMemoryBufferCreateInfo* forUnmarshaling);

void marshal_VkExportMemoryAllocateInfo(
    VulkanStreamGuest* vkStream,
    const VkExportMemoryAllocateInfo* forMarshaling);

void unmarshal_VkExportMemoryAllocateInfo(
    VulkanStreamGuest* vkStream,
    VkExportMemoryAllocateInfo* forUnmarshaling);

void marshal_VkPhysicalDeviceExternalFenceInfo(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceExternalFenceInfo* forMarshaling);

void unmarshal_VkPhysicalDeviceExternalFenceInfo(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceExternalFenceInfo* forUnmarshaling);

void marshal_VkExternalFenceProperties(
    VulkanStreamGuest* vkStream,
    const VkExternalFenceProperties* forMarshaling);

void unmarshal_VkExternalFenceProperties(
    VulkanStreamGuest* vkStream,
    VkExternalFenceProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceExternalFenceProperties 20162
void marshal_VkExportFenceCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkExportFenceCreateInfo* forMarshaling);

void unmarshal_VkExportFenceCreateInfo(
    VulkanStreamGuest* vkStream,
    VkExportFenceCreateInfo* forUnmarshaling);

void marshal_VkExportSemaphoreCreateInfo(
    VulkanStreamGuest* vkStream,
    const VkExportSemaphoreCreateInfo* forMarshaling);

void unmarshal_VkExportSemaphoreCreateInfo(
    VulkanStreamGuest* vkStream,
    VkExportSemaphoreCreateInfo* forUnmarshaling);

void marshal_VkPhysicalDeviceExternalSemaphoreInfo(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceExternalSemaphoreInfo* forMarshaling);

void unmarshal_VkPhysicalDeviceExternalSemaphoreInfo(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceExternalSemaphoreInfo* forUnmarshaling);

void marshal_VkExternalSemaphoreProperties(
    VulkanStreamGuest* vkStream,
    const VkExternalSemaphoreProperties* forMarshaling);

void unmarshal_VkExternalSemaphoreProperties(
    VulkanStreamGuest* vkStream,
    VkExternalSemaphoreProperties* forUnmarshaling);

#define OP_vkGetPhysicalDeviceExternalSemaphoreProperties 20163
void marshal_VkPhysicalDeviceMaintenance3Properties(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceMaintenance3Properties* forMarshaling);

void unmarshal_VkPhysicalDeviceMaintenance3Properties(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceMaintenance3Properties* forUnmarshaling);

void marshal_VkDescriptorSetLayoutSupport(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetLayoutSupport* forMarshaling);

void unmarshal_VkDescriptorSetLayoutSupport(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetLayoutSupport* forUnmarshaling);

#define OP_vkGetDescriptorSetLayoutSupport 20164
void marshal_VkPhysicalDeviceShaderDrawParameterFeatures(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceShaderDrawParameterFeatures* forMarshaling);

void unmarshal_VkPhysicalDeviceShaderDrawParameterFeatures(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceShaderDrawParameterFeatures* forUnmarshaling);

#endif
#ifdef VK_KHR_surface
#define OP_vkDestroySurfaceKHR 20165
#define OP_vkGetPhysicalDeviceSurfaceSupportKHR 20166
void marshal_VkSurfaceCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    const VkSurfaceCapabilitiesKHR* forMarshaling);

void unmarshal_VkSurfaceCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    VkSurfaceCapabilitiesKHR* forUnmarshaling);

#define OP_vkGetPhysicalDeviceSurfaceCapabilitiesKHR 20167
void marshal_VkSurfaceFormatKHR(
    VulkanStreamGuest* vkStream,
    const VkSurfaceFormatKHR* forMarshaling);

void unmarshal_VkSurfaceFormatKHR(
    VulkanStreamGuest* vkStream,
    VkSurfaceFormatKHR* forUnmarshaling);

#define OP_vkGetPhysicalDeviceSurfaceFormatsKHR 20168
#define OP_vkGetPhysicalDeviceSurfacePresentModesKHR 20169
#endif
#ifdef VK_KHR_swapchain
void marshal_VkSwapchainCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkSwapchainCreateInfoKHR* forMarshaling);

void unmarshal_VkSwapchainCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkSwapchainCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateSwapchainKHR 20170
#define OP_vkDestroySwapchainKHR 20171
#define OP_vkGetSwapchainImagesKHR 20172
#define OP_vkAcquireNextImageKHR 20173
void marshal_VkPresentInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkPresentInfoKHR* forMarshaling);

void unmarshal_VkPresentInfoKHR(
    VulkanStreamGuest* vkStream,
    VkPresentInfoKHR* forUnmarshaling);

#define OP_vkQueuePresentKHR 20174
void marshal_VkImageSwapchainCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImageSwapchainCreateInfoKHR* forMarshaling);

void unmarshal_VkImageSwapchainCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImageSwapchainCreateInfoKHR* forUnmarshaling);

void marshal_VkBindImageMemorySwapchainInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkBindImageMemorySwapchainInfoKHR* forMarshaling);

void unmarshal_VkBindImageMemorySwapchainInfoKHR(
    VulkanStreamGuest* vkStream,
    VkBindImageMemorySwapchainInfoKHR* forUnmarshaling);

void marshal_VkAcquireNextImageInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkAcquireNextImageInfoKHR* forMarshaling);

void unmarshal_VkAcquireNextImageInfoKHR(
    VulkanStreamGuest* vkStream,
    VkAcquireNextImageInfoKHR* forUnmarshaling);

void marshal_VkDeviceGroupPresentCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupPresentCapabilitiesKHR* forMarshaling);

void unmarshal_VkDeviceGroupPresentCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupPresentCapabilitiesKHR* forUnmarshaling);

void marshal_VkDeviceGroupPresentInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupPresentInfoKHR* forMarshaling);

void unmarshal_VkDeviceGroupPresentInfoKHR(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupPresentInfoKHR* forUnmarshaling);

void marshal_VkDeviceGroupSwapchainCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkDeviceGroupSwapchainCreateInfoKHR* forMarshaling);

void unmarshal_VkDeviceGroupSwapchainCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkDeviceGroupSwapchainCreateInfoKHR* forUnmarshaling);

#define OP_vkGetDeviceGroupPresentCapabilitiesKHR 20175
#define OP_vkGetDeviceGroupSurfacePresentModesKHR 20176
#define OP_vkGetPhysicalDevicePresentRectanglesKHR 20177
#define OP_vkAcquireNextImage2KHR 20178
#endif
#ifdef VK_KHR_display
void marshal_VkDisplayPropertiesKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPropertiesKHR* forMarshaling);

void unmarshal_VkDisplayPropertiesKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPropertiesKHR* forUnmarshaling);

void marshal_VkDisplayModeParametersKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayModeParametersKHR* forMarshaling);

void unmarshal_VkDisplayModeParametersKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayModeParametersKHR* forUnmarshaling);

void marshal_VkDisplayModePropertiesKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayModePropertiesKHR* forMarshaling);

void unmarshal_VkDisplayModePropertiesKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayModePropertiesKHR* forUnmarshaling);

void marshal_VkDisplayModeCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayModeCreateInfoKHR* forMarshaling);

void unmarshal_VkDisplayModeCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayModeCreateInfoKHR* forUnmarshaling);

void marshal_VkDisplayPlaneCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPlaneCapabilitiesKHR* forMarshaling);

void unmarshal_VkDisplayPlaneCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPlaneCapabilitiesKHR* forUnmarshaling);

void marshal_VkDisplayPlanePropertiesKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPlanePropertiesKHR* forMarshaling);

void unmarshal_VkDisplayPlanePropertiesKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPlanePropertiesKHR* forUnmarshaling);

void marshal_VkDisplaySurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplaySurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkDisplaySurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkDisplaySurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkGetPhysicalDeviceDisplayPropertiesKHR 20179
#define OP_vkGetPhysicalDeviceDisplayPlanePropertiesKHR 20180
#define OP_vkGetDisplayPlaneSupportedDisplaysKHR 20181
#define OP_vkGetDisplayModePropertiesKHR 20182
#define OP_vkCreateDisplayModeKHR 20183
#define OP_vkGetDisplayPlaneCapabilitiesKHR 20184
#define OP_vkCreateDisplayPlaneSurfaceKHR 20185
#endif
#ifdef VK_KHR_display_swapchain
void marshal_VkDisplayPresentInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPresentInfoKHR* forMarshaling);

void unmarshal_VkDisplayPresentInfoKHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPresentInfoKHR* forUnmarshaling);

#define OP_vkCreateSharedSwapchainsKHR 20186
#endif
#ifdef VK_KHR_xlib_surface
void marshal_VkXlibSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkXlibSurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkXlibSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkXlibSurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateXlibSurfaceKHR 20187
#define OP_vkGetPhysicalDeviceXlibPresentationSupportKHR 20188
#endif
#ifdef VK_KHR_xcb_surface
void marshal_VkXcbSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkXcbSurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkXcbSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkXcbSurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateXcbSurfaceKHR 20189
#define OP_vkGetPhysicalDeviceXcbPresentationSupportKHR 20190
#endif
#ifdef VK_KHR_wayland_surface
void marshal_VkWaylandSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkWaylandSurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkWaylandSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkWaylandSurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateWaylandSurfaceKHR 20191
#define OP_vkGetPhysicalDeviceWaylandPresentationSupportKHR 20192
#endif
#ifdef VK_KHR_mir_surface
void marshal_VkMirSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkMirSurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkMirSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkMirSurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateMirSurfaceKHR 20193
#define OP_vkGetPhysicalDeviceMirPresentationSupportKHR 20194
#endif
#ifdef VK_KHR_android_surface
void marshal_VkAndroidSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkAndroidSurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkAndroidSurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkAndroidSurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateAndroidSurfaceKHR 20195
#endif
#ifdef VK_KHR_win32_surface
void marshal_VkWin32SurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkWin32SurfaceCreateInfoKHR* forMarshaling);

void unmarshal_VkWin32SurfaceCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkWin32SurfaceCreateInfoKHR* forUnmarshaling);

#define OP_vkCreateWin32SurfaceKHR 20196
#define OP_vkGetPhysicalDeviceWin32PresentationSupportKHR 20197
#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#define OP_vkGetPhysicalDeviceFeatures2KHR 20198
#define OP_vkGetPhysicalDeviceProperties2KHR 20199
#define OP_vkGetPhysicalDeviceFormatProperties2KHR 20200
#define OP_vkGetPhysicalDeviceImageFormatProperties2KHR 20201
#define OP_vkGetPhysicalDeviceQueueFamilyProperties2KHR 20202
#define OP_vkGetPhysicalDeviceMemoryProperties2KHR 20203
#define OP_vkGetPhysicalDeviceSparseImageFormatProperties2KHR 20204
#endif
#ifdef VK_KHR_device_group
#define OP_vkGetDeviceGroupPeerMemoryFeaturesKHR 20205
#define OP_vkCmdSetDeviceMaskKHR 20206
#define OP_vkCmdDispatchBaseKHR 20207
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#define OP_vkTrimCommandPoolKHR 20208
#endif
#ifdef VK_KHR_device_group_creation
#define OP_vkEnumeratePhysicalDeviceGroupsKHR 20209
#endif
#ifdef VK_KHR_external_memory_capabilities
#define OP_vkGetPhysicalDeviceExternalBufferPropertiesKHR 20210
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void marshal_VkImportMemoryWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImportMemoryWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkImportMemoryWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImportMemoryWin32HandleInfoKHR* forUnmarshaling);

void marshal_VkExportMemoryWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkExportMemoryWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkExportMemoryWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkExportMemoryWin32HandleInfoKHR* forUnmarshaling);

void marshal_VkMemoryWin32HandlePropertiesKHR(
    VulkanStreamGuest* vkStream,
    const VkMemoryWin32HandlePropertiesKHR* forMarshaling);

void unmarshal_VkMemoryWin32HandlePropertiesKHR(
    VulkanStreamGuest* vkStream,
    VkMemoryWin32HandlePropertiesKHR* forUnmarshaling);

void marshal_VkMemoryGetWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkMemoryGetWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkMemoryGetWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkMemoryGetWin32HandleInfoKHR* forUnmarshaling);

#define OP_vkGetMemoryWin32HandleKHR 20211
#define OP_vkGetMemoryWin32HandlePropertiesKHR 20212
#endif
#ifdef VK_KHR_external_memory_fd
void marshal_VkImportMemoryFdInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImportMemoryFdInfoKHR* forMarshaling);

void unmarshal_VkImportMemoryFdInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImportMemoryFdInfoKHR* forUnmarshaling);

void marshal_VkMemoryFdPropertiesKHR(
    VulkanStreamGuest* vkStream,
    const VkMemoryFdPropertiesKHR* forMarshaling);

void unmarshal_VkMemoryFdPropertiesKHR(
    VulkanStreamGuest* vkStream,
    VkMemoryFdPropertiesKHR* forUnmarshaling);

void marshal_VkMemoryGetFdInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkMemoryGetFdInfoKHR* forMarshaling);

void unmarshal_VkMemoryGetFdInfoKHR(
    VulkanStreamGuest* vkStream,
    VkMemoryGetFdInfoKHR* forUnmarshaling);

#define OP_vkGetMemoryFdKHR 20213
#define OP_vkGetMemoryFdPropertiesKHR 20214
#endif
#ifdef VK_KHR_win32_keyed_mutex
void marshal_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkWin32KeyedMutexAcquireReleaseInfoKHR* forMarshaling);

void unmarshal_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    VulkanStreamGuest* vkStream,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* forUnmarshaling);

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#define OP_vkGetPhysicalDeviceExternalSemaphorePropertiesKHR 20215
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void marshal_VkImportSemaphoreWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImportSemaphoreWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkImportSemaphoreWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImportSemaphoreWin32HandleInfoKHR* forUnmarshaling);

void marshal_VkExportSemaphoreWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkExportSemaphoreWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkExportSemaphoreWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkExportSemaphoreWin32HandleInfoKHR* forUnmarshaling);

void marshal_VkD3D12FenceSubmitInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkD3D12FenceSubmitInfoKHR* forMarshaling);

void unmarshal_VkD3D12FenceSubmitInfoKHR(
    VulkanStreamGuest* vkStream,
    VkD3D12FenceSubmitInfoKHR* forUnmarshaling);

void marshal_VkSemaphoreGetWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkSemaphoreGetWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkSemaphoreGetWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkSemaphoreGetWin32HandleInfoKHR* forUnmarshaling);

#define OP_vkImportSemaphoreWin32HandleKHR 20216
#define OP_vkGetSemaphoreWin32HandleKHR 20217
#endif
#ifdef VK_KHR_external_semaphore_fd
void marshal_VkImportSemaphoreFdInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImportSemaphoreFdInfoKHR* forMarshaling);

void unmarshal_VkImportSemaphoreFdInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImportSemaphoreFdInfoKHR* forUnmarshaling);

void marshal_VkSemaphoreGetFdInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkSemaphoreGetFdInfoKHR* forMarshaling);

void unmarshal_VkSemaphoreGetFdInfoKHR(
    VulkanStreamGuest* vkStream,
    VkSemaphoreGetFdInfoKHR* forUnmarshaling);

#define OP_vkImportSemaphoreFdKHR 20218
#define OP_vkGetSemaphoreFdKHR 20219
#endif
#ifdef VK_KHR_push_descriptor
void marshal_VkPhysicalDevicePushDescriptorPropertiesKHR(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDevicePushDescriptorPropertiesKHR* forMarshaling);

void unmarshal_VkPhysicalDevicePushDescriptorPropertiesKHR(
    VulkanStreamGuest* vkStream,
    VkPhysicalDevicePushDescriptorPropertiesKHR* forUnmarshaling);

#define OP_vkCmdPushDescriptorSetKHR 20220
#define OP_vkCmdPushDescriptorSetWithTemplateKHR 20221
#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void marshal_VkRectLayerKHR(
    VulkanStreamGuest* vkStream,
    const VkRectLayerKHR* forMarshaling);

void unmarshal_VkRectLayerKHR(
    VulkanStreamGuest* vkStream,
    VkRectLayerKHR* forUnmarshaling);

void marshal_VkPresentRegionKHR(
    VulkanStreamGuest* vkStream,
    const VkPresentRegionKHR* forMarshaling);

void unmarshal_VkPresentRegionKHR(
    VulkanStreamGuest* vkStream,
    VkPresentRegionKHR* forUnmarshaling);

void marshal_VkPresentRegionsKHR(
    VulkanStreamGuest* vkStream,
    const VkPresentRegionsKHR* forMarshaling);

void unmarshal_VkPresentRegionsKHR(
    VulkanStreamGuest* vkStream,
    VkPresentRegionsKHR* forUnmarshaling);

#endif
#ifdef VK_KHR_descriptor_update_template
#define OP_vkCreateDescriptorUpdateTemplateKHR 20222
#define OP_vkDestroyDescriptorUpdateTemplateKHR 20223
#define OP_vkUpdateDescriptorSetWithTemplateKHR 20224
#endif
#ifdef VK_KHR_create_renderpass2
void marshal_VkAttachmentDescription2KHR(
    VulkanStreamGuest* vkStream,
    const VkAttachmentDescription2KHR* forMarshaling);

void unmarshal_VkAttachmentDescription2KHR(
    VulkanStreamGuest* vkStream,
    VkAttachmentDescription2KHR* forUnmarshaling);

void marshal_VkAttachmentReference2KHR(
    VulkanStreamGuest* vkStream,
    const VkAttachmentReference2KHR* forMarshaling);

void unmarshal_VkAttachmentReference2KHR(
    VulkanStreamGuest* vkStream,
    VkAttachmentReference2KHR* forUnmarshaling);

void marshal_VkSubpassDescription2KHR(
    VulkanStreamGuest* vkStream,
    const VkSubpassDescription2KHR* forMarshaling);

void unmarshal_VkSubpassDescription2KHR(
    VulkanStreamGuest* vkStream,
    VkSubpassDescription2KHR* forUnmarshaling);

void marshal_VkSubpassDependency2KHR(
    VulkanStreamGuest* vkStream,
    const VkSubpassDependency2KHR* forMarshaling);

void unmarshal_VkSubpassDependency2KHR(
    VulkanStreamGuest* vkStream,
    VkSubpassDependency2KHR* forUnmarshaling);

void marshal_VkRenderPassCreateInfo2KHR(
    VulkanStreamGuest* vkStream,
    const VkRenderPassCreateInfo2KHR* forMarshaling);

void unmarshal_VkRenderPassCreateInfo2KHR(
    VulkanStreamGuest* vkStream,
    VkRenderPassCreateInfo2KHR* forUnmarshaling);

#define OP_vkCreateRenderPass2KHR 20225
void marshal_VkSubpassBeginInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkSubpassBeginInfoKHR* forMarshaling);

void unmarshal_VkSubpassBeginInfoKHR(
    VulkanStreamGuest* vkStream,
    VkSubpassBeginInfoKHR* forUnmarshaling);

#define OP_vkCmdBeginRenderPass2KHR 20226
void marshal_VkSubpassEndInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkSubpassEndInfoKHR* forMarshaling);

void unmarshal_VkSubpassEndInfoKHR(
    VulkanStreamGuest* vkStream,
    VkSubpassEndInfoKHR* forUnmarshaling);

#define OP_vkCmdNextSubpass2KHR 20227
#define OP_vkCmdEndRenderPass2KHR 20228
#endif
#ifdef VK_KHR_shared_presentable_image
void marshal_VkSharedPresentSurfaceCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    const VkSharedPresentSurfaceCapabilitiesKHR* forMarshaling);

void unmarshal_VkSharedPresentSurfaceCapabilitiesKHR(
    VulkanStreamGuest* vkStream,
    VkSharedPresentSurfaceCapabilitiesKHR* forUnmarshaling);

#define OP_vkGetSwapchainStatusKHR 20229
#endif
#ifdef VK_KHR_external_fence_capabilities
#define OP_vkGetPhysicalDeviceExternalFencePropertiesKHR 20230
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void marshal_VkImportFenceWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImportFenceWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkImportFenceWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImportFenceWin32HandleInfoKHR* forUnmarshaling);

void marshal_VkExportFenceWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkExportFenceWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkExportFenceWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkExportFenceWin32HandleInfoKHR* forUnmarshaling);

void marshal_VkFenceGetWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkFenceGetWin32HandleInfoKHR* forMarshaling);

void unmarshal_VkFenceGetWin32HandleInfoKHR(
    VulkanStreamGuest* vkStream,
    VkFenceGetWin32HandleInfoKHR* forUnmarshaling);

#define OP_vkImportFenceWin32HandleKHR 20231
#define OP_vkGetFenceWin32HandleKHR 20232
#endif
#ifdef VK_KHR_external_fence_fd
void marshal_VkImportFenceFdInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImportFenceFdInfoKHR* forMarshaling);

void unmarshal_VkImportFenceFdInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImportFenceFdInfoKHR* forUnmarshaling);

void marshal_VkFenceGetFdInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkFenceGetFdInfoKHR* forMarshaling);

void unmarshal_VkFenceGetFdInfoKHR(
    VulkanStreamGuest* vkStream,
    VkFenceGetFdInfoKHR* forUnmarshaling);

#define OP_vkImportFenceFdKHR 20233
#define OP_vkGetFenceFdKHR 20234
#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void marshal_VkPhysicalDeviceSurfaceInfo2KHR(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSurfaceInfo2KHR* forMarshaling);

void unmarshal_VkPhysicalDeviceSurfaceInfo2KHR(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSurfaceInfo2KHR* forUnmarshaling);

void marshal_VkSurfaceCapabilities2KHR(
    VulkanStreamGuest* vkStream,
    const VkSurfaceCapabilities2KHR* forMarshaling);

void unmarshal_VkSurfaceCapabilities2KHR(
    VulkanStreamGuest* vkStream,
    VkSurfaceCapabilities2KHR* forUnmarshaling);

void marshal_VkSurfaceFormat2KHR(
    VulkanStreamGuest* vkStream,
    const VkSurfaceFormat2KHR* forMarshaling);

void unmarshal_VkSurfaceFormat2KHR(
    VulkanStreamGuest* vkStream,
    VkSurfaceFormat2KHR* forUnmarshaling);

#define OP_vkGetPhysicalDeviceSurfaceCapabilities2KHR 20235
#define OP_vkGetPhysicalDeviceSurfaceFormats2KHR 20236
#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void marshal_VkDisplayProperties2KHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayProperties2KHR* forMarshaling);

void unmarshal_VkDisplayProperties2KHR(
    VulkanStreamGuest* vkStream,
    VkDisplayProperties2KHR* forUnmarshaling);

void marshal_VkDisplayPlaneProperties2KHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPlaneProperties2KHR* forMarshaling);

void unmarshal_VkDisplayPlaneProperties2KHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPlaneProperties2KHR* forUnmarshaling);

void marshal_VkDisplayModeProperties2KHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayModeProperties2KHR* forMarshaling);

void unmarshal_VkDisplayModeProperties2KHR(
    VulkanStreamGuest* vkStream,
    VkDisplayModeProperties2KHR* forUnmarshaling);

void marshal_VkDisplayPlaneInfo2KHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPlaneInfo2KHR* forMarshaling);

void unmarshal_VkDisplayPlaneInfo2KHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPlaneInfo2KHR* forUnmarshaling);

void marshal_VkDisplayPlaneCapabilities2KHR(
    VulkanStreamGuest* vkStream,
    const VkDisplayPlaneCapabilities2KHR* forMarshaling);

void unmarshal_VkDisplayPlaneCapabilities2KHR(
    VulkanStreamGuest* vkStream,
    VkDisplayPlaneCapabilities2KHR* forUnmarshaling);

#define OP_vkGetPhysicalDeviceDisplayProperties2KHR 20237
#define OP_vkGetPhysicalDeviceDisplayPlaneProperties2KHR 20238
#define OP_vkGetDisplayModeProperties2KHR 20239
#define OP_vkGetDisplayPlaneCapabilities2KHR 20240
#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#define OP_vkGetImageMemoryRequirements2KHR 20241
#define OP_vkGetBufferMemoryRequirements2KHR 20242
#define OP_vkGetImageSparseMemoryRequirements2KHR 20243
#endif
#ifdef VK_KHR_image_format_list
void marshal_VkImageFormatListCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    const VkImageFormatListCreateInfoKHR* forMarshaling);

void unmarshal_VkImageFormatListCreateInfoKHR(
    VulkanStreamGuest* vkStream,
    VkImageFormatListCreateInfoKHR* forUnmarshaling);

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#define OP_vkCreateSamplerYcbcrConversionKHR 20244
#define OP_vkDestroySamplerYcbcrConversionKHR 20245
#endif
#ifdef VK_KHR_bind_memory2
#define OP_vkBindBufferMemory2KHR 20246
#define OP_vkBindImageMemory2KHR 20247
#endif
#ifdef VK_KHR_maintenance3
#define OP_vkGetDescriptorSetLayoutSupportKHR 20248
#endif
#ifdef VK_KHR_draw_indirect_count
#define OP_vkCmdDrawIndirectCountKHR 20249
#define OP_vkCmdDrawIndexedIndirectCountKHR 20250
#endif
#ifdef VK_KHR_8bit_storage
void marshal_VkPhysicalDevice8BitStorageFeaturesKHR(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDevice8BitStorageFeaturesKHR* forMarshaling);

void unmarshal_VkPhysicalDevice8BitStorageFeaturesKHR(
    VulkanStreamGuest* vkStream,
    VkPhysicalDevice8BitStorageFeaturesKHR* forUnmarshaling);

#endif
#ifdef VK_ANDROID_native_buffer
void marshal_VkNativeBufferANDROID(
    VulkanStreamGuest* vkStream,
    const VkNativeBufferANDROID* forMarshaling);

void unmarshal_VkNativeBufferANDROID(
    VulkanStreamGuest* vkStream,
    VkNativeBufferANDROID* forUnmarshaling);

#define OP_vkGetSwapchainGrallocUsageANDROID 20251
#define OP_vkAcquireImageANDROID 20252
#define OP_vkQueueSignalReleaseImageANDROID 20253
#endif
#ifdef VK_EXT_debug_report
void marshal_VkDebugReportCallbackCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugReportCallbackCreateInfoEXT* forMarshaling);

void unmarshal_VkDebugReportCallbackCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugReportCallbackCreateInfoEXT* forUnmarshaling);

#define OP_vkCreateDebugReportCallbackEXT 20254
#define OP_vkDestroyDebugReportCallbackEXT 20255
#define OP_vkDebugReportMessageEXT 20256
#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void marshal_VkPipelineRasterizationStateRasterizationOrderAMD(
    VulkanStreamGuest* vkStream,
    const VkPipelineRasterizationStateRasterizationOrderAMD* forMarshaling);

void unmarshal_VkPipelineRasterizationStateRasterizationOrderAMD(
    VulkanStreamGuest* vkStream,
    VkPipelineRasterizationStateRasterizationOrderAMD* forUnmarshaling);

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void marshal_VkDebugMarkerObjectNameInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugMarkerObjectNameInfoEXT* forMarshaling);

void unmarshal_VkDebugMarkerObjectNameInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugMarkerObjectNameInfoEXT* forUnmarshaling);

void marshal_VkDebugMarkerObjectTagInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugMarkerObjectTagInfoEXT* forMarshaling);

void unmarshal_VkDebugMarkerObjectTagInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugMarkerObjectTagInfoEXT* forUnmarshaling);

void marshal_VkDebugMarkerMarkerInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugMarkerMarkerInfoEXT* forMarshaling);

void unmarshal_VkDebugMarkerMarkerInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugMarkerMarkerInfoEXT* forUnmarshaling);

#define OP_vkDebugMarkerSetObjectTagEXT 20257
#define OP_vkDebugMarkerSetObjectNameEXT 20258
#define OP_vkCmdDebugMarkerBeginEXT 20259
#define OP_vkCmdDebugMarkerEndEXT 20260
#define OP_vkCmdDebugMarkerInsertEXT 20261
#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void marshal_VkDedicatedAllocationImageCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkDedicatedAllocationImageCreateInfoNV* forMarshaling);

void unmarshal_VkDedicatedAllocationImageCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkDedicatedAllocationImageCreateInfoNV* forUnmarshaling);

void marshal_VkDedicatedAllocationBufferCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkDedicatedAllocationBufferCreateInfoNV* forMarshaling);

void unmarshal_VkDedicatedAllocationBufferCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkDedicatedAllocationBufferCreateInfoNV* forUnmarshaling);

void marshal_VkDedicatedAllocationMemoryAllocateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkDedicatedAllocationMemoryAllocateInfoNV* forMarshaling);

void unmarshal_VkDedicatedAllocationMemoryAllocateInfoNV(
    VulkanStreamGuest* vkStream,
    VkDedicatedAllocationMemoryAllocateInfoNV* forUnmarshaling);

#endif
#ifdef VK_AMD_draw_indirect_count
#define OP_vkCmdDrawIndirectCountAMD 20262
#define OP_vkCmdDrawIndexedIndirectCountAMD 20263
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void marshal_VkTextureLODGatherFormatPropertiesAMD(
    VulkanStreamGuest* vkStream,
    const VkTextureLODGatherFormatPropertiesAMD* forMarshaling);

void unmarshal_VkTextureLODGatherFormatPropertiesAMD(
    VulkanStreamGuest* vkStream,
    VkTextureLODGatherFormatPropertiesAMD* forUnmarshaling);

#endif
#ifdef VK_AMD_shader_info
void marshal_VkShaderResourceUsageAMD(
    VulkanStreamGuest* vkStream,
    const VkShaderResourceUsageAMD* forMarshaling);

void unmarshal_VkShaderResourceUsageAMD(
    VulkanStreamGuest* vkStream,
    VkShaderResourceUsageAMD* forUnmarshaling);

void marshal_VkShaderStatisticsInfoAMD(
    VulkanStreamGuest* vkStream,
    const VkShaderStatisticsInfoAMD* forMarshaling);

void unmarshal_VkShaderStatisticsInfoAMD(
    VulkanStreamGuest* vkStream,
    VkShaderStatisticsInfoAMD* forUnmarshaling);

#define OP_vkGetShaderInfoAMD 20264
#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void marshal_VkExternalImageFormatPropertiesNV(
    VulkanStreamGuest* vkStream,
    const VkExternalImageFormatPropertiesNV* forMarshaling);

void unmarshal_VkExternalImageFormatPropertiesNV(
    VulkanStreamGuest* vkStream,
    VkExternalImageFormatPropertiesNV* forUnmarshaling);

#define OP_vkGetPhysicalDeviceExternalImageFormatPropertiesNV 20265
#endif
#ifdef VK_NV_external_memory
void marshal_VkExternalMemoryImageCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkExternalMemoryImageCreateInfoNV* forMarshaling);

void unmarshal_VkExternalMemoryImageCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkExternalMemoryImageCreateInfoNV* forUnmarshaling);

void marshal_VkExportMemoryAllocateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkExportMemoryAllocateInfoNV* forMarshaling);

void unmarshal_VkExportMemoryAllocateInfoNV(
    VulkanStreamGuest* vkStream,
    VkExportMemoryAllocateInfoNV* forUnmarshaling);

#endif
#ifdef VK_NV_external_memory_win32
void marshal_VkImportMemoryWin32HandleInfoNV(
    VulkanStreamGuest* vkStream,
    const VkImportMemoryWin32HandleInfoNV* forMarshaling);

void unmarshal_VkImportMemoryWin32HandleInfoNV(
    VulkanStreamGuest* vkStream,
    VkImportMemoryWin32HandleInfoNV* forUnmarshaling);

void marshal_VkExportMemoryWin32HandleInfoNV(
    VulkanStreamGuest* vkStream,
    const VkExportMemoryWin32HandleInfoNV* forMarshaling);

void unmarshal_VkExportMemoryWin32HandleInfoNV(
    VulkanStreamGuest* vkStream,
    VkExportMemoryWin32HandleInfoNV* forUnmarshaling);

#define OP_vkGetMemoryWin32HandleNV 20266
#endif
#ifdef VK_NV_win32_keyed_mutex
void marshal_VkWin32KeyedMutexAcquireReleaseInfoNV(
    VulkanStreamGuest* vkStream,
    const VkWin32KeyedMutexAcquireReleaseInfoNV* forMarshaling);

void unmarshal_VkWin32KeyedMutexAcquireReleaseInfoNV(
    VulkanStreamGuest* vkStream,
    VkWin32KeyedMutexAcquireReleaseInfoNV* forUnmarshaling);

#endif
#ifdef VK_EXT_validation_flags
void marshal_VkValidationFlagsEXT(
    VulkanStreamGuest* vkStream,
    const VkValidationFlagsEXT* forMarshaling);

void unmarshal_VkValidationFlagsEXT(
    VulkanStreamGuest* vkStream,
    VkValidationFlagsEXT* forUnmarshaling);

#endif
#ifdef VK_NN_vi_surface
void marshal_VkViSurfaceCreateInfoNN(
    VulkanStreamGuest* vkStream,
    const VkViSurfaceCreateInfoNN* forMarshaling);

void unmarshal_VkViSurfaceCreateInfoNN(
    VulkanStreamGuest* vkStream,
    VkViSurfaceCreateInfoNN* forUnmarshaling);

#define OP_vkCreateViSurfaceNN 20267
#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void marshal_VkConditionalRenderingBeginInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkConditionalRenderingBeginInfoEXT* forMarshaling);

void unmarshal_VkConditionalRenderingBeginInfoEXT(
    VulkanStreamGuest* vkStream,
    VkConditionalRenderingBeginInfoEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceConditionalRenderingFeaturesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* forUnmarshaling);

void marshal_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkCommandBufferInheritanceConditionalRenderingInfoEXT* forMarshaling);

void unmarshal_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    VulkanStreamGuest* vkStream,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* forUnmarshaling);

#define OP_vkCmdBeginConditionalRenderingEXT 20268
#define OP_vkCmdEndConditionalRenderingEXT 20269
#endif
#ifdef VK_NVX_device_generated_commands
void marshal_VkDeviceGeneratedCommandsFeaturesNVX(
    VulkanStreamGuest* vkStream,
    const VkDeviceGeneratedCommandsFeaturesNVX* forMarshaling);

void unmarshal_VkDeviceGeneratedCommandsFeaturesNVX(
    VulkanStreamGuest* vkStream,
    VkDeviceGeneratedCommandsFeaturesNVX* forUnmarshaling);

void marshal_VkDeviceGeneratedCommandsLimitsNVX(
    VulkanStreamGuest* vkStream,
    const VkDeviceGeneratedCommandsLimitsNVX* forMarshaling);

void unmarshal_VkDeviceGeneratedCommandsLimitsNVX(
    VulkanStreamGuest* vkStream,
    VkDeviceGeneratedCommandsLimitsNVX* forUnmarshaling);

void marshal_VkIndirectCommandsTokenNVX(
    VulkanStreamGuest* vkStream,
    const VkIndirectCommandsTokenNVX* forMarshaling);

void unmarshal_VkIndirectCommandsTokenNVX(
    VulkanStreamGuest* vkStream,
    VkIndirectCommandsTokenNVX* forUnmarshaling);

void marshal_VkIndirectCommandsLayoutTokenNVX(
    VulkanStreamGuest* vkStream,
    const VkIndirectCommandsLayoutTokenNVX* forMarshaling);

void unmarshal_VkIndirectCommandsLayoutTokenNVX(
    VulkanStreamGuest* vkStream,
    VkIndirectCommandsLayoutTokenNVX* forUnmarshaling);

void marshal_VkIndirectCommandsLayoutCreateInfoNVX(
    VulkanStreamGuest* vkStream,
    const VkIndirectCommandsLayoutCreateInfoNVX* forMarshaling);

void unmarshal_VkIndirectCommandsLayoutCreateInfoNVX(
    VulkanStreamGuest* vkStream,
    VkIndirectCommandsLayoutCreateInfoNVX* forUnmarshaling);

void marshal_VkCmdProcessCommandsInfoNVX(
    VulkanStreamGuest* vkStream,
    const VkCmdProcessCommandsInfoNVX* forMarshaling);

void unmarshal_VkCmdProcessCommandsInfoNVX(
    VulkanStreamGuest* vkStream,
    VkCmdProcessCommandsInfoNVX* forUnmarshaling);

void marshal_VkCmdReserveSpaceForCommandsInfoNVX(
    VulkanStreamGuest* vkStream,
    const VkCmdReserveSpaceForCommandsInfoNVX* forMarshaling);

void unmarshal_VkCmdReserveSpaceForCommandsInfoNVX(
    VulkanStreamGuest* vkStream,
    VkCmdReserveSpaceForCommandsInfoNVX* forUnmarshaling);

void marshal_VkObjectTableCreateInfoNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTableCreateInfoNVX* forMarshaling);

void unmarshal_VkObjectTableCreateInfoNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTableCreateInfoNVX* forUnmarshaling);

void marshal_VkObjectTableEntryNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTableEntryNVX* forMarshaling);

void unmarshal_VkObjectTableEntryNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTableEntryNVX* forUnmarshaling);

void marshal_VkObjectTablePipelineEntryNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTablePipelineEntryNVX* forMarshaling);

void unmarshal_VkObjectTablePipelineEntryNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTablePipelineEntryNVX* forUnmarshaling);

void marshal_VkObjectTableDescriptorSetEntryNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTableDescriptorSetEntryNVX* forMarshaling);

void unmarshal_VkObjectTableDescriptorSetEntryNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTableDescriptorSetEntryNVX* forUnmarshaling);

void marshal_VkObjectTableVertexBufferEntryNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTableVertexBufferEntryNVX* forMarshaling);

void unmarshal_VkObjectTableVertexBufferEntryNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTableVertexBufferEntryNVX* forUnmarshaling);

void marshal_VkObjectTableIndexBufferEntryNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTableIndexBufferEntryNVX* forMarshaling);

void unmarshal_VkObjectTableIndexBufferEntryNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTableIndexBufferEntryNVX* forUnmarshaling);

void marshal_VkObjectTablePushConstantEntryNVX(
    VulkanStreamGuest* vkStream,
    const VkObjectTablePushConstantEntryNVX* forMarshaling);

void unmarshal_VkObjectTablePushConstantEntryNVX(
    VulkanStreamGuest* vkStream,
    VkObjectTablePushConstantEntryNVX* forUnmarshaling);

#define OP_vkCmdProcessCommandsNVX 20270
#define OP_vkCmdReserveSpaceForCommandsNVX 20271
#define OP_vkCreateIndirectCommandsLayoutNVX 20272
#define OP_vkDestroyIndirectCommandsLayoutNVX 20273
#define OP_vkCreateObjectTableNVX 20274
#define OP_vkDestroyObjectTableNVX 20275
#define OP_vkRegisterObjectsNVX 20276
#define OP_vkUnregisterObjectsNVX 20277
#define OP_vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX 20278
#endif
#ifdef VK_NV_clip_space_w_scaling
void marshal_VkViewportWScalingNV(
    VulkanStreamGuest* vkStream,
    const VkViewportWScalingNV* forMarshaling);

void unmarshal_VkViewportWScalingNV(
    VulkanStreamGuest* vkStream,
    VkViewportWScalingNV* forUnmarshaling);

void marshal_VkPipelineViewportWScalingStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkPipelineViewportWScalingStateCreateInfoNV* forMarshaling);

void unmarshal_VkPipelineViewportWScalingStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkPipelineViewportWScalingStateCreateInfoNV* forUnmarshaling);

#define OP_vkCmdSetViewportWScalingNV 20279
#endif
#ifdef VK_EXT_direct_mode_display
#define OP_vkReleaseDisplayEXT 20280
#endif
#ifdef VK_EXT_acquire_xlib_display
#define OP_vkAcquireXlibDisplayEXT 20281
#define OP_vkGetRandROutputDisplayEXT 20282
#endif
#ifdef VK_EXT_display_surface_counter
void marshal_VkSurfaceCapabilities2EXT(
    VulkanStreamGuest* vkStream,
    const VkSurfaceCapabilities2EXT* forMarshaling);

void unmarshal_VkSurfaceCapabilities2EXT(
    VulkanStreamGuest* vkStream,
    VkSurfaceCapabilities2EXT* forUnmarshaling);

#define OP_vkGetPhysicalDeviceSurfaceCapabilities2EXT 20283
#endif
#ifdef VK_EXT_display_control
void marshal_VkDisplayPowerInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDisplayPowerInfoEXT* forMarshaling);

void unmarshal_VkDisplayPowerInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDisplayPowerInfoEXT* forUnmarshaling);

void marshal_VkDeviceEventInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDeviceEventInfoEXT* forMarshaling);

void unmarshal_VkDeviceEventInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDeviceEventInfoEXT* forUnmarshaling);

void marshal_VkDisplayEventInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDisplayEventInfoEXT* forMarshaling);

void unmarshal_VkDisplayEventInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDisplayEventInfoEXT* forUnmarshaling);

void marshal_VkSwapchainCounterCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkSwapchainCounterCreateInfoEXT* forMarshaling);

void unmarshal_VkSwapchainCounterCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkSwapchainCounterCreateInfoEXT* forUnmarshaling);

#define OP_vkDisplayPowerControlEXT 20284
#define OP_vkRegisterDeviceEventEXT 20285
#define OP_vkRegisterDisplayEventEXT 20286
#define OP_vkGetSwapchainCounterEXT 20287
#endif
#ifdef VK_GOOGLE_display_timing
void marshal_VkRefreshCycleDurationGOOGLE(
    VulkanStreamGuest* vkStream,
    const VkRefreshCycleDurationGOOGLE* forMarshaling);

void unmarshal_VkRefreshCycleDurationGOOGLE(
    VulkanStreamGuest* vkStream,
    VkRefreshCycleDurationGOOGLE* forUnmarshaling);

void marshal_VkPastPresentationTimingGOOGLE(
    VulkanStreamGuest* vkStream,
    const VkPastPresentationTimingGOOGLE* forMarshaling);

void unmarshal_VkPastPresentationTimingGOOGLE(
    VulkanStreamGuest* vkStream,
    VkPastPresentationTimingGOOGLE* forUnmarshaling);

void marshal_VkPresentTimeGOOGLE(
    VulkanStreamGuest* vkStream,
    const VkPresentTimeGOOGLE* forMarshaling);

void unmarshal_VkPresentTimeGOOGLE(
    VulkanStreamGuest* vkStream,
    VkPresentTimeGOOGLE* forUnmarshaling);

void marshal_VkPresentTimesInfoGOOGLE(
    VulkanStreamGuest* vkStream,
    const VkPresentTimesInfoGOOGLE* forMarshaling);

void unmarshal_VkPresentTimesInfoGOOGLE(
    VulkanStreamGuest* vkStream,
    VkPresentTimesInfoGOOGLE* forUnmarshaling);

#define OP_vkGetRefreshCycleDurationGOOGLE 20288
#define OP_vkGetPastPresentationTimingGOOGLE 20289
#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void marshal_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* forMarshaling);

void unmarshal_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* forUnmarshaling);

#endif
#ifdef VK_NV_viewport_swizzle
void marshal_VkViewportSwizzleNV(
    VulkanStreamGuest* vkStream,
    const VkViewportSwizzleNV* forMarshaling);

void unmarshal_VkViewportSwizzleNV(
    VulkanStreamGuest* vkStream,
    VkViewportSwizzleNV* forUnmarshaling);

void marshal_VkPipelineViewportSwizzleStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkPipelineViewportSwizzleStateCreateInfoNV* forMarshaling);

void unmarshal_VkPipelineViewportSwizzleStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkPipelineViewportSwizzleStateCreateInfoNV* forUnmarshaling);

#endif
#ifdef VK_EXT_discard_rectangles
void marshal_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceDiscardRectanglePropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* forUnmarshaling);

void marshal_VkPipelineDiscardRectangleStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkPipelineDiscardRectangleStateCreateInfoEXT* forMarshaling);

void unmarshal_VkPipelineDiscardRectangleStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkPipelineDiscardRectangleStateCreateInfoEXT* forUnmarshaling);

#define OP_vkCmdSetDiscardRectangleEXT 20290
#endif
#ifdef VK_EXT_conservative_rasterization
void marshal_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceConservativeRasterizationPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* forUnmarshaling);

void marshal_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkPipelineRasterizationConservativeStateCreateInfoEXT* forMarshaling);

void unmarshal_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* forUnmarshaling);

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void marshal_VkXYColorEXT(
    VulkanStreamGuest* vkStream,
    const VkXYColorEXT* forMarshaling);

void unmarshal_VkXYColorEXT(
    VulkanStreamGuest* vkStream,
    VkXYColorEXT* forUnmarshaling);

void marshal_VkHdrMetadataEXT(
    VulkanStreamGuest* vkStream,
    const VkHdrMetadataEXT* forMarshaling);

void unmarshal_VkHdrMetadataEXT(
    VulkanStreamGuest* vkStream,
    VkHdrMetadataEXT* forUnmarshaling);

#define OP_vkSetHdrMetadataEXT 20291
#endif
#ifdef VK_MVK_ios_surface
void marshal_VkIOSSurfaceCreateInfoMVK(
    VulkanStreamGuest* vkStream,
    const VkIOSSurfaceCreateInfoMVK* forMarshaling);

void unmarshal_VkIOSSurfaceCreateInfoMVK(
    VulkanStreamGuest* vkStream,
    VkIOSSurfaceCreateInfoMVK* forUnmarshaling);

#define OP_vkCreateIOSSurfaceMVK 20292
#endif
#ifdef VK_MVK_macos_surface
void marshal_VkMacOSSurfaceCreateInfoMVK(
    VulkanStreamGuest* vkStream,
    const VkMacOSSurfaceCreateInfoMVK* forMarshaling);

void unmarshal_VkMacOSSurfaceCreateInfoMVK(
    VulkanStreamGuest* vkStream,
    VkMacOSSurfaceCreateInfoMVK* forUnmarshaling);

#define OP_vkCreateMacOSSurfaceMVK 20293
#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void marshal_VkDebugUtilsObjectNameInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugUtilsObjectNameInfoEXT* forMarshaling);

void unmarshal_VkDebugUtilsObjectNameInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugUtilsObjectNameInfoEXT* forUnmarshaling);

void marshal_VkDebugUtilsObjectTagInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugUtilsObjectTagInfoEXT* forMarshaling);

void unmarshal_VkDebugUtilsObjectTagInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugUtilsObjectTagInfoEXT* forUnmarshaling);

void marshal_VkDebugUtilsLabelEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugUtilsLabelEXT* forMarshaling);

void unmarshal_VkDebugUtilsLabelEXT(
    VulkanStreamGuest* vkStream,
    VkDebugUtilsLabelEXT* forUnmarshaling);

void marshal_VkDebugUtilsMessengerCallbackDataEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugUtilsMessengerCallbackDataEXT* forMarshaling);

void unmarshal_VkDebugUtilsMessengerCallbackDataEXT(
    VulkanStreamGuest* vkStream,
    VkDebugUtilsMessengerCallbackDataEXT* forUnmarshaling);

void marshal_VkDebugUtilsMessengerCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDebugUtilsMessengerCreateInfoEXT* forMarshaling);

void unmarshal_VkDebugUtilsMessengerCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDebugUtilsMessengerCreateInfoEXT* forUnmarshaling);

#define OP_vkSetDebugUtilsObjectNameEXT 20294
#define OP_vkSetDebugUtilsObjectTagEXT 20295
#define OP_vkQueueBeginDebugUtilsLabelEXT 20296
#define OP_vkQueueEndDebugUtilsLabelEXT 20297
#define OP_vkQueueInsertDebugUtilsLabelEXT 20298
#define OP_vkCmdBeginDebugUtilsLabelEXT 20299
#define OP_vkCmdEndDebugUtilsLabelEXT 20300
#define OP_vkCmdInsertDebugUtilsLabelEXT 20301
#define OP_vkCreateDebugUtilsMessengerEXT 20302
#define OP_vkDestroyDebugUtilsMessengerEXT 20303
#define OP_vkSubmitDebugUtilsMessageEXT 20304
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void marshal_VkAndroidHardwareBufferUsageANDROID(
    VulkanStreamGuest* vkStream,
    const VkAndroidHardwareBufferUsageANDROID* forMarshaling);

void unmarshal_VkAndroidHardwareBufferUsageANDROID(
    VulkanStreamGuest* vkStream,
    VkAndroidHardwareBufferUsageANDROID* forUnmarshaling);

void marshal_VkAndroidHardwareBufferPropertiesANDROID(
    VulkanStreamGuest* vkStream,
    const VkAndroidHardwareBufferPropertiesANDROID* forMarshaling);

void unmarshal_VkAndroidHardwareBufferPropertiesANDROID(
    VulkanStreamGuest* vkStream,
    VkAndroidHardwareBufferPropertiesANDROID* forUnmarshaling);

void marshal_VkAndroidHardwareBufferFormatPropertiesANDROID(
    VulkanStreamGuest* vkStream,
    const VkAndroidHardwareBufferFormatPropertiesANDROID* forMarshaling);

void unmarshal_VkAndroidHardwareBufferFormatPropertiesANDROID(
    VulkanStreamGuest* vkStream,
    VkAndroidHardwareBufferFormatPropertiesANDROID* forUnmarshaling);

void marshal_VkImportAndroidHardwareBufferInfoANDROID(
    VulkanStreamGuest* vkStream,
    const VkImportAndroidHardwareBufferInfoANDROID* forMarshaling);

void unmarshal_VkImportAndroidHardwareBufferInfoANDROID(
    VulkanStreamGuest* vkStream,
    VkImportAndroidHardwareBufferInfoANDROID* forUnmarshaling);

void marshal_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    VulkanStreamGuest* vkStream,
    const VkMemoryGetAndroidHardwareBufferInfoANDROID* forMarshaling);

void unmarshal_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    VulkanStreamGuest* vkStream,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* forUnmarshaling);

void marshal_VkExternalFormatANDROID(
    VulkanStreamGuest* vkStream,
    const VkExternalFormatANDROID* forMarshaling);

void unmarshal_VkExternalFormatANDROID(
    VulkanStreamGuest* vkStream,
    VkExternalFormatANDROID* forUnmarshaling);

#define OP_vkGetAndroidHardwareBufferPropertiesANDROID 20305
#define OP_vkGetMemoryAndroidHardwareBufferANDROID 20306
#endif
#ifdef VK_EXT_sampler_filter_minmax
void marshal_VkSamplerReductionModeCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkSamplerReductionModeCreateInfoEXT* forMarshaling);

void unmarshal_VkSamplerReductionModeCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkSamplerReductionModeCreateInfoEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* forUnmarshaling);

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void marshal_VkSampleLocationEXT(
    VulkanStreamGuest* vkStream,
    const VkSampleLocationEXT* forMarshaling);

void unmarshal_VkSampleLocationEXT(
    VulkanStreamGuest* vkStream,
    VkSampleLocationEXT* forUnmarshaling);

void marshal_VkSampleLocationsInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkSampleLocationsInfoEXT* forMarshaling);

void unmarshal_VkSampleLocationsInfoEXT(
    VulkanStreamGuest* vkStream,
    VkSampleLocationsInfoEXT* forUnmarshaling);

void marshal_VkAttachmentSampleLocationsEXT(
    VulkanStreamGuest* vkStream,
    const VkAttachmentSampleLocationsEXT* forMarshaling);

void unmarshal_VkAttachmentSampleLocationsEXT(
    VulkanStreamGuest* vkStream,
    VkAttachmentSampleLocationsEXT* forUnmarshaling);

void marshal_VkSubpassSampleLocationsEXT(
    VulkanStreamGuest* vkStream,
    const VkSubpassSampleLocationsEXT* forMarshaling);

void unmarshal_VkSubpassSampleLocationsEXT(
    VulkanStreamGuest* vkStream,
    VkSubpassSampleLocationsEXT* forUnmarshaling);

void marshal_VkRenderPassSampleLocationsBeginInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkRenderPassSampleLocationsBeginInfoEXT* forMarshaling);

void unmarshal_VkRenderPassSampleLocationsBeginInfoEXT(
    VulkanStreamGuest* vkStream,
    VkRenderPassSampleLocationsBeginInfoEXT* forUnmarshaling);

void marshal_VkPipelineSampleLocationsStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkPipelineSampleLocationsStateCreateInfoEXT* forMarshaling);

void unmarshal_VkPipelineSampleLocationsStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkPipelineSampleLocationsStateCreateInfoEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceSampleLocationsPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* forUnmarshaling);

void marshal_VkMultisamplePropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkMultisamplePropertiesEXT* forMarshaling);

void unmarshal_VkMultisamplePropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkMultisamplePropertiesEXT* forUnmarshaling);

#define OP_vkCmdSetSampleLocationsEXT 20307
#define OP_vkGetPhysicalDeviceMultisamplePropertiesEXT 20308
#endif
#ifdef VK_EXT_blend_operation_advanced
void marshal_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* forUnmarshaling);

void marshal_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkPipelineColorBlendAdvancedStateCreateInfoEXT* forMarshaling);

void unmarshal_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* forUnmarshaling);

#endif
#ifdef VK_NV_fragment_coverage_to_color
void marshal_VkPipelineCoverageToColorStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkPipelineCoverageToColorStateCreateInfoNV* forMarshaling);

void unmarshal_VkPipelineCoverageToColorStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkPipelineCoverageToColorStateCreateInfoNV* forUnmarshaling);

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void marshal_VkPipelineCoverageModulationStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    const VkPipelineCoverageModulationStateCreateInfoNV* forMarshaling);

void unmarshal_VkPipelineCoverageModulationStateCreateInfoNV(
    VulkanStreamGuest* vkStream,
    VkPipelineCoverageModulationStateCreateInfoNV* forUnmarshaling);

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void marshal_VkValidationCacheCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkValidationCacheCreateInfoEXT* forMarshaling);

void unmarshal_VkValidationCacheCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkValidationCacheCreateInfoEXT* forUnmarshaling);

void marshal_VkShaderModuleValidationCacheCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkShaderModuleValidationCacheCreateInfoEXT* forMarshaling);

void unmarshal_VkShaderModuleValidationCacheCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkShaderModuleValidationCacheCreateInfoEXT* forUnmarshaling);

#define OP_vkCreateValidationCacheEXT 20309
#define OP_vkDestroyValidationCacheEXT 20310
#define OP_vkMergeValidationCachesEXT 20311
#define OP_vkGetValidationCacheDataEXT 20312
#endif
#ifdef VK_EXT_descriptor_indexing
void marshal_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* forMarshaling);

void unmarshal_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceDescriptorIndexingFeaturesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceDescriptorIndexingPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* forUnmarshaling);

void marshal_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* forMarshaling);

void unmarshal_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* forUnmarshaling);

void marshal_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    VulkanStreamGuest* vkStream,
    const VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* forMarshaling);

void unmarshal_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    VulkanStreamGuest* vkStream,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* forUnmarshaling);

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void marshal_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkDeviceQueueGlobalPriorityCreateInfoEXT* forMarshaling);

void unmarshal_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* forUnmarshaling);

#endif
#ifdef VK_EXT_external_memory_host
void marshal_VkImportMemoryHostPointerInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkImportMemoryHostPointerInfoEXT* forMarshaling);

void unmarshal_VkImportMemoryHostPointerInfoEXT(
    VulkanStreamGuest* vkStream,
    VkImportMemoryHostPointerInfoEXT* forUnmarshaling);

void marshal_VkMemoryHostPointerPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkMemoryHostPointerPropertiesEXT* forMarshaling);

void unmarshal_VkMemoryHostPointerPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkMemoryHostPointerPropertiesEXT* forUnmarshaling);

void marshal_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceExternalMemoryHostPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* forUnmarshaling);

#define OP_vkGetMemoryHostPointerPropertiesEXT 20313
#endif
#ifdef VK_AMD_buffer_marker
#define OP_vkCmdWriteBufferMarkerAMD 20314
#endif
#ifdef VK_AMD_shader_core_properties
void marshal_VkPhysicalDeviceShaderCorePropertiesAMD(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceShaderCorePropertiesAMD* forMarshaling);

void unmarshal_VkPhysicalDeviceShaderCorePropertiesAMD(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceShaderCorePropertiesAMD* forUnmarshaling);

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void marshal_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    VulkanStreamGuest* vkStream,
    const VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* forMarshaling);

void unmarshal_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    VulkanStreamGuest* vkStream,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* forUnmarshaling);

void marshal_VkVertexInputBindingDivisorDescriptionEXT(
    VulkanStreamGuest* vkStream,
    const VkVertexInputBindingDivisorDescriptionEXT* forMarshaling);

void unmarshal_VkVertexInputBindingDivisorDescriptionEXT(
    VulkanStreamGuest* vkStream,
    VkVertexInputBindingDivisorDescriptionEXT* forUnmarshaling);

void marshal_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    const VkPipelineVertexInputDivisorStateCreateInfoEXT* forMarshaling);

void unmarshal_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    VulkanStreamGuest* vkStream,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* forUnmarshaling);

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void marshal_VkQueueFamilyCheckpointPropertiesNV(
    VulkanStreamGuest* vkStream,
    const VkQueueFamilyCheckpointPropertiesNV* forMarshaling);

void unmarshal_VkQueueFamilyCheckpointPropertiesNV(
    VulkanStreamGuest* vkStream,
    VkQueueFamilyCheckpointPropertiesNV* forUnmarshaling);

void marshal_VkCheckpointDataNV(
    VulkanStreamGuest* vkStream,
    const VkCheckpointDataNV* forMarshaling);

void unmarshal_VkCheckpointDataNV(
    VulkanStreamGuest* vkStream,
    VkCheckpointDataNV* forUnmarshaling);

#define OP_vkCmdSetCheckpointNV 20315
#define OP_vkGetQueueCheckpointDataNV 20316
#endif
#ifdef VK_GOOGLE_address_space
#define OP_vkMapMemoryIntoAddressSpaceGOOGLE 20317
#endif
#ifdef VK_GOOGLE_color_buffer
void marshal_VkImportColorBufferGOOGLE(
    VulkanStreamGuest* vkStream,
    const VkImportColorBufferGOOGLE* forMarshaling);

void unmarshal_VkImportColorBufferGOOGLE(
    VulkanStreamGuest* vkStream,
    VkImportColorBufferGOOGLE* forUnmarshaling);

void marshal_VkImportPhysicalAddressGOOGLE(
    VulkanStreamGuest* vkStream,
    const VkImportPhysicalAddressGOOGLE* forMarshaling);

void unmarshal_VkImportPhysicalAddressGOOGLE(
    VulkanStreamGuest* vkStream,
    VkImportPhysicalAddressGOOGLE* forUnmarshaling);

#define OP_vkRegisterImageColorBufferGOOGLE 20318
#define OP_vkRegisterBufferColorBufferGOOGLE 20319
#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#define OP_vkUpdateDescriptorSetWithTemplateSizedGOOGLE 20320
#endif
#ifdef VK_GOOGLE_async_command_buffers
#define OP_vkBeginCommandBufferAsyncGOOGLE 20321
#define OP_vkEndCommandBufferAsyncGOOGLE 20322
#define OP_vkResetCommandBufferAsyncGOOGLE 20323
#endif

} // namespace goldfish_vk
