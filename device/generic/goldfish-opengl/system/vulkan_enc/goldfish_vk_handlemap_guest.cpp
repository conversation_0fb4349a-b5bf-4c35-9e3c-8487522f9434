// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_handlemap_guest
// (impl) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#include "goldfish_vk_handlemap_guest.h"


#include "goldfish_vk_extension_structs_guest.h"
#include "goldfish_vk_private_defs.h"


namespace goldfish_vk {

void handlemap_extension_struct(
    VulkanHandleMapping* handlemap,
    void* structExtension_out);

#ifdef VK_VERSION_1_0
void handlemap_VkApplicationInfo(
    VulkanHandleMapping* handlemap,
    VkApplicationInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkInstanceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkInstanceCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pApplicationInfo)
    {
        handlemap_VkApplicationInfo(handlemap, (VkApplicationInfo*)(toMap->pApplicationInfo));
    }
}

void handlemap_VkAllocationCallbacks(
    VulkanHandleMapping* handlemap,
    VkAllocationCallbacks* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPhysicalDeviceFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkFormatProperties(
    VulkanHandleMapping* handlemap,
    VkFormatProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkExtent3D(
    VulkanHandleMapping* handlemap,
    VkExtent3D* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkImageFormatProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->maxExtent));
}

void handlemap_VkPhysicalDeviceLimits(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceLimits* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPhysicalDeviceSparseProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSparseProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPhysicalDeviceProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkPhysicalDeviceLimits(handlemap, (VkPhysicalDeviceLimits*)(&toMap->limits));
    handlemap_VkPhysicalDeviceSparseProperties(handlemap, (VkPhysicalDeviceSparseProperties*)(&toMap->sparseProperties));
}

void handlemap_VkQueueFamilyProperties(
    VulkanHandleMapping* handlemap,
    VkQueueFamilyProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->minImageTransferGranularity));
}

void handlemap_VkMemoryType(
    VulkanHandleMapping* handlemap,
    VkMemoryType* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkMemoryHeap(
    VulkanHandleMapping* handlemap,
    VkMemoryHeap* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPhysicalDeviceMemoryProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMemoryProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_TYPES; ++i)
    {
        handlemap_VkMemoryType(handlemap, (VkMemoryType*)(toMap->memoryTypes + i));
    }
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_HEAPS; ++i)
    {
        handlemap_VkMemoryHeap(handlemap, (VkMemoryHeap*)(toMap->memoryHeaps + i));
    }
}

void handlemap_VkDeviceQueueCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceQueueCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pQueueCreateInfos)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->queueCreateInfoCount; ++i)
        {
            handlemap_VkDeviceQueueCreateInfo(handlemap, (VkDeviceQueueCreateInfo*)(toMap->pQueueCreateInfos + i));
        }
    }
    if (toMap->pEnabledFeatures)
    {
        handlemap_VkPhysicalDeviceFeatures(handlemap, (VkPhysicalDeviceFeatures*)(toMap->pEnabledFeatures));
    }
}

void handlemap_VkExtensionProperties(
    VulkanHandleMapping* handlemap,
    VkExtensionProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkLayerProperties(
    VulkanHandleMapping* handlemap,
    VkLayerProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkSubmitInfo(
    VulkanHandleMapping* handlemap,
    VkSubmitInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pWaitSemaphores)
    {
        handlemap->mapHandles_VkSemaphore((VkSemaphore*)toMap->pWaitSemaphores, toMap->waitSemaphoreCount);
    }
    if (toMap->pCommandBuffers)
    {
        handlemap->mapHandles_VkCommandBuffer((VkCommandBuffer*)toMap->pCommandBuffers, toMap->commandBufferCount);
    }
    if (toMap->pSignalSemaphores)
    {
        handlemap->mapHandles_VkSemaphore((VkSemaphore*)toMap->pSignalSemaphores, toMap->signalSemaphoreCount);
    }
}

void handlemap_VkMemoryAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkMemoryAllocateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMappedMemoryRange(
    VulkanHandleMapping* handlemap,
    VkMappedMemoryRange* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

void handlemap_VkMemoryRequirements(
    VulkanHandleMapping* handlemap,
    VkMemoryRequirements* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkSparseImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkSparseImageFormatProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->imageGranularity));
}

void handlemap_VkSparseImageMemoryRequirements(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryRequirements* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkSparseImageFormatProperties(handlemap, (VkSparseImageFormatProperties*)(&toMap->formatProperties));
}

void handlemap_VkSparseMemoryBind(
    VulkanHandleMapping* handlemap,
    VkSparseMemoryBind* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

void handlemap_VkSparseBufferMemoryBindInfo(
    VulkanHandleMapping* handlemap,
    VkSparseBufferMemoryBindInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
    if (toMap->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->bindCount; ++i)
        {
            handlemap_VkSparseMemoryBind(handlemap, (VkSparseMemoryBind*)(toMap->pBinds + i));
        }
    }
}

void handlemap_VkSparseImageOpaqueMemoryBindInfo(
    VulkanHandleMapping* handlemap,
    VkSparseImageOpaqueMemoryBindInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    if (toMap->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->bindCount; ++i)
        {
            handlemap_VkSparseMemoryBind(handlemap, (VkSparseMemoryBind*)(toMap->pBinds + i));
        }
    }
}

void handlemap_VkImageSubresource(
    VulkanHandleMapping* handlemap,
    VkImageSubresource* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkOffset3D(
    VulkanHandleMapping* handlemap,
    VkOffset3D* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkSparseImageMemoryBind(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryBind* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkImageSubresource(handlemap, (VkImageSubresource*)(&toMap->subresource));
    handlemap_VkOffset3D(handlemap, (VkOffset3D*)(&toMap->offset));
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->extent));
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

void handlemap_VkSparseImageMemoryBindInfo(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryBindInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    if (toMap->pBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->bindCount; ++i)
        {
            handlemap_VkSparseImageMemoryBind(handlemap, (VkSparseImageMemoryBind*)(toMap->pBinds + i));
        }
    }
}

void handlemap_VkBindSparseInfo(
    VulkanHandleMapping* handlemap,
    VkBindSparseInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pWaitSemaphores)
    {
        handlemap->mapHandles_VkSemaphore((VkSemaphore*)toMap->pWaitSemaphores, toMap->waitSemaphoreCount);
    }
    if (toMap->pBufferBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->bufferBindCount; ++i)
        {
            handlemap_VkSparseBufferMemoryBindInfo(handlemap, (VkSparseBufferMemoryBindInfo*)(toMap->pBufferBinds + i));
        }
    }
    if (toMap->pImageOpaqueBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->imageOpaqueBindCount; ++i)
        {
            handlemap_VkSparseImageOpaqueMemoryBindInfo(handlemap, (VkSparseImageOpaqueMemoryBindInfo*)(toMap->pImageOpaqueBinds + i));
        }
    }
    if (toMap->pImageBinds)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->imageBindCount; ++i)
        {
            handlemap_VkSparseImageMemoryBindInfo(handlemap, (VkSparseImageMemoryBindInfo*)(toMap->pImageBinds + i));
        }
    }
    if (toMap->pSignalSemaphores)
    {
        handlemap->mapHandles_VkSemaphore((VkSemaphore*)toMap->pSignalSemaphores, toMap->signalSemaphoreCount);
    }
}

void handlemap_VkFenceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkFenceCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSemaphoreCreateInfo(
    VulkanHandleMapping* handlemap,
    VkSemaphoreCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkEventCreateInfo(
    VulkanHandleMapping* handlemap,
    VkEventCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkQueryPoolCreateInfo(
    VulkanHandleMapping* handlemap,
    VkQueryPoolCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBufferCreateInfo(
    VulkanHandleMapping* handlemap,
    VkBufferCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBufferViewCreateInfo(
    VulkanHandleMapping* handlemap,
    VkBufferViewCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkImageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkImageCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->extent));
}

void handlemap_VkSubresourceLayout(
    VulkanHandleMapping* handlemap,
    VkSubresourceLayout* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkComponentMapping(
    VulkanHandleMapping* handlemap,
    VkComponentMapping* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkImageSubresourceRange(
    VulkanHandleMapping* handlemap,
    VkImageSubresourceRange* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkImageViewCreateInfo(
    VulkanHandleMapping* handlemap,
    VkImageViewCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    handlemap_VkComponentMapping(handlemap, (VkComponentMapping*)(&toMap->components));
    handlemap_VkImageSubresourceRange(handlemap, (VkImageSubresourceRange*)(&toMap->subresourceRange));
}

void handlemap_VkShaderModuleCreateInfo(
    VulkanHandleMapping* handlemap,
    VkShaderModuleCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineCacheCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineCacheCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSpecializationMapEntry(
    VulkanHandleMapping* handlemap,
    VkSpecializationMapEntry* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkSpecializationInfo(
    VulkanHandleMapping* handlemap,
    VkSpecializationInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pMapEntries)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->mapEntryCount; ++i)
        {
            handlemap_VkSpecializationMapEntry(handlemap, (VkSpecializationMapEntry*)(toMap->pMapEntries + i));
        }
    }
}

void handlemap_VkPipelineShaderStageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineShaderStageCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkShaderModule((VkShaderModule*)&toMap->module);
    if (toMap->pSpecializationInfo)
    {
        handlemap_VkSpecializationInfo(handlemap, (VkSpecializationInfo*)(toMap->pSpecializationInfo));
    }
}

void handlemap_VkVertexInputBindingDescription(
    VulkanHandleMapping* handlemap,
    VkVertexInputBindingDescription* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkVertexInputAttributeDescription(
    VulkanHandleMapping* handlemap,
    VkVertexInputAttributeDescription* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineVertexInputStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineVertexInputStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pVertexBindingDescriptions)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->vertexBindingDescriptionCount; ++i)
        {
            handlemap_VkVertexInputBindingDescription(handlemap, (VkVertexInputBindingDescription*)(toMap->pVertexBindingDescriptions + i));
        }
    }
    if (toMap->pVertexAttributeDescriptions)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->vertexAttributeDescriptionCount; ++i)
        {
            handlemap_VkVertexInputAttributeDescription(handlemap, (VkVertexInputAttributeDescription*)(toMap->pVertexAttributeDescriptions + i));
        }
    }
}

void handlemap_VkPipelineInputAssemblyStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineInputAssemblyStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineTessellationStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineTessellationStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkViewport(
    VulkanHandleMapping* handlemap,
    VkViewport* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkOffset2D(
    VulkanHandleMapping* handlemap,
    VkOffset2D* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkExtent2D(
    VulkanHandleMapping* handlemap,
    VkExtent2D* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkRect2D(
    VulkanHandleMapping* handlemap,
    VkRect2D* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkOffset2D(handlemap, (VkOffset2D*)(&toMap->offset));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->extent));
}

void handlemap_VkPipelineViewportStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineViewportStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pViewports)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->viewportCount; ++i)
        {
            handlemap_VkViewport(handlemap, (VkViewport*)(toMap->pViewports + i));
        }
    }
    if (toMap->pScissors)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->scissorCount; ++i)
        {
            handlemap_VkRect2D(handlemap, (VkRect2D*)(toMap->pScissors + i));
        }
    }
}

void handlemap_VkPipelineRasterizationStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineRasterizationStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineMultisampleStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineMultisampleStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkStencilOpState(
    VulkanHandleMapping* handlemap,
    VkStencilOpState* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineDepthStencilStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineDepthStencilStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkStencilOpState(handlemap, (VkStencilOpState*)(&toMap->front));
    handlemap_VkStencilOpState(handlemap, (VkStencilOpState*)(&toMap->back));
}

void handlemap_VkPipelineColorBlendAttachmentState(
    VulkanHandleMapping* handlemap,
    VkPipelineColorBlendAttachmentState* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineColorBlendStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineColorBlendStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->attachmentCount; ++i)
        {
            handlemap_VkPipelineColorBlendAttachmentState(handlemap, (VkPipelineColorBlendAttachmentState*)(toMap->pAttachments + i));
        }
    }
}

void handlemap_VkPipelineDynamicStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineDynamicStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkGraphicsPipelineCreateInfo(
    VulkanHandleMapping* handlemap,
    VkGraphicsPipelineCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pStages)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->stageCount; ++i)
        {
            handlemap_VkPipelineShaderStageCreateInfo(handlemap, (VkPipelineShaderStageCreateInfo*)(toMap->pStages + i));
        }
    }
    if (toMap->pVertexInputState)
    {
        handlemap_VkPipelineVertexInputStateCreateInfo(handlemap, (VkPipelineVertexInputStateCreateInfo*)(toMap->pVertexInputState));
    }
    if (toMap->pInputAssemblyState)
    {
        handlemap_VkPipelineInputAssemblyStateCreateInfo(handlemap, (VkPipelineInputAssemblyStateCreateInfo*)(toMap->pInputAssemblyState));
    }
    if (toMap->pTessellationState)
    {
        handlemap_VkPipelineTessellationStateCreateInfo(handlemap, (VkPipelineTessellationStateCreateInfo*)(toMap->pTessellationState));
    }
    if (toMap->pViewportState)
    {
        handlemap_VkPipelineViewportStateCreateInfo(handlemap, (VkPipelineViewportStateCreateInfo*)(toMap->pViewportState));
    }
    if (toMap->pRasterizationState)
    {
        handlemap_VkPipelineRasterizationStateCreateInfo(handlemap, (VkPipelineRasterizationStateCreateInfo*)(toMap->pRasterizationState));
    }
    if (toMap->pMultisampleState)
    {
        handlemap_VkPipelineMultisampleStateCreateInfo(handlemap, (VkPipelineMultisampleStateCreateInfo*)(toMap->pMultisampleState));
    }
    if (toMap->pDepthStencilState)
    {
        handlemap_VkPipelineDepthStencilStateCreateInfo(handlemap, (VkPipelineDepthStencilStateCreateInfo*)(toMap->pDepthStencilState));
    }
    if (toMap->pColorBlendState)
    {
        handlemap_VkPipelineColorBlendStateCreateInfo(handlemap, (VkPipelineColorBlendStateCreateInfo*)(toMap->pColorBlendState));
    }
    if (toMap->pDynamicState)
    {
        handlemap_VkPipelineDynamicStateCreateInfo(handlemap, (VkPipelineDynamicStateCreateInfo*)(toMap->pDynamicState));
    }
    handlemap->mapHandles_VkPipelineLayout((VkPipelineLayout*)&toMap->layout);
    handlemap->mapHandles_VkRenderPass((VkRenderPass*)&toMap->renderPass);
    handlemap->mapHandles_VkPipeline((VkPipeline*)&toMap->basePipelineHandle);
}

void handlemap_VkComputePipelineCreateInfo(
    VulkanHandleMapping* handlemap,
    VkComputePipelineCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkPipelineShaderStageCreateInfo(handlemap, (VkPipelineShaderStageCreateInfo*)(&toMap->stage));
    handlemap->mapHandles_VkPipelineLayout((VkPipelineLayout*)&toMap->layout);
    handlemap->mapHandles_VkPipeline((VkPipeline*)&toMap->basePipelineHandle);
}

void handlemap_VkPushConstantRange(
    VulkanHandleMapping* handlemap,
    VkPushConstantRange* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineLayoutCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineLayoutCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pSetLayouts)
    {
        handlemap->mapHandles_VkDescriptorSetLayout((VkDescriptorSetLayout*)toMap->pSetLayouts, toMap->setLayoutCount);
    }
    if (toMap->pPushConstantRanges)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->pushConstantRangeCount; ++i)
        {
            handlemap_VkPushConstantRange(handlemap, (VkPushConstantRange*)(toMap->pPushConstantRanges + i));
        }
    }
}

void handlemap_VkSamplerCreateInfo(
    VulkanHandleMapping* handlemap,
    VkSamplerCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDescriptorSetLayoutBinding(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutBinding* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pImmutableSamplers)
    {
        handlemap->mapHandles_VkSampler((VkSampler*)toMap->pImmutableSamplers, toMap->descriptorCount);
    }
}

void handlemap_VkDescriptorSetLayoutCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pBindings)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->bindingCount; ++i)
        {
            handlemap_VkDescriptorSetLayoutBinding(handlemap, (VkDescriptorSetLayoutBinding*)(toMap->pBindings + i));
        }
    }
}

void handlemap_VkDescriptorPoolSize(
    VulkanHandleMapping* handlemap,
    VkDescriptorPoolSize* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkDescriptorPoolCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorPoolCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pPoolSizes)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->poolSizeCount; ++i)
        {
            handlemap_VkDescriptorPoolSize(handlemap, (VkDescriptorPoolSize*)(toMap->pPoolSizes + i));
        }
    }
}

void handlemap_VkDescriptorSetAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetAllocateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDescriptorPool((VkDescriptorPool*)&toMap->descriptorPool);
    if (toMap->pSetLayouts)
    {
        handlemap->mapHandles_VkDescriptorSetLayout((VkDescriptorSetLayout*)toMap->pSetLayouts, toMap->descriptorSetCount);
    }
}

void handlemap_VkDescriptorImageInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorImageInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkSampler((VkSampler*)&toMap->sampler);
    handlemap->mapHandles_VkImageView((VkImageView*)&toMap->imageView);
}

void handlemap_VkDescriptorBufferInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorBufferInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkWriteDescriptorSet(
    VulkanHandleMapping* handlemap,
    VkWriteDescriptorSet* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDescriptorSet((VkDescriptorSet*)&toMap->dstSet);
    if (toMap->pImageInfo)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->descriptorCount; ++i)
        {
            handlemap_VkDescriptorImageInfo(handlemap, (VkDescriptorImageInfo*)(toMap->pImageInfo + i));
        }
    }
    if (toMap->pBufferInfo)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->descriptorCount; ++i)
        {
            handlemap_VkDescriptorBufferInfo(handlemap, (VkDescriptorBufferInfo*)(toMap->pBufferInfo + i));
        }
    }
    if (toMap->pTexelBufferView)
    {
        handlemap->mapHandles_VkBufferView((VkBufferView*)toMap->pTexelBufferView, toMap->descriptorCount);
    }
}

void handlemap_VkCopyDescriptorSet(
    VulkanHandleMapping* handlemap,
    VkCopyDescriptorSet* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDescriptorSet((VkDescriptorSet*)&toMap->srcSet);
    handlemap->mapHandles_VkDescriptorSet((VkDescriptorSet*)&toMap->dstSet);
}

void handlemap_VkFramebufferCreateInfo(
    VulkanHandleMapping* handlemap,
    VkFramebufferCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkRenderPass((VkRenderPass*)&toMap->renderPass);
    if (toMap->pAttachments)
    {
        handlemap->mapHandles_VkImageView((VkImageView*)toMap->pAttachments, toMap->attachmentCount);
    }
}

void handlemap_VkAttachmentDescription(
    VulkanHandleMapping* handlemap,
    VkAttachmentDescription* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkAttachmentReference(
    VulkanHandleMapping* handlemap,
    VkAttachmentReference* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkSubpassDescription(
    VulkanHandleMapping* handlemap,
    VkSubpassDescription* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pInputAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->inputAttachmentCount; ++i)
        {
            handlemap_VkAttachmentReference(handlemap, (VkAttachmentReference*)(toMap->pInputAttachments + i));
        }
    }
    if (toMap->pColorAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->colorAttachmentCount; ++i)
        {
            handlemap_VkAttachmentReference(handlemap, (VkAttachmentReference*)(toMap->pColorAttachments + i));
        }
    }
    if (toMap->pResolveAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->colorAttachmentCount; ++i)
        {
            handlemap_VkAttachmentReference(handlemap, (VkAttachmentReference*)(toMap->pResolveAttachments + i));
        }
    }
    if (toMap->pDepthStencilAttachment)
    {
        handlemap_VkAttachmentReference(handlemap, (VkAttachmentReference*)(toMap->pDepthStencilAttachment));
    }
}

void handlemap_VkSubpassDependency(
    VulkanHandleMapping* handlemap,
    VkSubpassDependency* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkRenderPassCreateInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->attachmentCount; ++i)
        {
            handlemap_VkAttachmentDescription(handlemap, (VkAttachmentDescription*)(toMap->pAttachments + i));
        }
    }
    if (toMap->pSubpasses)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->subpassCount; ++i)
        {
            handlemap_VkSubpassDescription(handlemap, (VkSubpassDescription*)(toMap->pSubpasses + i));
        }
    }
    if (toMap->pDependencies)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->dependencyCount; ++i)
        {
            handlemap_VkSubpassDependency(handlemap, (VkSubpassDependency*)(toMap->pDependencies + i));
        }
    }
}

void handlemap_VkCommandPoolCreateInfo(
    VulkanHandleMapping* handlemap,
    VkCommandPoolCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkCommandBufferAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkCommandBufferAllocateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkCommandPool((VkCommandPool*)&toMap->commandPool);
}

void handlemap_VkCommandBufferInheritanceInfo(
    VulkanHandleMapping* handlemap,
    VkCommandBufferInheritanceInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkRenderPass((VkRenderPass*)&toMap->renderPass);
    handlemap->mapHandles_VkFramebuffer((VkFramebuffer*)&toMap->framebuffer);
}

void handlemap_VkCommandBufferBeginInfo(
    VulkanHandleMapping* handlemap,
    VkCommandBufferBeginInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pInheritanceInfo)
    {
        handlemap_VkCommandBufferInheritanceInfo(handlemap, (VkCommandBufferInheritanceInfo*)(toMap->pInheritanceInfo));
    }
}

void handlemap_VkBufferCopy(
    VulkanHandleMapping* handlemap,
    VkBufferCopy* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkImageSubresourceLayers(
    VulkanHandleMapping* handlemap,
    VkImageSubresourceLayers* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkImageCopy(
    VulkanHandleMapping* handlemap,
    VkImageCopy* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->srcSubresource));
    handlemap_VkOffset3D(handlemap, (VkOffset3D*)(&toMap->srcOffset));
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->dstSubresource));
    handlemap_VkOffset3D(handlemap, (VkOffset3D*)(&toMap->dstOffset));
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->extent));
}

void handlemap_VkImageBlit(
    VulkanHandleMapping* handlemap,
    VkImageBlit* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->srcSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        handlemap_VkOffset3D(handlemap, (VkOffset3D*)(toMap->srcOffsets + i));
    }
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->dstSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        handlemap_VkOffset3D(handlemap, (VkOffset3D*)(toMap->dstOffsets + i));
    }
}

void handlemap_VkBufferImageCopy(
    VulkanHandleMapping* handlemap,
    VkBufferImageCopy* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->imageSubresource));
    handlemap_VkOffset3D(handlemap, (VkOffset3D*)(&toMap->imageOffset));
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->imageExtent));
}

void handlemap_VkClearColorValue(
    VulkanHandleMapping* handlemap,
    VkClearColorValue* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkClearDepthStencilValue(
    VulkanHandleMapping* handlemap,
    VkClearDepthStencilValue* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkClearValue(
    VulkanHandleMapping* handlemap,
    VkClearValue* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkClearColorValue(handlemap, (VkClearColorValue*)(&toMap->color));
    handlemap_VkClearDepthStencilValue(handlemap, (VkClearDepthStencilValue*)(&toMap->depthStencil));
}

void handlemap_VkClearAttachment(
    VulkanHandleMapping* handlemap,
    VkClearAttachment* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkClearValue(handlemap, (VkClearValue*)(&toMap->clearValue));
}

void handlemap_VkClearRect(
    VulkanHandleMapping* handlemap,
    VkClearRect* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkRect2D(handlemap, (VkRect2D*)(&toMap->rect));
}

void handlemap_VkImageResolve(
    VulkanHandleMapping* handlemap,
    VkImageResolve* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->srcSubresource));
    handlemap_VkOffset3D(handlemap, (VkOffset3D*)(&toMap->srcOffset));
    handlemap_VkImageSubresourceLayers(handlemap, (VkImageSubresourceLayers*)(&toMap->dstSubresource));
    handlemap_VkOffset3D(handlemap, (VkOffset3D*)(&toMap->dstOffset));
    handlemap_VkExtent3D(handlemap, (VkExtent3D*)(&toMap->extent));
}

void handlemap_VkMemoryBarrier(
    VulkanHandleMapping* handlemap,
    VkMemoryBarrier* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBufferMemoryBarrier(
    VulkanHandleMapping* handlemap,
    VkBufferMemoryBarrier* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkImageMemoryBarrier(
    VulkanHandleMapping* handlemap,
    VkImageMemoryBarrier* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    handlemap_VkImageSubresourceRange(handlemap, (VkImageSubresourceRange*)(&toMap->subresourceRange));
}

void handlemap_VkRenderPassBeginInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassBeginInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkRenderPass((VkRenderPass*)&toMap->renderPass);
    handlemap->mapHandles_VkFramebuffer((VkFramebuffer*)&toMap->framebuffer);
    handlemap_VkRect2D(handlemap, (VkRect2D*)(&toMap->renderArea));
    if (toMap->pClearValues)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->clearValueCount; ++i)
        {
            handlemap_VkClearValue(handlemap, (VkClearValue*)(toMap->pClearValues + i));
        }
    }
}

void handlemap_VkDispatchIndirectCommand(
    VulkanHandleMapping* handlemap,
    VkDispatchIndirectCommand* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkDrawIndexedIndirectCommand(
    VulkanHandleMapping* handlemap,
    VkDrawIndexedIndirectCommand* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkDrawIndirectCommand(
    VulkanHandleMapping* handlemap,
    VkDrawIndirectCommand* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkBaseOutStructure(
    VulkanHandleMapping* handlemap,
    VkBaseOutStructure* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBaseInStructure(
    VulkanHandleMapping* handlemap,
    VkBaseInStructure* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_VERSION_1_1
void handlemap_VkPhysicalDeviceSubgroupProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSubgroupProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBindBufferMemoryInfo(
    VulkanHandleMapping* handlemap,
    VkBindBufferMemoryInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

void handlemap_VkBindImageMemoryInfo(
    VulkanHandleMapping* handlemap,
    VkBindImageMemoryInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

void handlemap_VkPhysicalDevice16BitStorageFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevice16BitStorageFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryDedicatedRequirements(
    VulkanHandleMapping* handlemap,
    VkMemoryDedicatedRequirements* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryDedicatedAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkMemoryDedicatedAllocateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkMemoryAllocateFlagsInfo(
    VulkanHandleMapping* handlemap,
    VkMemoryAllocateFlagsInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceGroupRenderPassBeginInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupRenderPassBeginInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pDeviceRenderAreas)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->deviceRenderAreaCount; ++i)
        {
            handlemap_VkRect2D(handlemap, (VkRect2D*)(toMap->pDeviceRenderAreas + i));
        }
    }
}

void handlemap_VkDeviceGroupCommandBufferBeginInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupCommandBufferBeginInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceGroupSubmitInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupSubmitInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceGroupBindSparseInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupBindSparseInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBindBufferMemoryDeviceGroupInfo(
    VulkanHandleMapping* handlemap,
    VkBindBufferMemoryDeviceGroupInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkBindImageMemoryDeviceGroupInfo(
    VulkanHandleMapping* handlemap,
    VkBindImageMemoryDeviceGroupInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pSplitInstanceBindRegions)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->splitInstanceBindRegionCount; ++i)
        {
            handlemap_VkRect2D(handlemap, (VkRect2D*)(toMap->pSplitInstanceBindRegions + i));
        }
    }
}

void handlemap_VkPhysicalDeviceGroupProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceGroupProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkPhysicalDevice((VkPhysicalDevice*)toMap->physicalDevices, VK_MAX_DEVICE_GROUP_SIZE);
}

void handlemap_VkDeviceGroupDeviceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupDeviceCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pPhysicalDevices)
    {
        handlemap->mapHandles_VkPhysicalDevice((VkPhysicalDevice*)toMap->pPhysicalDevices, toMap->physicalDeviceCount);
    }
}

void handlemap_VkBufferMemoryRequirementsInfo2(
    VulkanHandleMapping* handlemap,
    VkBufferMemoryRequirementsInfo2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkImageMemoryRequirementsInfo2(
    VulkanHandleMapping* handlemap,
    VkImageMemoryRequirementsInfo2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
}

void handlemap_VkImageSparseMemoryRequirementsInfo2(
    VulkanHandleMapping* handlemap,
    VkImageSparseMemoryRequirementsInfo2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
}

void handlemap_VkMemoryRequirements2(
    VulkanHandleMapping* handlemap,
    VkMemoryRequirements2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkMemoryRequirements(handlemap, (VkMemoryRequirements*)(&toMap->memoryRequirements));
}

void handlemap_VkSparseImageMemoryRequirements2(
    VulkanHandleMapping* handlemap,
    VkSparseImageMemoryRequirements2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkSparseImageMemoryRequirements(handlemap, (VkSparseImageMemoryRequirements*)(&toMap->memoryRequirements));
}

void handlemap_VkPhysicalDeviceFeatures2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceFeatures2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkPhysicalDeviceFeatures(handlemap, (VkPhysicalDeviceFeatures*)(&toMap->features));
}

void handlemap_VkPhysicalDeviceProperties2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProperties2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkPhysicalDeviceProperties(handlemap, (VkPhysicalDeviceProperties*)(&toMap->properties));
}

void handlemap_VkFormatProperties2(
    VulkanHandleMapping* handlemap,
    VkFormatProperties2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkFormatProperties(handlemap, (VkFormatProperties*)(&toMap->formatProperties));
}

void handlemap_VkImageFormatProperties2(
    VulkanHandleMapping* handlemap,
    VkImageFormatProperties2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkImageFormatProperties(handlemap, (VkImageFormatProperties*)(&toMap->imageFormatProperties));
}

void handlemap_VkPhysicalDeviceImageFormatInfo2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceImageFormatInfo2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkQueueFamilyProperties2(
    VulkanHandleMapping* handlemap,
    VkQueueFamilyProperties2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkQueueFamilyProperties(handlemap, (VkQueueFamilyProperties*)(&toMap->queueFamilyProperties));
}

void handlemap_VkPhysicalDeviceMemoryProperties2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMemoryProperties2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkPhysicalDeviceMemoryProperties(handlemap, (VkPhysicalDeviceMemoryProperties*)(&toMap->memoryProperties));
}

void handlemap_VkSparseImageFormatProperties2(
    VulkanHandleMapping* handlemap,
    VkSparseImageFormatProperties2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkSparseImageFormatProperties(handlemap, (VkSparseImageFormatProperties*)(&toMap->properties));
}

void handlemap_VkPhysicalDeviceSparseImageFormatInfo2(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSparseImageFormatInfo2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDevicePointClippingProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevicePointClippingProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkInputAttachmentAspectReference(
    VulkanHandleMapping* handlemap,
    VkInputAttachmentAspectReference* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkRenderPassInputAttachmentAspectCreateInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassInputAttachmentAspectCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAspectReferences)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->aspectReferenceCount; ++i)
        {
            handlemap_VkInputAttachmentAspectReference(handlemap, (VkInputAttachmentAspectReference*)(toMap->pAspectReferences + i));
        }
    }
}

void handlemap_VkImageViewUsageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkImageViewUsageCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineTessellationDomainOriginStateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkPipelineTessellationDomainOriginStateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkRenderPassMultiviewCreateInfo(
    VulkanHandleMapping* handlemap,
    VkRenderPassMultiviewCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceMultiviewFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMultiviewFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceMultiviewProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMultiviewProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceVariablePointerFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceVariablePointerFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceProtectedMemoryFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProtectedMemoryFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceProtectedMemoryProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceProtectedMemoryProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceQueueInfo2(
    VulkanHandleMapping* handlemap,
    VkDeviceQueueInfo2* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkProtectedSubmitInfo(
    VulkanHandleMapping* handlemap,
    VkProtectedSubmitInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSamplerYcbcrConversionCreateInfo(
    VulkanHandleMapping* handlemap,
    VkSamplerYcbcrConversionCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkComponentMapping(handlemap, (VkComponentMapping*)(&toMap->components));
}

void handlemap_VkSamplerYcbcrConversionInfo(
    VulkanHandleMapping* handlemap,
    VkSamplerYcbcrConversionInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSamplerYcbcrConversion((VkSamplerYcbcrConversion*)&toMap->conversion);
}

void handlemap_VkBindImagePlaneMemoryInfo(
    VulkanHandleMapping* handlemap,
    VkBindImagePlaneMemoryInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkImagePlaneMemoryRequirementsInfo(
    VulkanHandleMapping* handlemap,
    VkImagePlaneMemoryRequirementsInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSamplerYcbcrConversionImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkSamplerYcbcrConversionImageFormatProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDescriptorUpdateTemplateEntry(
    VulkanHandleMapping* handlemap,
    VkDescriptorUpdateTemplateEntry* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkDescriptorUpdateTemplateCreateInfo(
    VulkanHandleMapping* handlemap,
    VkDescriptorUpdateTemplateCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pDescriptorUpdateEntries)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->descriptorUpdateEntryCount; ++i)
        {
            handlemap_VkDescriptorUpdateTemplateEntry(handlemap, (VkDescriptorUpdateTemplateEntry*)(toMap->pDescriptorUpdateEntries + i));
        }
    }
    handlemap->mapHandles_VkDescriptorSetLayout((VkDescriptorSetLayout*)&toMap->descriptorSetLayout);
    handlemap->mapHandles_VkPipelineLayout((VkPipelineLayout*)&toMap->pipelineLayout);
}

void handlemap_VkExternalMemoryProperties(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPhysicalDeviceExternalImageFormatInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalImageFormatInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExternalImageFormatProperties(
    VulkanHandleMapping* handlemap,
    VkExternalImageFormatProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExternalMemoryProperties(handlemap, (VkExternalMemoryProperties*)(&toMap->externalMemoryProperties));
}

void handlemap_VkPhysicalDeviceExternalBufferInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalBufferInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExternalBufferProperties(
    VulkanHandleMapping* handlemap,
    VkExternalBufferProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExternalMemoryProperties(handlemap, (VkExternalMemoryProperties*)(&toMap->externalMemoryProperties));
}

void handlemap_VkPhysicalDeviceIDProperties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceIDProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExternalMemoryImageCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryImageCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExternalMemoryBufferCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryBufferCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExportMemoryAllocateInfo(
    VulkanHandleMapping* handlemap,
    VkExportMemoryAllocateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceExternalFenceInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalFenceInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExternalFenceProperties(
    VulkanHandleMapping* handlemap,
    VkExternalFenceProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExportFenceCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExportFenceCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExportSemaphoreCreateInfo(
    VulkanHandleMapping* handlemap,
    VkExportSemaphoreCreateInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceExternalSemaphoreInfo(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalSemaphoreInfo* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExternalSemaphoreProperties(
    VulkanHandleMapping* handlemap,
    VkExternalSemaphoreProperties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceMaintenance3Properties(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMaintenance3Properties* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDescriptorSetLayoutSupport(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutSupport* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceShaderDrawParameterFeatures(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceShaderDrawParameterFeatures* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_surface
void handlemap_VkSurfaceCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceCapabilitiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->currentExtent));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->minImageExtent));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->maxImageExtent));
}

void handlemap_VkSurfaceFormatKHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceFormatKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
}

#endif
#ifdef VK_KHR_swapchain
void handlemap_VkSwapchainCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSwapchainCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSurfaceKHR((VkSurfaceKHR*)&toMap->surface);
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->imageExtent));
    handlemap->mapHandles_VkSwapchainKHR((VkSwapchainKHR*)&toMap->oldSwapchain);
}

void handlemap_VkPresentInfoKHR(
    VulkanHandleMapping* handlemap,
    VkPresentInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pWaitSemaphores)
    {
        handlemap->mapHandles_VkSemaphore((VkSemaphore*)toMap->pWaitSemaphores, toMap->waitSemaphoreCount);
    }
    if (toMap->pSwapchains)
    {
        handlemap->mapHandles_VkSwapchainKHR((VkSwapchainKHR*)toMap->pSwapchains, toMap->swapchainCount);
    }
}

void handlemap_VkImageSwapchainCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImageSwapchainCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSwapchainKHR((VkSwapchainKHR*)&toMap->swapchain);
}

void handlemap_VkBindImageMemorySwapchainInfoKHR(
    VulkanHandleMapping* handlemap,
    VkBindImageMemorySwapchainInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSwapchainKHR((VkSwapchainKHR*)&toMap->swapchain);
}

void handlemap_VkAcquireNextImageInfoKHR(
    VulkanHandleMapping* handlemap,
    VkAcquireNextImageInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSwapchainKHR((VkSwapchainKHR*)&toMap->swapchain);
    handlemap->mapHandles_VkSemaphore((VkSemaphore*)&toMap->semaphore);
    handlemap->mapHandles_VkFence((VkFence*)&toMap->fence);
}

void handlemap_VkDeviceGroupPresentCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupPresentCapabilitiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceGroupPresentInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupPresentInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceGroupSwapchainCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDeviceGroupSwapchainCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_display
void handlemap_VkDisplayPropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPropertiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkDisplayKHR((VkDisplayKHR*)&toMap->display);
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->physicalDimensions));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->physicalResolution));
}

void handlemap_VkDisplayModeParametersKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModeParametersKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->visibleRegion));
}

void handlemap_VkDisplayModePropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModePropertiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkDisplayModeKHR((VkDisplayModeKHR*)&toMap->displayMode);
    handlemap_VkDisplayModeParametersKHR(handlemap, (VkDisplayModeParametersKHR*)(&toMap->parameters));
}

void handlemap_VkDisplayModeCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModeCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkDisplayModeParametersKHR(handlemap, (VkDisplayModeParametersKHR*)(&toMap->parameters));
}

void handlemap_VkDisplayPlaneCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneCapabilitiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkOffset2D(handlemap, (VkOffset2D*)(&toMap->minSrcPosition));
    handlemap_VkOffset2D(handlemap, (VkOffset2D*)(&toMap->maxSrcPosition));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->minSrcExtent));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->maxSrcExtent));
    handlemap_VkOffset2D(handlemap, (VkOffset2D*)(&toMap->minDstPosition));
    handlemap_VkOffset2D(handlemap, (VkOffset2D*)(&toMap->maxDstPosition));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->minDstExtent));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->maxDstExtent));
}

void handlemap_VkDisplayPlanePropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlanePropertiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkDisplayKHR((VkDisplayKHR*)&toMap->currentDisplay);
}

void handlemap_VkDisplaySurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDisplaySurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDisplayModeKHR((VkDisplayModeKHR*)&toMap->displayMode);
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->imageExtent));
}

#endif
#ifdef VK_KHR_display_swapchain
void handlemap_VkDisplayPresentInfoKHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPresentInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkRect2D(handlemap, (VkRect2D*)(&toMap->srcRect));
    handlemap_VkRect2D(handlemap, (VkRect2D*)(&toMap->dstRect));
}

#endif
#ifdef VK_KHR_xlib_surface
void handlemap_VkXlibSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkXlibSurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_xcb_surface
void handlemap_VkXcbSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkXcbSurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_wayland_surface
void handlemap_VkWaylandSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkWaylandSurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_mir_surface
void handlemap_VkMirSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkMirSurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_android_surface
void handlemap_VkAndroidSurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkAndroidSurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_win32_surface
void handlemap_VkWin32SurfaceCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkWin32SurfaceCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#endif
#ifdef VK_KHR_device_group
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#endif
#ifdef VK_KHR_device_group_creation
#endif
#ifdef VK_KHR_external_memory_capabilities
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void handlemap_VkImportMemoryWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportMemoryWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExportMemoryWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkExportMemoryWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryWin32HandlePropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryWin32HandlePropertiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryGetWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryGetWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

#endif
#ifdef VK_KHR_external_memory_fd
void handlemap_VkImportMemoryFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportMemoryFdInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryFdPropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryFdPropertiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryGetFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkMemoryGetFdInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

#endif
#ifdef VK_KHR_win32_keyed_mutex
void handlemap_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    VulkanHandleMapping* handlemap,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAcquireSyncs)
    {
        handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)toMap->pAcquireSyncs, toMap->acquireCount);
    }
    if (toMap->pReleaseSyncs)
    {
        handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)toMap->pReleaseSyncs, toMap->releaseCount);
    }
}

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void handlemap_VkImportSemaphoreWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportSemaphoreWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSemaphore((VkSemaphore*)&toMap->semaphore);
}

void handlemap_VkExportSemaphoreWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkExportSemaphoreWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkD3D12FenceSubmitInfoKHR(
    VulkanHandleMapping* handlemap,
    VkD3D12FenceSubmitInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSemaphoreGetWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSemaphoreGetWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSemaphore((VkSemaphore*)&toMap->semaphore);
}

#endif
#ifdef VK_KHR_external_semaphore_fd
void handlemap_VkImportSemaphoreFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportSemaphoreFdInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSemaphore((VkSemaphore*)&toMap->semaphore);
}

void handlemap_VkSemaphoreGetFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSemaphoreGetFdInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSemaphore((VkSemaphore*)&toMap->semaphore);
}

#endif
#ifdef VK_KHR_push_descriptor
void handlemap_VkPhysicalDevicePushDescriptorPropertiesKHR(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevicePushDescriptorPropertiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void handlemap_VkRectLayerKHR(
    VulkanHandleMapping* handlemap,
    VkRectLayerKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkOffset2D(handlemap, (VkOffset2D*)(&toMap->offset));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->extent));
}

void handlemap_VkPresentRegionKHR(
    VulkanHandleMapping* handlemap,
    VkPresentRegionKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pRectangles)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->rectangleCount; ++i)
        {
            handlemap_VkRectLayerKHR(handlemap, (VkRectLayerKHR*)(toMap->pRectangles + i));
        }
    }
}

void handlemap_VkPresentRegionsKHR(
    VulkanHandleMapping* handlemap,
    VkPresentRegionsKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pRegions)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->swapchainCount; ++i)
        {
            handlemap_VkPresentRegionKHR(handlemap, (VkPresentRegionKHR*)(toMap->pRegions + i));
        }
    }
}

#endif
#ifdef VK_KHR_descriptor_update_template
#endif
#ifdef VK_KHR_create_renderpass2
void handlemap_VkAttachmentDescription2KHR(
    VulkanHandleMapping* handlemap,
    VkAttachmentDescription2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkAttachmentReference2KHR(
    VulkanHandleMapping* handlemap,
    VkAttachmentReference2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSubpassDescription2KHR(
    VulkanHandleMapping* handlemap,
    VkSubpassDescription2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pInputAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->inputAttachmentCount; ++i)
        {
            handlemap_VkAttachmentReference2KHR(handlemap, (VkAttachmentReference2KHR*)(toMap->pInputAttachments + i));
        }
    }
    if (toMap->pColorAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->colorAttachmentCount; ++i)
        {
            handlemap_VkAttachmentReference2KHR(handlemap, (VkAttachmentReference2KHR*)(toMap->pColorAttachments + i));
        }
    }
    if (toMap->pResolveAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->colorAttachmentCount; ++i)
        {
            handlemap_VkAttachmentReference2KHR(handlemap, (VkAttachmentReference2KHR*)(toMap->pResolveAttachments + i));
        }
    }
    if (toMap->pDepthStencilAttachment)
    {
        handlemap_VkAttachmentReference2KHR(handlemap, (VkAttachmentReference2KHR*)(toMap->pDepthStencilAttachment));
    }
}

void handlemap_VkSubpassDependency2KHR(
    VulkanHandleMapping* handlemap,
    VkSubpassDependency2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkRenderPassCreateInfo2KHR(
    VulkanHandleMapping* handlemap,
    VkRenderPassCreateInfo2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAttachments)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->attachmentCount; ++i)
        {
            handlemap_VkAttachmentDescription2KHR(handlemap, (VkAttachmentDescription2KHR*)(toMap->pAttachments + i));
        }
    }
    if (toMap->pSubpasses)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->subpassCount; ++i)
        {
            handlemap_VkSubpassDescription2KHR(handlemap, (VkSubpassDescription2KHR*)(toMap->pSubpasses + i));
        }
    }
    if (toMap->pDependencies)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->dependencyCount; ++i)
        {
            handlemap_VkSubpassDependency2KHR(handlemap, (VkSubpassDependency2KHR*)(toMap->pDependencies + i));
        }
    }
}

void handlemap_VkSubpassBeginInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSubpassBeginInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSubpassEndInfoKHR(
    VulkanHandleMapping* handlemap,
    VkSubpassEndInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_shared_presentable_image
void handlemap_VkSharedPresentSurfaceCapabilitiesKHR(
    VulkanHandleMapping* handlemap,
    VkSharedPresentSurfaceCapabilitiesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_external_fence_capabilities
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void handlemap_VkImportFenceWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportFenceWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkFence((VkFence*)&toMap->fence);
}

void handlemap_VkExportFenceWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkExportFenceWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkFenceGetWin32HandleInfoKHR(
    VulkanHandleMapping* handlemap,
    VkFenceGetWin32HandleInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkFence((VkFence*)&toMap->fence);
}

#endif
#ifdef VK_KHR_external_fence_fd
void handlemap_VkImportFenceFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImportFenceFdInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkFence((VkFence*)&toMap->fence);
}

void handlemap_VkFenceGetFdInfoKHR(
    VulkanHandleMapping* handlemap,
    VkFenceGetFdInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkFence((VkFence*)&toMap->fence);
}

#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void handlemap_VkPhysicalDeviceSurfaceInfo2KHR(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSurfaceInfo2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkSurfaceKHR((VkSurfaceKHR*)&toMap->surface);
}

void handlemap_VkSurfaceCapabilities2KHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceCapabilities2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkSurfaceCapabilitiesKHR(handlemap, (VkSurfaceCapabilitiesKHR*)(&toMap->surfaceCapabilities));
}

void handlemap_VkSurfaceFormat2KHR(
    VulkanHandleMapping* handlemap,
    VkSurfaceFormat2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkSurfaceFormatKHR(handlemap, (VkSurfaceFormatKHR*)(&toMap->surfaceFormat));
}

#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void handlemap_VkDisplayProperties2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayProperties2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkDisplayPropertiesKHR(handlemap, (VkDisplayPropertiesKHR*)(&toMap->displayProperties));
}

void handlemap_VkDisplayPlaneProperties2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneProperties2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkDisplayPlanePropertiesKHR(handlemap, (VkDisplayPlanePropertiesKHR*)(&toMap->displayPlaneProperties));
}

void handlemap_VkDisplayModeProperties2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayModeProperties2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkDisplayModePropertiesKHR(handlemap, (VkDisplayModePropertiesKHR*)(&toMap->displayModeProperties));
}

void handlemap_VkDisplayPlaneInfo2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneInfo2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDisplayModeKHR((VkDisplayModeKHR*)&toMap->mode);
}

void handlemap_VkDisplayPlaneCapabilities2KHR(
    VulkanHandleMapping* handlemap,
    VkDisplayPlaneCapabilities2KHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkDisplayPlaneCapabilitiesKHR(handlemap, (VkDisplayPlaneCapabilitiesKHR*)(&toMap->capabilities));
}

#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#endif
#ifdef VK_KHR_image_format_list
void handlemap_VkImageFormatListCreateInfoKHR(
    VulkanHandleMapping* handlemap,
    VkImageFormatListCreateInfoKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#endif
#ifdef VK_KHR_bind_memory2
#endif
#ifdef VK_KHR_maintenance3
#endif
#ifdef VK_KHR_draw_indirect_count
#endif
#ifdef VK_KHR_8bit_storage
void handlemap_VkPhysicalDevice8BitStorageFeaturesKHR(
    VulkanHandleMapping* handlemap,
    VkPhysicalDevice8BitStorageFeaturesKHR* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_ANDROID_native_buffer
void handlemap_VkNativeBufferANDROID(
    VulkanHandleMapping* handlemap,
    VkNativeBufferANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_debug_report
void handlemap_VkDebugReportCallbackCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugReportCallbackCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void handlemap_VkPipelineRasterizationStateRasterizationOrderAMD(
    VulkanHandleMapping* handlemap,
    VkPipelineRasterizationStateRasterizationOrderAMD* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void handlemap_VkDebugMarkerObjectNameInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugMarkerObjectNameInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDebugMarkerObjectTagInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugMarkerObjectTagInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDebugMarkerMarkerInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugMarkerMarkerInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void handlemap_VkDedicatedAllocationImageCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkDedicatedAllocationImageCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDedicatedAllocationBufferCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkDedicatedAllocationBufferCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDedicatedAllocationMemoryAllocateInfoNV(
    VulkanHandleMapping* handlemap,
    VkDedicatedAllocationMemoryAllocateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkImage((VkImage*)&toMap->image);
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

#endif
#ifdef VK_AMD_draw_indirect_count
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void handlemap_VkTextureLODGatherFormatPropertiesAMD(
    VulkanHandleMapping* handlemap,
    VkTextureLODGatherFormatPropertiesAMD* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_AMD_shader_info
void handlemap_VkShaderResourceUsageAMD(
    VulkanHandleMapping* handlemap,
    VkShaderResourceUsageAMD* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkShaderStatisticsInfoAMD(
    VulkanHandleMapping* handlemap,
    VkShaderStatisticsInfoAMD* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkShaderResourceUsageAMD(handlemap, (VkShaderResourceUsageAMD*)(&toMap->resourceUsage));
}

#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void handlemap_VkExternalImageFormatPropertiesNV(
    VulkanHandleMapping* handlemap,
    VkExternalImageFormatPropertiesNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkImageFormatProperties(handlemap, (VkImageFormatProperties*)(&toMap->imageFormatProperties));
}

#endif
#ifdef VK_NV_external_memory
void handlemap_VkExternalMemoryImageCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkExternalMemoryImageCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExportMemoryAllocateInfoNV(
    VulkanHandleMapping* handlemap,
    VkExportMemoryAllocateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_external_memory_win32
void handlemap_VkImportMemoryWin32HandleInfoNV(
    VulkanHandleMapping* handlemap,
    VkImportMemoryWin32HandleInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkExportMemoryWin32HandleInfoNV(
    VulkanHandleMapping* handlemap,
    VkExportMemoryWin32HandleInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_win32_keyed_mutex
void handlemap_VkWin32KeyedMutexAcquireReleaseInfoNV(
    VulkanHandleMapping* handlemap,
    VkWin32KeyedMutexAcquireReleaseInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAcquireSyncs)
    {
        handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)toMap->pAcquireSyncs, toMap->acquireCount);
    }
    if (toMap->pReleaseSyncs)
    {
        handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)toMap->pReleaseSyncs, toMap->releaseCount);
    }
}

#endif
#ifdef VK_EXT_validation_flags
void handlemap_VkValidationFlagsEXT(
    VulkanHandleMapping* handlemap,
    VkValidationFlagsEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NN_vi_surface
void handlemap_VkViSurfaceCreateInfoNN(
    VulkanHandleMapping* handlemap,
    VkViSurfaceCreateInfoNN* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void handlemap_VkConditionalRenderingBeginInfoEXT(
    VulkanHandleMapping* handlemap,
    VkConditionalRenderingBeginInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    VulkanHandleMapping* handlemap,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NVX_device_generated_commands
void handlemap_VkDeviceGeneratedCommandsFeaturesNVX(
    VulkanHandleMapping* handlemap,
    VkDeviceGeneratedCommandsFeaturesNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceGeneratedCommandsLimitsNVX(
    VulkanHandleMapping* handlemap,
    VkDeviceGeneratedCommandsLimitsNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkIndirectCommandsTokenNVX(
    VulkanHandleMapping* handlemap,
    VkIndirectCommandsTokenNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkIndirectCommandsLayoutTokenNVX(
    VulkanHandleMapping* handlemap,
    VkIndirectCommandsLayoutTokenNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkIndirectCommandsLayoutCreateInfoNVX(
    VulkanHandleMapping* handlemap,
    VkIndirectCommandsLayoutCreateInfoNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pTokens)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->tokenCount; ++i)
        {
            handlemap_VkIndirectCommandsLayoutTokenNVX(handlemap, (VkIndirectCommandsLayoutTokenNVX*)(toMap->pTokens + i));
        }
    }
}

void handlemap_VkCmdProcessCommandsInfoNVX(
    VulkanHandleMapping* handlemap,
    VkCmdProcessCommandsInfoNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkObjectTableNVX((VkObjectTableNVX*)&toMap->objectTable);
    handlemap->mapHandles_VkIndirectCommandsLayoutNVX((VkIndirectCommandsLayoutNVX*)&toMap->indirectCommandsLayout);
    if (toMap->pIndirectCommandsTokens)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->indirectCommandsTokenCount; ++i)
        {
            handlemap_VkIndirectCommandsTokenNVX(handlemap, (VkIndirectCommandsTokenNVX*)(toMap->pIndirectCommandsTokens + i));
        }
    }
    handlemap->mapHandles_VkCommandBuffer((VkCommandBuffer*)&toMap->targetCommandBuffer);
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->sequencesCountBuffer);
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->sequencesIndexBuffer);
}

void handlemap_VkCmdReserveSpaceForCommandsInfoNVX(
    VulkanHandleMapping* handlemap,
    VkCmdReserveSpaceForCommandsInfoNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkObjectTableNVX((VkObjectTableNVX*)&toMap->objectTable);
    handlemap->mapHandles_VkIndirectCommandsLayoutNVX((VkIndirectCommandsLayoutNVX*)&toMap->indirectCommandsLayout);
}

void handlemap_VkObjectTableCreateInfoNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableCreateInfoNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkObjectTableEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableEntryNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkObjectTablePipelineEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTablePipelineEntryNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkPipeline((VkPipeline*)&toMap->pipeline);
}

void handlemap_VkObjectTableDescriptorSetEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableDescriptorSetEntryNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkPipelineLayout((VkPipelineLayout*)&toMap->pipelineLayout);
    handlemap->mapHandles_VkDescriptorSet((VkDescriptorSet*)&toMap->descriptorSet);
}

void handlemap_VkObjectTableVertexBufferEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableVertexBufferEntryNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkObjectTableIndexBufferEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTableIndexBufferEntryNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkBuffer((VkBuffer*)&toMap->buffer);
}

void handlemap_VkObjectTablePushConstantEntryNVX(
    VulkanHandleMapping* handlemap,
    VkObjectTablePushConstantEntryNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap->mapHandles_VkPipelineLayout((VkPipelineLayout*)&toMap->pipelineLayout);
}

#endif
#ifdef VK_NV_clip_space_w_scaling
void handlemap_VkViewportWScalingNV(
    VulkanHandleMapping* handlemap,
    VkViewportWScalingNV* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineViewportWScalingStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineViewportWScalingStateCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pViewportWScalings)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->viewportCount; ++i)
        {
            handlemap_VkViewportWScalingNV(handlemap, (VkViewportWScalingNV*)(toMap->pViewportWScalings + i));
        }
    }
}

#endif
#ifdef VK_EXT_direct_mode_display
#endif
#ifdef VK_EXT_acquire_xlib_display
#endif
#ifdef VK_EXT_display_surface_counter
void handlemap_VkSurfaceCapabilities2EXT(
    VulkanHandleMapping* handlemap,
    VkSurfaceCapabilities2EXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->currentExtent));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->minImageExtent));
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->maxImageExtent));
}

#endif
#ifdef VK_EXT_display_control
void handlemap_VkDisplayPowerInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDisplayPowerInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDeviceEventInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDeviceEventInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDisplayEventInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDisplayEventInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkSwapchainCounterCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkSwapchainCounterCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_display_timing
void handlemap_VkRefreshCycleDurationGOOGLE(
    VulkanHandleMapping* handlemap,
    VkRefreshCycleDurationGOOGLE* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPastPresentationTimingGOOGLE(
    VulkanHandleMapping* handlemap,
    VkPastPresentationTimingGOOGLE* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPresentTimeGOOGLE(
    VulkanHandleMapping* handlemap,
    VkPresentTimeGOOGLE* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPresentTimesInfoGOOGLE(
    VulkanHandleMapping* handlemap,
    VkPresentTimesInfoGOOGLE* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pTimes)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->swapchainCount; ++i)
        {
            handlemap_VkPresentTimeGOOGLE(handlemap, (VkPresentTimeGOOGLE*)(toMap->pTimes + i));
        }
    }
}

#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void handlemap_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_viewport_swizzle
void handlemap_VkViewportSwizzleNV(
    VulkanHandleMapping* handlemap,
    VkViewportSwizzleNV* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineViewportSwizzleStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineViewportSwizzleStateCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pViewportSwizzles)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->viewportCount; ++i)
        {
            handlemap_VkViewportSwizzleNV(handlemap, (VkViewportSwizzleNV*)(toMap->pViewportSwizzles + i));
        }
    }
}

#endif
#ifdef VK_EXT_discard_rectangles
void handlemap_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineDiscardRectangleStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineDiscardRectangleStateCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pDiscardRectangles)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->discardRectangleCount; ++i)
        {
            handlemap_VkRect2D(handlemap, (VkRect2D*)(toMap->pDiscardRectangles + i));
        }
    }
}

#endif
#ifdef VK_EXT_conservative_rasterization
void handlemap_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void handlemap_VkXYColorEXT(
    VulkanHandleMapping* handlemap,
    VkXYColorEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkHdrMetadataEXT(
    VulkanHandleMapping* handlemap,
    VkHdrMetadataEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkXYColorEXT(handlemap, (VkXYColorEXT*)(&toMap->displayPrimaryRed));
    handlemap_VkXYColorEXT(handlemap, (VkXYColorEXT*)(&toMap->displayPrimaryGreen));
    handlemap_VkXYColorEXT(handlemap, (VkXYColorEXT*)(&toMap->displayPrimaryBlue));
    handlemap_VkXYColorEXT(handlemap, (VkXYColorEXT*)(&toMap->whitePoint));
}

#endif
#ifdef VK_MVK_ios_surface
void handlemap_VkIOSSurfaceCreateInfoMVK(
    VulkanHandleMapping* handlemap,
    VkIOSSurfaceCreateInfoMVK* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_MVK_macos_surface
void handlemap_VkMacOSSurfaceCreateInfoMVK(
    VulkanHandleMapping* handlemap,
    VkMacOSSurfaceCreateInfoMVK* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void handlemap_VkDebugUtilsObjectNameInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsObjectNameInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDebugUtilsObjectTagInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsObjectTagInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDebugUtilsLabelEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsLabelEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDebugUtilsMessengerCallbackDataEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsMessengerCallbackDataEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pQueueLabels)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->queueLabelCount; ++i)
        {
            handlemap_VkDebugUtilsLabelEXT(handlemap, (VkDebugUtilsLabelEXT*)(toMap->pQueueLabels + i));
        }
    }
    if (toMap->pCmdBufLabels)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->cmdBufLabelCount; ++i)
        {
            handlemap_VkDebugUtilsLabelEXT(handlemap, (VkDebugUtilsLabelEXT*)(toMap->pCmdBufLabels + i));
        }
    }
    if (toMap->pObjects)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->objectCount; ++i)
        {
            handlemap_VkDebugUtilsObjectNameInfoEXT(handlemap, (VkDebugUtilsObjectNameInfoEXT*)(toMap->pObjects + i));
        }
    }
}

void handlemap_VkDebugUtilsMessengerCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDebugUtilsMessengerCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void handlemap_VkAndroidHardwareBufferUsageANDROID(
    VulkanHandleMapping* handlemap,
    VkAndroidHardwareBufferUsageANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkAndroidHardwareBufferPropertiesANDROID(
    VulkanHandleMapping* handlemap,
    VkAndroidHardwareBufferPropertiesANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkAndroidHardwareBufferFormatPropertiesANDROID(
    VulkanHandleMapping* handlemap,
    VkAndroidHardwareBufferFormatPropertiesANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkComponentMapping(handlemap, (VkComponentMapping*)(&toMap->samplerYcbcrConversionComponents));
}

void handlemap_VkImportAndroidHardwareBufferInfoANDROID(
    VulkanHandleMapping* handlemap,
    VkImportAndroidHardwareBufferInfoANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    VulkanHandleMapping* handlemap,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkDeviceMemory((VkDeviceMemory*)&toMap->memory);
}

void handlemap_VkExternalFormatANDROID(
    VulkanHandleMapping* handlemap,
    VkExternalFormatANDROID* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_sampler_filter_minmax
void handlemap_VkSamplerReductionModeCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkSamplerReductionModeCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void handlemap_VkSampleLocationEXT(
    VulkanHandleMapping* handlemap,
    VkSampleLocationEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkSampleLocationsInfoEXT(
    VulkanHandleMapping* handlemap,
    VkSampleLocationsInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->sampleLocationGridSize));
    if (toMap->pSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->sampleLocationsCount; ++i)
        {
            handlemap_VkSampleLocationEXT(handlemap, (VkSampleLocationEXT*)(toMap->pSampleLocations + i));
        }
    }
}

void handlemap_VkAttachmentSampleLocationsEXT(
    VulkanHandleMapping* handlemap,
    VkAttachmentSampleLocationsEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkSampleLocationsInfoEXT(handlemap, (VkSampleLocationsInfoEXT*)(&toMap->sampleLocationsInfo));
}

void handlemap_VkSubpassSampleLocationsEXT(
    VulkanHandleMapping* handlemap,
    VkSubpassSampleLocationsEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    handlemap_VkSampleLocationsInfoEXT(handlemap, (VkSampleLocationsInfoEXT*)(&toMap->sampleLocationsInfo));
}

void handlemap_VkRenderPassSampleLocationsBeginInfoEXT(
    VulkanHandleMapping* handlemap,
    VkRenderPassSampleLocationsBeginInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pAttachmentInitialSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->attachmentInitialSampleLocationsCount; ++i)
        {
            handlemap_VkAttachmentSampleLocationsEXT(handlemap, (VkAttachmentSampleLocationsEXT*)(toMap->pAttachmentInitialSampleLocations + i));
        }
    }
    if (toMap->pPostSubpassSampleLocations)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->postSubpassSampleLocationsCount; ++i)
        {
            handlemap_VkSubpassSampleLocationsEXT(handlemap, (VkSubpassSampleLocationsEXT*)(toMap->pPostSubpassSampleLocations + i));
        }
    }
}

void handlemap_VkPipelineSampleLocationsStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineSampleLocationsStateCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkSampleLocationsInfoEXT(handlemap, (VkSampleLocationsInfoEXT*)(&toMap->sampleLocationsInfo));
}

void handlemap_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->maxSampleLocationGridSize));
}

void handlemap_VkMultisamplePropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkMultisamplePropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap_VkExtent2D(handlemap, (VkExtent2D*)(&toMap->maxSampleLocationGridSize));
}

#endif
#ifdef VK_EXT_blend_operation_advanced
void handlemap_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_fragment_coverage_to_color
void handlemap_VkPipelineCoverageToColorStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineCoverageToColorStateCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void handlemap_VkPipelineCoverageModulationStateCreateInfoNV(
    VulkanHandleMapping* handlemap,
    VkPipelineCoverageModulationStateCreateInfoNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void handlemap_VkValidationCacheCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkValidationCacheCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkShaderModuleValidationCacheCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkShaderModuleValidationCacheCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    handlemap->mapHandles_VkValidationCacheEXT((VkValidationCacheEXT*)&toMap->validationCache);
}

#endif
#ifdef VK_EXT_descriptor_indexing
void handlemap_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    VulkanHandleMapping* handlemap,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void handlemap_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_external_memory_host
void handlemap_VkImportMemoryHostPointerInfoEXT(
    VulkanHandleMapping* handlemap,
    VkImportMemoryHostPointerInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkMemoryHostPointerPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkMemoryHostPointerPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_AMD_buffer_marker
#endif
#ifdef VK_AMD_shader_core_properties
void handlemap_VkPhysicalDeviceShaderCorePropertiesAMD(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceShaderCorePropertiesAMD* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void handlemap_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    VulkanHandleMapping* handlemap,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkVertexInputBindingDivisorDescriptionEXT(
    VulkanHandleMapping* handlemap,
    VkVertexInputBindingDivisorDescriptionEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
}

void handlemap_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    VulkanHandleMapping* handlemap,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
    if (toMap->pVertexBindingDivisors)
    {
        for (uint32_t i = 0; i < (uint32_t)toMap->vertexBindingDivisorCount; ++i)
        {
            handlemap_VkVertexInputBindingDivisorDescriptionEXT(handlemap, (VkVertexInputBindingDivisorDescriptionEXT*)(toMap->pVertexBindingDivisors + i));
        }
    }
}

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void handlemap_VkQueueFamilyCheckpointPropertiesNV(
    VulkanHandleMapping* handlemap,
    VkQueueFamilyCheckpointPropertiesNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkCheckpointDataNV(
    VulkanHandleMapping* handlemap,
    VkCheckpointDataNV* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_address_space
#endif
#ifdef VK_GOOGLE_color_buffer
void handlemap_VkImportColorBufferGOOGLE(
    VulkanHandleMapping* handlemap,
    VkImportColorBufferGOOGLE* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

void handlemap_VkImportPhysicalAddressGOOGLE(
    VulkanHandleMapping* handlemap,
    VkImportPhysicalAddressGOOGLE* toMap)
{
    (void)handlemap;
    (void)toMap;
    if (toMap->pNext)
    {
        handlemap_extension_struct(handlemap, (void*)(toMap->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#endif
#ifdef VK_GOOGLE_async_command_buffers
#endif
void handlemap_extension_struct(
    VulkanHandleMapping* handlemap,
    void* structExtension_out)
{
    if (!structExtension_out)
    {
        return;
    }
    uint32_t structType = (uint32_t)goldfish_vk_struct_type(structExtension_out);
    switch(structType)
    {
#ifdef VK_VERSION_1_1
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SUBGROUP_PROPERTIES:
        {
            handlemap_VkPhysicalDeviceSubgroupProperties(handlemap, reinterpret_cast<VkPhysicalDeviceSubgroupProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_16BIT_STORAGE_FEATURES:
        {
            handlemap_VkPhysicalDevice16BitStorageFeatures(handlemap, reinterpret_cast<VkPhysicalDevice16BitStorageFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_REQUIREMENTS:
        {
            handlemap_VkMemoryDedicatedRequirements(handlemap, reinterpret_cast<VkMemoryDedicatedRequirements*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO:
        {
            handlemap_VkMemoryDedicatedAllocateInfo(handlemap, reinterpret_cast<VkMemoryDedicatedAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_FLAGS_INFO:
        {
            handlemap_VkMemoryAllocateFlagsInfo(handlemap, reinterpret_cast<VkMemoryAllocateFlagsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_RENDER_PASS_BEGIN_INFO:
        {
            handlemap_VkDeviceGroupRenderPassBeginInfo(handlemap, reinterpret_cast<VkDeviceGroupRenderPassBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_COMMAND_BUFFER_BEGIN_INFO:
        {
            handlemap_VkDeviceGroupCommandBufferBeginInfo(handlemap, reinterpret_cast<VkDeviceGroupCommandBufferBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SUBMIT_INFO:
        {
            handlemap_VkDeviceGroupSubmitInfo(handlemap, reinterpret_cast<VkDeviceGroupSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_BIND_SPARSE_INFO:
        {
            handlemap_VkDeviceGroupBindSparseInfo(handlemap, reinterpret_cast<VkDeviceGroupBindSparseInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_BUFFER_MEMORY_DEVICE_GROUP_INFO:
        {
            handlemap_VkBindBufferMemoryDeviceGroupInfo(handlemap, reinterpret_cast<VkBindBufferMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_DEVICE_GROUP_INFO:
        {
            handlemap_VkBindImageMemoryDeviceGroupInfo(handlemap, reinterpret_cast<VkBindImageMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_DEVICE_CREATE_INFO:
        {
            handlemap_VkDeviceGroupDeviceCreateInfo(handlemap, reinterpret_cast<VkDeviceGroupDeviceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2:
        {
            handlemap_VkPhysicalDeviceFeatures2(handlemap, reinterpret_cast<VkPhysicalDeviceFeatures2*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_POINT_CLIPPING_PROPERTIES:
        {
            handlemap_VkPhysicalDevicePointClippingProperties(handlemap, reinterpret_cast<VkPhysicalDevicePointClippingProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_INPUT_ATTACHMENT_ASPECT_CREATE_INFO:
        {
            handlemap_VkRenderPassInputAttachmentAspectCreateInfo(handlemap, reinterpret_cast<VkRenderPassInputAttachmentAspectCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_VIEW_USAGE_CREATE_INFO:
        {
            handlemap_VkImageViewUsageCreateInfo(handlemap, reinterpret_cast<VkImageViewUsageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_DOMAIN_ORIGIN_STATE_CREATE_INFO:
        {
            handlemap_VkPipelineTessellationDomainOriginStateCreateInfo(handlemap, reinterpret_cast<VkPipelineTessellationDomainOriginStateCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_MULTIVIEW_CREATE_INFO:
        {
            handlemap_VkRenderPassMultiviewCreateInfo(handlemap, reinterpret_cast<VkRenderPassMultiviewCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_FEATURES:
        {
            handlemap_VkPhysicalDeviceMultiviewFeatures(handlemap, reinterpret_cast<VkPhysicalDeviceMultiviewFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PROPERTIES:
        {
            handlemap_VkPhysicalDeviceMultiviewProperties(handlemap, reinterpret_cast<VkPhysicalDeviceMultiviewProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTER_FEATURES:
        {
            handlemap_VkPhysicalDeviceVariablePointerFeatures(handlemap, reinterpret_cast<VkPhysicalDeviceVariablePointerFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_FEATURES:
        {
            handlemap_VkPhysicalDeviceProtectedMemoryFeatures(handlemap, reinterpret_cast<VkPhysicalDeviceProtectedMemoryFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_PROPERTIES:
        {
            handlemap_VkPhysicalDeviceProtectedMemoryProperties(handlemap, reinterpret_cast<VkPhysicalDeviceProtectedMemoryProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PROTECTED_SUBMIT_INFO:
        {
            handlemap_VkProtectedSubmitInfo(handlemap, reinterpret_cast<VkProtectedSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_INFO:
        {
            handlemap_VkSamplerYcbcrConversionInfo(handlemap, reinterpret_cast<VkSamplerYcbcrConversionInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_PLANE_MEMORY_INFO:
        {
            handlemap_VkBindImagePlaneMemoryInfo(handlemap, reinterpret_cast<VkBindImagePlaneMemoryInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_PLANE_MEMORY_REQUIREMENTS_INFO:
        {
            handlemap_VkImagePlaneMemoryRequirementsInfo(handlemap, reinterpret_cast<VkImagePlaneMemoryRequirementsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_YCBCR_CONVERSION_FEATURES:
        {
            handlemap_VkPhysicalDeviceSamplerYcbcrConversionFeatures(handlemap, reinterpret_cast<VkPhysicalDeviceSamplerYcbcrConversionFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_IMAGE_FORMAT_PROPERTIES:
        {
            handlemap_VkSamplerYcbcrConversionImageFormatProperties(handlemap, reinterpret_cast<VkSamplerYcbcrConversionImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_IMAGE_FORMAT_INFO:
        {
            handlemap_VkPhysicalDeviceExternalImageFormatInfo(handlemap, reinterpret_cast<VkPhysicalDeviceExternalImageFormatInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_IMAGE_FORMAT_PROPERTIES:
        {
            handlemap_VkExternalImageFormatProperties(handlemap, reinterpret_cast<VkExternalImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ID_PROPERTIES:
        {
            handlemap_VkPhysicalDeviceIDProperties(handlemap, reinterpret_cast<VkPhysicalDeviceIDProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO:
        {
            handlemap_VkExternalMemoryImageCreateInfo(handlemap, reinterpret_cast<VkExternalMemoryImageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_BUFFER_CREATE_INFO:
        {
            handlemap_VkExternalMemoryBufferCreateInfo(handlemap, reinterpret_cast<VkExternalMemoryBufferCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO:
        {
            handlemap_VkExportMemoryAllocateInfo(handlemap, reinterpret_cast<VkExportMemoryAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_CREATE_INFO:
        {
            handlemap_VkExportFenceCreateInfo(handlemap, reinterpret_cast<VkExportFenceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_CREATE_INFO:
        {
            handlemap_VkExportSemaphoreCreateInfo(handlemap, reinterpret_cast<VkExportSemaphoreCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MAINTENANCE_3_PROPERTIES:
        {
            handlemap_VkPhysicalDeviceMaintenance3Properties(handlemap, reinterpret_cast<VkPhysicalDeviceMaintenance3Properties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETER_FEATURES:
        {
            handlemap_VkPhysicalDeviceShaderDrawParameterFeatures(handlemap, reinterpret_cast<VkPhysicalDeviceShaderDrawParameterFeatures*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_swapchain
        case VK_STRUCTURE_TYPE_IMAGE_SWAPCHAIN_CREATE_INFO_KHR:
        {
            handlemap_VkImageSwapchainCreateInfoKHR(handlemap, reinterpret_cast<VkImageSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_SWAPCHAIN_INFO_KHR:
        {
            handlemap_VkBindImageMemorySwapchainInfoKHR(handlemap, reinterpret_cast<VkBindImageMemorySwapchainInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_PRESENT_INFO_KHR:
        {
            handlemap_VkDeviceGroupPresentInfoKHR(handlemap, reinterpret_cast<VkDeviceGroupPresentInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SWAPCHAIN_CREATE_INFO_KHR:
        {
            handlemap_VkDeviceGroupSwapchainCreateInfoKHR(handlemap, reinterpret_cast<VkDeviceGroupSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_display_swapchain
        case VK_STRUCTURE_TYPE_DISPLAY_PRESENT_INFO_KHR:
        {
            handlemap_VkDisplayPresentInfoKHR(handlemap, reinterpret_cast<VkDisplayPresentInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            handlemap_VkImportMemoryWin32HandleInfoKHR(handlemap, reinterpret_cast<VkImportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            handlemap_VkExportMemoryWin32HandleInfoKHR(handlemap, reinterpret_cast<VkExportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_fd
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_FD_INFO_KHR:
        {
            handlemap_VkImportMemoryFdInfoKHR(handlemap, reinterpret_cast<VkImportMemoryFdInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_KHR:
        {
            handlemap_VkWin32KeyedMutexAcquireReleaseInfoKHR(handlemap, reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_semaphore_win32
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_WIN32_HANDLE_INFO_KHR:
        {
            handlemap_VkExportSemaphoreWin32HandleInfoKHR(handlemap, reinterpret_cast<VkExportSemaphoreWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_D3D12_FENCE_SUBMIT_INFO_KHR:
        {
            handlemap_VkD3D12FenceSubmitInfoKHR(handlemap, reinterpret_cast<VkD3D12FenceSubmitInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_push_descriptor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PUSH_DESCRIPTOR_PROPERTIES_KHR:
        {
            handlemap_VkPhysicalDevicePushDescriptorPropertiesKHR(handlemap, reinterpret_cast<VkPhysicalDevicePushDescriptorPropertiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_incremental_present
        case VK_STRUCTURE_TYPE_PRESENT_REGIONS_KHR:
        {
            handlemap_VkPresentRegionsKHR(handlemap, reinterpret_cast<VkPresentRegionsKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_shared_presentable_image
        case VK_STRUCTURE_TYPE_SHARED_PRESENT_SURFACE_CAPABILITIES_KHR:
        {
            handlemap_VkSharedPresentSurfaceCapabilitiesKHR(handlemap, reinterpret_cast<VkSharedPresentSurfaceCapabilitiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_fence_win32
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_WIN32_HANDLE_INFO_KHR:
        {
            handlemap_VkExportFenceWin32HandleInfoKHR(handlemap, reinterpret_cast<VkExportFenceWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_image_format_list
        case VK_STRUCTURE_TYPE_IMAGE_FORMAT_LIST_CREATE_INFO_KHR:
        {
            handlemap_VkImageFormatListCreateInfoKHR(handlemap, reinterpret_cast<VkImageFormatListCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_8bit_storage
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_8BIT_STORAGE_FEATURES_KHR:
        {
            handlemap_VkPhysicalDevice8BitStorageFeaturesKHR(handlemap, reinterpret_cast<VkPhysicalDevice8BitStorageFeaturesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_native_buffer
        case VK_STRUCTURE_TYPE_NATIVE_BUFFER_ANDROID:
        {
            handlemap_VkNativeBufferANDROID(handlemap, reinterpret_cast<VkNativeBufferANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_report
        case VK_STRUCTURE_TYPE_DEBUG_REPORT_CALLBACK_CREATE_INFO_EXT:
        {
            handlemap_VkDebugReportCallbackCreateInfoEXT(handlemap, reinterpret_cast<VkDebugReportCallbackCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_rasterization_order
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_RASTERIZATION_ORDER_AMD:
        {
            handlemap_VkPipelineRasterizationStateRasterizationOrderAMD(handlemap, reinterpret_cast<VkPipelineRasterizationStateRasterizationOrderAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_dedicated_allocation
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_IMAGE_CREATE_INFO_NV:
        {
            handlemap_VkDedicatedAllocationImageCreateInfoNV(handlemap, reinterpret_cast<VkDedicatedAllocationImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_BUFFER_CREATE_INFO_NV:
        {
            handlemap_VkDedicatedAllocationBufferCreateInfoNV(handlemap, reinterpret_cast<VkDedicatedAllocationBufferCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_MEMORY_ALLOCATE_INFO_NV:
        {
            handlemap_VkDedicatedAllocationMemoryAllocateInfoNV(handlemap, reinterpret_cast<VkDedicatedAllocationMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_texture_gather_bias_lod
        case VK_STRUCTURE_TYPE_TEXTURE_LOD_GATHER_FORMAT_PROPERTIES_AMD:
        {
            handlemap_VkTextureLODGatherFormatPropertiesAMD(handlemap, reinterpret_cast<VkTextureLODGatherFormatPropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO_NV:
        {
            handlemap_VkExternalMemoryImageCreateInfoNV(handlemap, reinterpret_cast<VkExternalMemoryImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO_NV:
        {
            handlemap_VkExportMemoryAllocateInfoNV(handlemap, reinterpret_cast<VkExportMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            handlemap_VkImportMemoryWin32HandleInfoNV(handlemap, reinterpret_cast<VkImportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            handlemap_VkExportMemoryWin32HandleInfoNV(handlemap, reinterpret_cast<VkExportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_NV:
        {
            handlemap_VkWin32KeyedMutexAcquireReleaseInfoNV(handlemap, reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_flags
        case VK_STRUCTURE_TYPE_VALIDATION_FLAGS_EXT:
        {
            handlemap_VkValidationFlagsEXT(handlemap, reinterpret_cast<VkValidationFlagsEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conditional_rendering
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONDITIONAL_RENDERING_FEATURES_EXT:
        {
            handlemap_VkPhysicalDeviceConditionalRenderingFeaturesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceConditionalRenderingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_COMMAND_BUFFER_INHERITANCE_CONDITIONAL_RENDERING_INFO_EXT:
        {
            handlemap_VkCommandBufferInheritanceConditionalRenderingInfoEXT(handlemap, reinterpret_cast<VkCommandBufferInheritanceConditionalRenderingInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_clip_space_w_scaling
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_W_SCALING_STATE_CREATE_INFO_NV:
        {
            handlemap_VkPipelineViewportWScalingStateCreateInfoNV(handlemap, reinterpret_cast<VkPipelineViewportWScalingStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_display_control
        case VK_STRUCTURE_TYPE_SWAPCHAIN_COUNTER_CREATE_INFO_EXT:
        {
            handlemap_VkSwapchainCounterCreateInfoEXT(handlemap, reinterpret_cast<VkSwapchainCounterCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_display_timing
        case VK_STRUCTURE_TYPE_PRESENT_TIMES_INFO_GOOGLE:
        {
            handlemap_VkPresentTimesInfoGOOGLE(handlemap, reinterpret_cast<VkPresentTimesInfoGOOGLE*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NVX_multiview_per_view_attributes
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PER_VIEW_ATTRIBUTES_PROPERTIES_NVX:
        {
            handlemap_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(handlemap, reinterpret_cast<VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_viewport_swizzle
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_SWIZZLE_STATE_CREATE_INFO_NV:
        {
            handlemap_VkPipelineViewportSwizzleStateCreateInfoNV(handlemap, reinterpret_cast<VkPipelineViewportSwizzleStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_discard_rectangles
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DISCARD_RECTANGLE_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceDiscardRectanglePropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceDiscardRectanglePropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_DISCARD_RECTANGLE_STATE_CREATE_INFO_EXT:
        {
            handlemap_VkPipelineDiscardRectangleStateCreateInfoEXT(handlemap, reinterpret_cast<VkPipelineDiscardRectangleStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conservative_rasterization
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONSERVATIVE_RASTERIZATION_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceConservativeRasterizationPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_CONSERVATIVE_STATE_CREATE_INFO_EXT:
        {
            handlemap_VkPipelineRasterizationConservativeStateCreateInfoEXT(handlemap, reinterpret_cast<VkPipelineRasterizationConservativeStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_utils
        case VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT:
        {
            handlemap_VkDebugUtilsMessengerCreateInfoEXT(handlemap, reinterpret_cast<VkDebugUtilsMessengerCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_USAGE_ANDROID:
        {
            handlemap_VkAndroidHardwareBufferUsageANDROID(handlemap, reinterpret_cast<VkAndroidHardwareBufferUsageANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_FORMAT_PROPERTIES_ANDROID:
        {
            handlemap_VkAndroidHardwareBufferFormatPropertiesANDROID(handlemap, reinterpret_cast<VkAndroidHardwareBufferFormatPropertiesANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_ANDROID_HARDWARE_BUFFER_INFO_ANDROID:
        {
            handlemap_VkImportAndroidHardwareBufferInfoANDROID(handlemap, reinterpret_cast<VkImportAndroidHardwareBufferInfoANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_FORMAT_ANDROID:
        {
            handlemap_VkExternalFormatANDROID(handlemap, reinterpret_cast<VkExternalFormatANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sampler_filter_minmax
        case VK_STRUCTURE_TYPE_SAMPLER_REDUCTION_MODE_CREATE_INFO_EXT:
        {
            handlemap_VkSamplerReductionModeCreateInfoEXT(handlemap, reinterpret_cast<VkSamplerReductionModeCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_FILTER_MINMAX_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sample_locations
        case VK_STRUCTURE_TYPE_SAMPLE_LOCATIONS_INFO_EXT:
        {
            handlemap_VkSampleLocationsInfoEXT(handlemap, reinterpret_cast<VkSampleLocationsInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_SAMPLE_LOCATIONS_BEGIN_INFO_EXT:
        {
            handlemap_VkRenderPassSampleLocationsBeginInfoEXT(handlemap, reinterpret_cast<VkRenderPassSampleLocationsBeginInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_SAMPLE_LOCATIONS_STATE_CREATE_INFO_EXT:
        {
            handlemap_VkPipelineSampleLocationsStateCreateInfoEXT(handlemap, reinterpret_cast<VkPipelineSampleLocationsStateCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLE_LOCATIONS_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceSampleLocationsPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceSampleLocationsPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_blend_operation_advanced
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_FEATURES_EXT:
        {
            handlemap_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_ADVANCED_STATE_CREATE_INFO_EXT:
        {
            handlemap_VkPipelineColorBlendAdvancedStateCreateInfoEXT(handlemap, reinterpret_cast<VkPipelineColorBlendAdvancedStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_fragment_coverage_to_color
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_TO_COLOR_STATE_CREATE_INFO_NV:
        {
            handlemap_VkPipelineCoverageToColorStateCreateInfoNV(handlemap, reinterpret_cast<VkPipelineCoverageToColorStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_framebuffer_mixed_samples
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_MODULATION_STATE_CREATE_INFO_NV:
        {
            handlemap_VkPipelineCoverageModulationStateCreateInfoNV(handlemap, reinterpret_cast<VkPipelineCoverageModulationStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_cache
        case VK_STRUCTURE_TYPE_SHADER_MODULE_VALIDATION_CACHE_CREATE_INFO_EXT:
        {
            handlemap_VkShaderModuleValidationCacheCreateInfoEXT(handlemap, reinterpret_cast<VkShaderModuleValidationCacheCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_descriptor_indexing
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_BINDING_FLAGS_CREATE_INFO_EXT:
        {
            handlemap_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(handlemap, reinterpret_cast<VkDescriptorSetLayoutBindingFlagsCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_FEATURES_EXT:
        {
            handlemap_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceDescriptorIndexingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceDescriptorIndexingPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_ALLOCATE_INFO_EXT:
        {
            handlemap_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(handlemap, reinterpret_cast<VkDescriptorSetVariableDescriptorCountAllocateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_LAYOUT_SUPPORT_EXT:
        {
            handlemap_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(handlemap, reinterpret_cast<VkDescriptorSetVariableDescriptorCountLayoutSupportEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_global_priority
        case VK_STRUCTURE_TYPE_DEVICE_QUEUE_GLOBAL_PRIORITY_CREATE_INFO_EXT:
        {
            handlemap_VkDeviceQueueGlobalPriorityCreateInfoEXT(handlemap, reinterpret_cast<VkDeviceQueueGlobalPriorityCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_external_memory_host
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_HOST_POINTER_INFO_EXT:
        {
            handlemap_VkImportMemoryHostPointerInfoEXT(handlemap, reinterpret_cast<VkImportMemoryHostPointerInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_MEMORY_HOST_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceExternalMemoryHostPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_shader_core_properties
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_CORE_PROPERTIES_AMD:
        {
            handlemap_VkPhysicalDeviceShaderCorePropertiesAMD(handlemap, reinterpret_cast<VkPhysicalDeviceShaderCorePropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_vertex_attribute_divisor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VERTEX_ATTRIBUTE_DIVISOR_PROPERTIES_EXT:
        {
            handlemap_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(handlemap, reinterpret_cast<VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_DIVISOR_STATE_CREATE_INFO_EXT:
        {
            handlemap_VkPipelineVertexInputDivisorStateCreateInfoEXT(handlemap, reinterpret_cast<VkPipelineVertexInputDivisorStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
        case VK_STRUCTURE_TYPE_QUEUE_FAMILY_CHECKPOINT_PROPERTIES_NV:
        {
            handlemap_VkQueueFamilyCheckpointPropertiesNV(handlemap, reinterpret_cast<VkQueueFamilyCheckpointPropertiesNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_color_buffer
        case VK_STRUCTURE_TYPE_IMPORT_COLOR_BUFFER_GOOGLE:
        {
            handlemap_VkImportColorBufferGOOGLE(handlemap, reinterpret_cast<VkImportColorBufferGOOGLE*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_PHYSICAL_ADDRESS_GOOGLE:
        {
            handlemap_VkImportPhysicalAddressGOOGLE(handlemap, reinterpret_cast<VkImportPhysicalAddressGOOGLE*>(structExtension_out));
            break;
        }
#endif
        default:
        {
            return;
        }
    }
}


} // namespace goldfish_vk
