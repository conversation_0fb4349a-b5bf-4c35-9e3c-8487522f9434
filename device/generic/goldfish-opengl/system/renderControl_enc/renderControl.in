GL_ENRTY(<PERSON><PERSON><PERSON>, rcGetRendererVersion)
GL_ENTRY(EGLint, rcGetEGLVersion, EGLint *major, EGLint *minor)
GL_ENTRY(EGLint, rcQueryEGLString, EGLenum name, void *buffer, EGLint bufferSize)
GL_ENTRY(EGLint, rcGetGLString, EGLenum name, void *buffer, EGLint bufferSize)
GL_ENTRY(EGLint, rcGetNumConfigs, uint32_t *numAttribs)
GL_ENTRY(EGLint, rcGetConfigs, uint32_t bufSize, GLuint *buffer)
GL_ENTRY(EGLint, rcChooseConfig, EGLint *attribs, uint32_t attribs_size, uint32_t *configs, uint32_t configs_size)
GL_ENTRY(EGLint, rcGetFBParam, EGLint param)
GL_ENTRY(uint32_t, rc<PERSON>reateContext, uint32_t config, uint32_t share, uint32_t glVersion)
GL_ENTRY(void, rcDestroyContext, uint32_t context)
GL_ENTRY(uint32_t, rcCreateWindowSurface, uint32_t config, uint32_t width, uint32_t height)
GL_ENTRY(void, rcDestroyWindowSurface, uint32_t windowSurface)
GL_ENTRY(uint32_t, rcCreateColorBuffer, uint32_t width, uint32_t height, GLenum internalFormat)
GL_ENTRY(void, rcOpenColorBuffer, uint32_t colorbuffer)
GL_ENTRY(void, rcCloseColorBuffer, uint32_t colorbuffer)
GL_ENTRY(void, rcSetWindowColorBuffer, uint32_t windowSurface, uint32_t colorBuffer)
GL_ENTRY(int, rcFlushWindowColorBuffer, uint32_t windowSurface)
GL_ENTRY(EGLint, rcMakeCurrent, uint32_t context, uint32_t drawSurf, uint32_t readSurf)
GL_ENTRY(void, rcFBPost, uint32_t colorBuffer)
GL_ENTRY(void, rcFBSetSwapInterval, EGLint interval)
GL_ENTRY(void, rcBindTexture, uint32_t colorBuffer)
GL_ENTRY(void, rcBindRenderbuffer, uint32_t colorBuffer)
GL_ENTRY(EGLint, rcColorBufferCacheFlush, uint32_t colorbuffer, EGLint postCount,int forRead)
GL_ENTRY(void, rcReadColorBuffer, uint32_t colorbuffer, GLint x, GLint y, GLint width, GLint height, GLenum format, GLenum type, void *pixels)
GL_ENTRY(int, rcUpdateColorBuffer, uint32_t colorbuffer, GLint x, GLint y, GLint width, GLint height, GLenum format, GLenum type, void *pixels)
GL_ENTRY(int, rcOpenColorBuffer2, uint32_t colorbuffer)
GL_ENTRY(uint32_t, rcCreateClientImage, uint32_t context, EGLenum target, GLuint buffer)
GL_ENTRY(int, rcDestroyClientImage, uint32_t image)
GL_ENTRY(void, rcSelectChecksumHelper, uint32_t newProtocol, uint32_t reserved)
GL_ENTRY(uint32_t, rcCreateColorBufferPuid, uint32_t width, uint32_t height, GLenum internalFormat, uint64_t puid)
GL_ENTRY(int, rcOpenColorBuffer2Puid, uint32_t colorbuffer, uint64_t puid)
GL_ENTRY(void, rcCloseColorBufferPuid, uint32_t colorbuffer, uint64_t puid)
GL_ENTRY(void, rcCreateSyncKHR, EGLenum type, EGLint* attribs, uint32_t num_attribs, uint64_t* glsync_out, uint64_t* syncthread_out)
GL_ENTRY(EGLint, rcClientWaitSyncKHR, uint64_t sync, EGLint flags, uint64_t timeout)
GL_ENTRY(void, rcFlushWindowColorBufferAsync, uint32_t windowSurface)
GL_ENTRY(uint32_t, rcCreateClientImagePuid, uint32_t context, EGLenum target, GLuint buffer, uint64_t puid)
GL_ENTRY(int, rcDestroyClientImagePuid, uint32_t image, uint64_t puid)
