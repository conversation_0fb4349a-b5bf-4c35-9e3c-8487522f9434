// Generated Code - DO NOT EDIT !!
// generated by 'emugen'


#include <string.h>
#include "renderControl_opcodes.h"

#include "renderControl_enc.h"


#include <vector>

#include <stdio.h>

namespace {

void enc_unsupported()
{
	ALOGE("Function is unsupported\n");
}

GLint rcGetRendererVersion_enc(void *self )
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetRendererVersion;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetRendererVersion: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcGetEGLVersion_enc(void *self , EGLint* major, EGLint* minor)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_major =  sizeof(EGLint);
	const unsigned int __size_minor =  sizeof(EGLint);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetEGLVersion;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_major; ptr += 4;
	*(unsigned int *)(ptr) = __size_minor; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(major, __size_major);
	if (useChecksum) checksumCalculator->addBuffer(major, __size_major);
	stream->readback(minor, __size_minor);
	if (useChecksum) checksumCalculator->addBuffer(minor, __size_minor);

	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetEGLVersion: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcQueryEGLString_enc(void *self , EGLenum name, void* buffer, EGLint bufferSize)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffer =  bufferSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcQueryEGLString;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &name, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffer; ptr += 4;
		memcpy(ptr, &bufferSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(buffer, __size_buffer);
	if (useChecksum) checksumCalculator->addBuffer(buffer, __size_buffer);

	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcQueryEGLString: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcGetGLString_enc(void *self , EGLenum name, void* buffer, EGLint bufferSize)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffer =  bufferSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetGLString;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &name, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffer; ptr += 4;
		memcpy(ptr, &bufferSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(buffer, __size_buffer);
	if (useChecksum) checksumCalculator->addBuffer(buffer, __size_buffer);

	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetGLString: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcGetNumConfigs_enc(void *self , uint32_t* numAttribs)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_numAttribs =  sizeof(uint32_t);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetNumConfigs;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_numAttribs; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(numAttribs, __size_numAttribs);
	if (useChecksum) checksumCalculator->addBuffer(numAttribs, __size_numAttribs);

	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetNumConfigs: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcGetConfigs_enc(void *self , uint32_t bufSize, GLuint* buffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffer =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetConfigs;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffer; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(buffer, __size_buffer);
	if (useChecksum) checksumCalculator->addBuffer(buffer, __size_buffer);

	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetConfigs: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcChooseConfig_enc(void *self , EGLint* attribs, uint32_t attribs_size, uint32_t* configs, uint32_t configs_size)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_attribs =  attribs_size;
	const unsigned int __size_configs = ((configs != NULL) ?  configs_size*sizeof(uint32_t) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + __size_attribs + 4 + 0 + 4 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcChooseConfig;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_attribs; ptr += 4;
	memcpy(ptr, attribs, __size_attribs);ptr += __size_attribs;
		memcpy(ptr, &attribs_size, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_configs; ptr += 4;
		memcpy(ptr, &configs_size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (configs != NULL) {
		stream->readback(configs, __size_configs);
		if (useChecksum) checksumCalculator->addBuffer(configs, __size_configs);
	}

	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcChooseConfig: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcGetFBParam_enc(void *self , EGLint param)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetFBParam;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetFBParam: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

uint32_t rcCreateContext_enc(void *self , uint32_t config, uint32_t share, uint32_t glVersion)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateContext;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &config, 4); ptr += 4;
		memcpy(ptr, &share, 4); ptr += 4;
		memcpy(ptr, &glVersion, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint32_t retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateContext: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcDestroyContext_enc(void *self , uint32_t context)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcDestroyContext;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &context, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

uint32_t rcCreateWindowSurface_enc(void *self , uint32_t config, uint32_t width, uint32_t height)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateWindowSurface;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &config, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint32_t retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateWindowSurface: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcDestroyWindowSurface_enc(void *self , uint32_t windowSurface)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcDestroyWindowSurface;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &windowSurface, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

uint32_t rcCreateColorBuffer_enc(void *self , uint32_t width, uint32_t height, GLenum internalFormat)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &internalFormat, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint32_t retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateColorBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcOpenColorBuffer_enc(void *self , uint32_t colorbuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcOpenColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void rcCloseColorBuffer_enc(void *self , uint32_t colorbuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCloseColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->flush();
}

void rcSetWindowColorBuffer_enc(void *self , uint32_t windowSurface, uint32_t colorBuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcSetWindowColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &windowSurface, 4); ptr += 4;
		memcpy(ptr, &colorBuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

int rcFlushWindowColorBuffer_enc(void *self , uint32_t windowSurface)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcFlushWindowColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &windowSurface, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcFlushWindowColorBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

EGLint rcMakeCurrent_enc(void *self , uint32_t context, uint32_t drawSurf, uint32_t readSurf)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcMakeCurrent;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &context, 4); ptr += 4;
		memcpy(ptr, &drawSurf, 4); ptr += 4;
		memcpy(ptr, &readSurf, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcMakeCurrent: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcFBPost_enc(void *self , uint32_t colorBuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcFBPost;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorBuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void rcFBSetSwapInterval_enc(void *self , EGLint interval)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcFBSetSwapInterval;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &interval, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void rcBindTexture_enc(void *self , uint32_t colorBuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcBindTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorBuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void rcBindRenderbuffer_enc(void *self , uint32_t colorBuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcBindRenderbuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorBuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

EGLint rcColorBufferCacheFlush_enc(void *self , uint32_t colorbuffer, EGLint postCount, int forRead)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcColorBufferCacheFlush;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;
		memcpy(ptr, &postCount, 4); ptr += 4;
		memcpy(ptr, &forRead, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcColorBufferCacheFlush: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcReadColorBuffer_enc(void *self , uint32_t colorbuffer, GLint x, GLint y, GLint width, GLint height, GLenum format, GLenum type, void* pixels)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels =  (((glUtilsPixelBitSize(format, type) * width) >> 3) * height);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcReadColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_pixels; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(pixels, __size_pixels);
	if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcReadColorBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

int rcUpdateColorBuffer_enc(void *self , uint32_t colorbuffer, GLint x, GLint y, GLint width, GLint height, GLenum format, GLenum type, void* pixels)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels =  (((glUtilsPixelBitSize(format, type) * width) >> 3) * height);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_rcUpdateColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
		stream->writeFully(pixels, __size_pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcUpdateColorBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

int rcOpenColorBuffer2_enc(void *self , uint32_t colorbuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcOpenColorBuffer2;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcOpenColorBuffer2: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

uint32_t rcCreateClientImage_enc(void *self , uint32_t context, EGLenum target, GLuint buffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateClientImage;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &context, 4); ptr += 4;
		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &buffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint32_t retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateClientImage: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

int rcDestroyClientImage_enc(void *self , uint32_t image)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcDestroyClientImage;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &image, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcDestroyClientImage: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcSelectChecksumHelper_enc(void *self , uint32_t newProtocol, uint32_t reserved)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcSelectChecksumHelper;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &newProtocol, 4); ptr += 4;
		memcpy(ptr, &reserved, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void rcCreateSyncKHR_enc(void *self , EGLenum type, EGLint* attribs, uint32_t num_attribs, int destroy_when_signaled, uint64_t* glsync_out, uint64_t* syncthread_out)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_attribs =  num_attribs;
	const unsigned int __size_glsync_out =  sizeof(uint64_t);
	const unsigned int __size_syncthread_out =  sizeof(uint64_t);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_attribs + 4 + 4 + 0 + 0 + 3*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateSyncKHR;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_attribs; ptr += 4;
	memcpy(ptr, attribs, __size_attribs);ptr += __size_attribs;
		memcpy(ptr, &num_attribs, 4); ptr += 4;
		memcpy(ptr, &destroy_when_signaled, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_glsync_out; ptr += 4;
	*(unsigned int *)(ptr) = __size_syncthread_out; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(glsync_out, __size_glsync_out);
	if (useChecksum) checksumCalculator->addBuffer(glsync_out, __size_glsync_out);
	stream->readback(syncthread_out, __size_syncthread_out);
	if (useChecksum) checksumCalculator->addBuffer(syncthread_out, __size_syncthread_out);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateSyncKHR: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

EGLint rcClientWaitSyncKHR_enc(void *self , uint64_t sync, EGLint flags, uint64_t timeout)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8 + 4 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcClientWaitSyncKHR;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sync, 8); ptr += 8;
		memcpy(ptr, &flags, 4); ptr += 4;
		memcpy(ptr, &timeout, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	EGLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcClientWaitSyncKHR: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcFlushWindowColorBufferAsync_enc(void *self , uint32_t windowSurface)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcFlushWindowColorBufferAsync;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &windowSurface, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

int rcDestroySyncKHR_enc(void *self , uint64_t sync)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcDestroySyncKHR;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sync, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcDestroySyncKHR: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcSetPuid_enc(void *self , uint64_t puid)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcSetPuid;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &puid, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

int rcUpdateColorBufferDMA_enc(void *self , uint32_t colorbuffer, GLint x, GLint y, GLint width, GLint height, GLenum format, GLenum type, void* pixels, uint32_t pixels_size)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels =  pixels_size;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 8 + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcUpdateColorBufferDMA;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorbuffer, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(uint64_t *)(ptr) = ctx->lockAndWriteDma(pixels, __size_pixels); ptr += 8;
		memcpy(ptr, &pixels_size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcUpdateColorBufferDMA: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

uint32_t rcCreateColorBufferDMA_enc(void *self , uint32_t width, uint32_t height, GLenum internalFormat, int frameworkFormat)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateColorBufferDMA;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &internalFormat, 4); ptr += 4;
		memcpy(ptr, &frameworkFormat, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint32_t retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateColorBufferDMA: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void rcWaitSyncKHR_enc(void *self , uint64_t sync, EGLint flags)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcWaitSyncKHR;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sync, 8); ptr += 8;
		memcpy(ptr, &flags, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLint rcCompose_enc(void *self , uint32_t bufferSize, void* buffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffer =  bufferSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_buffer + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCompose;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &bufferSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffer; ptr += 4;
	memcpy(ptr, buffer, __size_buffer);ptr += __size_buffer;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCompose: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcCreateDisplay_enc(void *self , uint32_t* displayId)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_displayId =  sizeof(uint32_t);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcCreateDisplay;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

	*(unsigned int *)(ptr) = __size_displayId; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(displayId, __size_displayId);
	if (useChecksum) checksumCalculator->addBuffer(displayId, __size_displayId);

	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcCreateDisplay: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcDestroyDisplay_enc(void *self , uint32_t displayId)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcDestroyDisplay;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &displayId, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcDestroyDisplay: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcSetDisplayColorBuffer_enc(void *self , uint32_t displayId, uint32_t colorBuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcSetDisplayColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &displayId, 4); ptr += 4;
		memcpy(ptr, &colorBuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcSetDisplayColorBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcGetDisplayColorBuffer_enc(void *self , uint32_t displayId, uint32_t* colorBuffer)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_colorBuffer =  sizeof(uint32_t);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetDisplayColorBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &displayId, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_colorBuffer; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(colorBuffer, __size_colorBuffer);
	if (useChecksum) checksumCalculator->addBuffer(colorBuffer, __size_colorBuffer);

	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetDisplayColorBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcGetColorBufferDisplay_enc(void *self , uint32_t colorBuffer, uint32_t* displayId)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_displayId =  sizeof(uint32_t);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetColorBufferDisplay;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &colorBuffer, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_displayId; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(displayId, __size_displayId);
	if (useChecksum) checksumCalculator->addBuffer(displayId, __size_displayId);

	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetColorBufferDisplay: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcGetDisplayPose_enc(void *self , uint32_t displayId, uint32_t* x, uint32_t* y, uint32_t* w, uint32_t* h)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_x =  sizeof(uint32_t);
	const unsigned int __size_y =  sizeof(uint32_t);
	const unsigned int __size_w =  sizeof(uint32_t);
	const unsigned int __size_h =  sizeof(uint32_t);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 0 + 0 + 0 + 4*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcGetDisplayPose;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &displayId, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_x; ptr += 4;
	*(unsigned int *)(ptr) = __size_y; ptr += 4;
	*(unsigned int *)(ptr) = __size_w; ptr += 4;
	*(unsigned int *)(ptr) = __size_h; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(x, __size_x);
	if (useChecksum) checksumCalculator->addBuffer(x, __size_x);
	stream->readback(y, __size_y);
	if (useChecksum) checksumCalculator->addBuffer(y, __size_y);
	stream->readback(w, __size_w);
	if (useChecksum) checksumCalculator->addBuffer(w, __size_w);
	stream->readback(h, __size_h);
	if (useChecksum) checksumCalculator->addBuffer(h, __size_h);

	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcGetDisplayPose: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint rcSetDisplayPose_enc(void *self , uint32_t displayId, uint32_t x, uint32_t y, uint32_t w, uint32_t h)
{

	renderControl_encoder_context_t *ctx = (renderControl_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_rcSetDisplayPose;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &displayId, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &w, 4); ptr += 4;
		memcpy(ptr, &h, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("rcSetDisplayPose: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

}  // namespace

renderControl_encoder_context_t::renderControl_encoder_context_t(IOStream *stream, ChecksumCalculator *checksumCalculator)
{
	m_stream = stream;
	m_checksumCalculator = checksumCalculator;

	this->rcGetRendererVersion = &rcGetRendererVersion_enc;
	this->rcGetEGLVersion = &rcGetEGLVersion_enc;
	this->rcQueryEGLString = &rcQueryEGLString_enc;
	this->rcGetGLString = &rcGetGLString_enc;
	this->rcGetNumConfigs = &rcGetNumConfigs_enc;
	this->rcGetConfigs = &rcGetConfigs_enc;
	this->rcChooseConfig = &rcChooseConfig_enc;
	this->rcGetFBParam = &rcGetFBParam_enc;
	this->rcCreateContext = &rcCreateContext_enc;
	this->rcDestroyContext = &rcDestroyContext_enc;
	this->rcCreateWindowSurface = &rcCreateWindowSurface_enc;
	this->rcDestroyWindowSurface = &rcDestroyWindowSurface_enc;
	this->rcCreateColorBuffer = &rcCreateColorBuffer_enc;
	this->rcOpenColorBuffer = &rcOpenColorBuffer_enc;
	this->rcCloseColorBuffer = &rcCloseColorBuffer_enc;
	this->rcSetWindowColorBuffer = &rcSetWindowColorBuffer_enc;
	this->rcFlushWindowColorBuffer = &rcFlushWindowColorBuffer_enc;
	this->rcMakeCurrent = &rcMakeCurrent_enc;
	this->rcFBPost = &rcFBPost_enc;
	this->rcFBSetSwapInterval = &rcFBSetSwapInterval_enc;
	this->rcBindTexture = &rcBindTexture_enc;
	this->rcBindRenderbuffer = &rcBindRenderbuffer_enc;
	this->rcColorBufferCacheFlush = &rcColorBufferCacheFlush_enc;
	this->rcReadColorBuffer = &rcReadColorBuffer_enc;
	this->rcUpdateColorBuffer = &rcUpdateColorBuffer_enc;
	this->rcOpenColorBuffer2 = &rcOpenColorBuffer2_enc;
	this->rcCreateClientImage = &rcCreateClientImage_enc;
	this->rcDestroyClientImage = &rcDestroyClientImage_enc;
	this->rcSelectChecksumHelper = &rcSelectChecksumHelper_enc;
	this->rcCreateSyncKHR = &rcCreateSyncKHR_enc;
	this->rcClientWaitSyncKHR = &rcClientWaitSyncKHR_enc;
	this->rcFlushWindowColorBufferAsync = &rcFlushWindowColorBufferAsync_enc;
	this->rcDestroySyncKHR = &rcDestroySyncKHR_enc;
	this->rcSetPuid = &rcSetPuid_enc;
	this->rcUpdateColorBufferDMA = &rcUpdateColorBufferDMA_enc;
	this->rcCreateColorBufferDMA = &rcCreateColorBufferDMA_enc;
	this->rcWaitSyncKHR = &rcWaitSyncKHR_enc;
	this->rcCompose = &rcCompose_enc;
	this->rcCreateDisplay = &rcCreateDisplay_enc;
	this->rcDestroyDisplay = &rcDestroyDisplay_enc;
	this->rcSetDisplayColorBuffer = &rcSetDisplayColorBuffer_enc;
	this->rcGetDisplayColorBuffer = &rcGetDisplayColorBuffer_enc;
	this->rcGetColorBufferDisplay = &rcGetColorBufferDisplay_enc;
	this->rcGetDisplayPose = &rcGetDisplayPose_enc;
	this->rcSetDisplayPose = &rcSetDisplayPose_enc;
}

