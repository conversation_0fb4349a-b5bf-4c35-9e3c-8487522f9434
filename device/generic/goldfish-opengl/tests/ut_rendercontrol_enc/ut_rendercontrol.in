GL_ENTRY(int, createContext, uint32_t pid, uint32_t handle, uint32_t shareCtx, int version)
GL_ENTRY(int, createSurface, uint32_t pid, uint32_t handle)
GL_ENTRY(int, makeCurrentContext, uint32_t pid, uint32_t drawSurface, uint32_t readSurface, uint32_t ctxHandle)
GL_ENTRY(void, swapBuffers, uint32_t pid, uint32_t surface)
GL_ENTRY(int, destroyContext, uint32_t pid, uint32_t handle)
GL_ENTRY(int, destroySurface, uint32_t pid, uint32_t handle)





