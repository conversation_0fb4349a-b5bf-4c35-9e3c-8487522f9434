// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __foo_client_context_t_h
#define __foo_client_context_t_h

#include "foo_client_proc.h"

#include "foo_types.h"


struct foo_client_context_t {

	fooAlphaFunc_client_proc_t fooAlphaFunc;
	fooIsBuffer_client_proc_t fooIsBuffer;
	fooUnsupported_client_proc_t fooUnsupported;
	fooDoEncoderFlush_client_proc_t fooDoEncoderFlush;
	fooTakeConstVoidPtrConstPtr_client_proc_t fooTakeConstVoidPtrConstPtr;
	fooSetComplexStruct_client_proc_t fooSetComplexStruct;
	fooGetComplexStruct_client_proc_t fooGetComplexStruct;
	fooInout_client_proc_t fooInout;
	virtual ~foo_client_context_t() {}

	typedef foo_client_context_t *CONTEXT_ACCESSOR_TYPE(void);
	static void setContextAccessor(CONTEXT_ACCESSOR_TYPE *f);
	int initDispatchByName( void *(*getProc)(const char *name, void *userData), void *userData);
	virtual void setError(unsigned int  error){ (void)error; };
	virtual unsigned int getError(){ return 0; };
};

#endif
