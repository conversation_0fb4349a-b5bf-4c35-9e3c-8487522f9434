GLbitfield 32 0x%08x
GLboolean 8 %d
GLclampf 32 %f
GLclampx 32 0x%08x
GLeglImageOES 32 %p
GLenum 32 0x%08x
GLfixed 32 0x%08x
GLfloat 32 %f
GLint 32 %d
GLintptr 32 0x%08lx
GLshort 16 %d
GLsizei 32 %d
GLsizeiptr 32 0x%08lx
GLubyte 8 0x%02x
GLuint 32 %u
GLvoid 0 %x
GLchar 8 %d
GLenum* 32 0x%08x
GLboolean* 32 0x%08x
GLclampf* 32 0x%08x
GLclampx* 32 0x%08x
GLeglImageOES* 32 0x%08x
GLfixed* 32 0x%08x
GLfloat* 32 0x%08x
GLint* 32 0x%08x
GLshort* 32 0x%08x
GLsizei* 32 0x%08x
GLubyte* 32 0x%08x
GLuint* 32 0x%08x
GLvoid* 32 0x%08x
GLchar* 32 0x%08x
GLchar** 32 0x%08x
GLvoid** 32 0x%08x
void* 32 0x%08x
GLstr* 32 0x%08x
GLvoidptr* 32 0x%08x
GLchar*const* 32 0x%08x
GLvoid*const* 32 0x%08x
GLsync 64 %p
uint64_t 64 0x%016lx
GLint32 32 0x%08x
GLint32* 32 %p
GLint64 64 0x%016lx
GLint64* 32 %p
GLuint32 32 0x%08x
GLuint32* 32 %p
GLuint64 64 0x%016lx
GLuint64* 32 %p
char* 32 0x%08x
char** 32 0x%08x
