GL_ENTRY(void, glVertexPointerOffset, GLint size, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glColorPointerOffset, GLint size, G<PERSON>enum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glNormalPointerOffset, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glPointSizePointerOffset, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glTexCoordPointerOffset, GLint size, G<PERSON>enum type, GLsizei stride,  GLuint offset)

GL_ENTRY(void, glVertexPointerData, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glColorPointerData, GLint size, G<PERSON><PERSON><PERSON> type, GLsize<PERSON> stride,  void * data, GLuint datalen)
GL_ENTRY(void, glNormalPointerData, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glTexCoordPointerData, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glPointSizePointerData, GLenum type, GLsizei stride,  void * data, GLuint datalen)

GL_ENTRY(void, glDrawElementsOffset, GLenum mode, GLsizei count, GLenum type, GLuint offset);
GL_ENTRY(void, glDrawElementsData, GLenum mode, GLsizei count, GLenum type, void *data, GLuint datalen);

