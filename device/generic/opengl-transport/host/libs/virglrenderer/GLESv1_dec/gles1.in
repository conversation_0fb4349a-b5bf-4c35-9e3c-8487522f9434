GL_ENTRY(void, glAlphaFunc, GLenum func, GLclampf ref)
GL_ENTRY(void, glClearColor, GLclamp<PERSON> red, <PERSON>L<PERSON>lamp<PERSON> green, GLclamp<PERSON> blue, GLclampf alpha)
GL_ENTRY(void, glClearDepthf, GLclampf depth)
GL_ENTRY(void, glClipPlanef, GLenum plane, const GLfloat *equation)
GL_ENTRY(void, glColor4f, GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha)
GL_ENTRY(void, glDepthRangef, GLclampf zNear, GLclampf zFar)
GL_ENTRY(void, glFogf, GLenum pname, GLfloat param)
GL_ENTRY(void, glFogfv, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glFrustumf, GLfloat left, GLfloat right, GL<PERSON>loat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
GL_ENTRY(void, glGetClipPlanef, GLenum pname, GLfloat* eqn)
GL_ENTRY(void, glGetFloatv, GLenum pname, GLfloat *params)
GL_ENTRY(void, glGetLightfv, GLenum light, GLenum pname, GLfloat *params)
GL_ENTRY(void, glGetMaterialfv, GLenum face, GLenum pname, GLfloat *params)
GL_ENTRY(void, glGetTexEnvfv, GLenum env, GLenum pname, GLfloat *params)
GL_ENTRY(void, glGetTexParameterfv, GLenum target, GLenum pname, GLfloat *params)
GL_ENTRY(void, glLightModelf, GLenum pname, GLfloat param)
GL_ENTRY(void, glLightModelfv, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glLightf, GLenum light, GLenum pname, GLfloat param)
GL_ENTRY(void, glLightfv, GLenum light, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glLineWidth, GLfloat width)
GL_ENTRY(void, glLoadMatrixf, const GLfloat *m)
GL_ENTRY(void, glMaterialf, GLenum face, GLenum pname, GLfloat param)
GL_ENTRY(void, glMaterialfv, GLenum face, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glMultMatrixf, const GLfloat *m)
GL_ENTRY(void, glMultiTexCoord4f, GLenum target, GLfloat s, GLfloat t, GLfloat r, GLfloat q)
GL_ENTRY(void, glNormal3f, GLfloat nx, GLfloat ny, GLfloat nz)
GL_ENTRY(void, glOrthof, GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
GL_ENTRY(void, glPointParameterf, GLenum pname, GLfloat param)
GL_ENTRY(void, glPointParameterfv, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glPointSize, GLfloat size)
GL_ENTRY(void, glPolygonOffset, GLfloat factor, GLfloat units)
GL_ENTRY(void, glRotatef, GLfloat angle, GLfloat x, GLfloat y, GLfloat z)
GL_ENTRY(void, glScalef, GLfloat x, GLfloat y, GLfloat z)
GL_ENTRY(void, glTexEnvf, GLenum target, GLenum pname, GLfloat param)
GL_ENTRY(void, glTexEnvfv, GLenum target, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glTexParameterf, GLenum target, GLenum pname, GLfloat param)
GL_ENTRY(void, glTexParameterfv, GLenum target, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glTranslatef, GLfloat x, GLfloat y, GLfloat z)
GL_ENTRY(void, glActiveTexture, GLenum texture)
GL_ENTRY(void, glAlphaFuncx, GLenum func, GLclampx ref)
GL_ENTRY(void, glBindBuffer, GLenum target, GLuint buffer)
GL_ENTRY(void, glBindTexture, GLenum target, GLuint texture)
GL_ENTRY(void, glBlendFunc, GLenum sfactor, GLenum dfactor)
GL_ENTRY(void, glBufferData, GLenum target, GLsizeiptr size, const GLvoid *data, GLenum usage)
GL_ENTRY(void, glBufferSubData, GLenum target, GLintptr offset, GLsizeiptr size, const GLvoid *data)
GL_ENTRY(void, glClear, GLbitfield mask)
GL_ENTRY(void, glClearColorx, GLclampx red, GLclampx green, GLclampx blue, GLclampx alpha)
GL_ENTRY(void, glClearDepthx, GLclampx depth)
GL_ENTRY(void, glClearStencil, GLint s)
GL_ENTRY(void, glClientActiveTexture, GLenum texture)
GL_ENTRY(void, glColor4ub, GLubyte red, GLubyte green, GLubyte blue, GLubyte alpha)
GL_ENTRY(void, glColor4x, GLfixed red, GLfixed green, GLfixed blue, GLfixed alpha)
GL_ENTRY(void, glColorMask, GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha)
GL_ENTRY(void, glColorPointer, GLint size, GLenum type, GLsizei stride, const GLvoid *pointer)
GL_ENTRY(void, glCompressedTexImage2D, GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, const GLvoid *data)
GL_ENTRY(void, glCompressedTexSubImage2D, GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, const GLvoid *data)
GL_ENTRY(void, glCopyTexImage2D, GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border)
GL_ENTRY(void, glCopyTexSubImage2D, GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height)
GL_ENTRY(void, glCullFace, GLenum mode)
GL_ENTRY(void, glDeleteBuffers, GLsizei n, const GLuint *buffers)
GL_ENTRY(void, glDeleteTextures, GLsizei n, const GLuint *textures)
GL_ENTRY(void, glDepthFunc, GLenum func)
GL_ENTRY(void, glDepthMask, GLboolean flag)
GL_ENTRY(void, glDepthRangex, GLclampx zNear, GLclampx zFar)
GL_ENTRY(void, glDisable, GLenum cap)
GL_ENTRY(void, glDisableClientState, GLenum array)
GL_ENTRY(void, glDrawArrays, GLenum mode, GLint first, GLsizei count)
GL_ENTRY(void, glDrawElements, GLenum mode, GLsizei count, GLenum type, const GLvoid *indices)
GL_ENTRY(void, glEnable, GLenum cap)
GL_ENTRY(void, glEnableClientState, GLenum array)
GL_ENTRY(void, glFinish, void)
GL_ENTRY(void, glFlush, void)
GL_ENTRY(void, glFogx, GLenum pname, GLfixed param)
GL_ENTRY(void, glFogxv, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glFrontFace, GLenum mode)
GL_ENTRY(void, glFrustumx, GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
GL_ENTRY(void, glGetBooleanv, GLenum pname, GLboolean *params)
GL_ENTRY(void, glGetBufferParameteriv, GLenum target, GLenum pname, GLint *params)
GL_ENTRY(void, glClipPlanex, GLenum pname, const GLfixed * eqn)
GL_ENTRY(void, glGenBuffers, GLsizei n, GLuint *buffers)
GL_ENTRY(void, glGenTextures, GLsizei n, GLuint *textures)
GL_ENTRY(GLenum, glGetError, void)
GL_ENTRY(void, glGetFixedv, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetIntegerv, GLenum pname, GLint *params)
GL_ENTRY(void, glGetLightxv, GLenum light, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetMaterialxv, GLenum face, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetPointerv, GLenum pname, GLvoid **params)
GL_ENTRY(const GLubyte *, glGetString, GLenum name)
GL_ENTRY(void, glGetTexEnviv, GLenum env, GLenum pname, GLint *params)
GL_ENTRY(void, glGetTexEnvxv, GLenum env, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetTexParameteriv, GLenum target, GLenum pname, GLint *params)
GL_ENTRY(void, glGetTexParameterxv, GLenum target, GLenum pname, GLfixed *params)
GL_ENTRY(void, glHint, GLenum target, GLenum mode)
GL_ENTRY(GLboolean, glIsBuffer, GLuint buffer)
GL_ENTRY(GLboolean, glIsEnabled, GLenum cap)
GL_ENTRY(GLboolean, glIsTexture, GLuint texture)
GL_ENTRY(void, glLightModelx, GLenum pname, GLfixed param)
GL_ENTRY(void, glLightModelxv, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glLightx, GLenum light, GLenum pname, GLfixed param)
GL_ENTRY(void, glLightxv, GLenum light, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glLineWidthx, GLfixed width)
GL_ENTRY(void, glLoadIdentity, void)
GL_ENTRY(void, glLoadMatrixx, const GLfixed *m)
GL_ENTRY(void, glLogicOp, GLenum opcode)
GL_ENTRY(void, glMaterialx, GLenum face, GLenum pname, GLfixed param)
GL_ENTRY(void, glMaterialxv, GLenum face, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glMatrixMode, GLenum mode)
GL_ENTRY(void, glMultMatrixx, const GLfixed *m)
GL_ENTRY(void, glMultiTexCoord4x, GLenum target, GLfixed s, GLfixed t, GLfixed r, GLfixed q)
GL_ENTRY(void, glNormal3x, GLfixed nx, GLfixed ny, GLfixed nz)
GL_ENTRY(void, glNormalPointer, GLenum type, GLsizei stride, const GLvoid *pointer)
GL_ENTRY(void, glOrthox, GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
GL_ENTRY(void, glPixelStorei, GLenum pname, GLint param)
GL_ENTRY(void, glPointParameterx, GLenum pname, GLfixed param)
GL_ENTRY(void, glPointParameterxv, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glPointSizex, GLfixed size)
GL_ENTRY(void, glPolygonOffsetx, GLfixed factor, GLfixed units)
GL_ENTRY(void, glPopMatrix, void)
GL_ENTRY(void, glPushMatrix, void)
GL_ENTRY(void, glReadPixels, GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLvoid *pixels)
GL_ENTRY(void, glRotatex, GLfixed angle, GLfixed x, GLfixed y, GLfixed z)
GL_ENTRY(void, glSampleCoverage, GLclampf value, GLboolean invert)
GL_ENTRY(void, glSampleCoveragex, GLclampx value, GLboolean invert)
GL_ENTRY(void, glScalex, GLfixed x, GLfixed y, GLfixed z)
GL_ENTRY(void, glScissor, GLint x, GLint y, GLsizei width, GLsizei height)
GL_ENTRY(void, glShadeModel, GLenum mode)
GL_ENTRY(void, glStencilFunc, GLenum func, GLint ref, GLuint mask)
GL_ENTRY(void, glStencilMask, GLuint mask)
GL_ENTRY(void, glStencilOp, GLenum fail, GLenum zfail, GLenum zpass)
GL_ENTRY(void, glTexCoordPointer, GLint size, GLenum type, GLsizei stride, const GLvoid *pointer)
GL_ENTRY(void, glTexEnvi, GLenum target, GLenum pname, GLint param)
GL_ENTRY(void, glTexEnvx, GLenum target, GLenum pname, GLfixed param)
GL_ENTRY(void, glTexEnviv, GLenum target, GLenum pname, const GLint *params)
GL_ENTRY(void, glTexEnvxv, GLenum target, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glTexImage2D, GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, const GLvoid *pixels)
GL_ENTRY(void, glTexParameteri, GLenum target, GLenum pname, GLint param)
GL_ENTRY(void, glTexParameterx, GLenum target, GLenum pname, GLfixed param)
GL_ENTRY(void, glTexParameteriv, GLenum target, GLenum pname, const GLint *params)
GL_ENTRY(void, glTexParameterxv, GLenum target, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glTexSubImage2D, GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, const GLvoid *pixels)
GL_ENTRY(void, glTranslatex, GLfixed x, GLfixed y, GLfixed z)
GL_ENTRY(void, glVertexPointer, GLint size, GLenum type, GLsizei stride, const GLvoid *pointer)
GL_ENTRY(void, glViewport, GLint x, GLint y, GLsizei width, GLsizei height)
GL_ENTRY(void, glPointSizePointerOES, GLenum type, GLsizei stride, const GLvoid *pointer)

GL_ENTRY(void, glVertexPointerOffset, GLint size, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glColorPointerOffset, GLint size, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glNormalPointerOffset, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glPointSizePointerOffset, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glTexCoordPointerOffset, GLint size, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glWeightPointerOffset, GLint size, GLenum type, GLsizei stride,  GLuint offset)
GL_ENTRY(void, glMatrixIndexPointerOffset, GLint size, GLenum type, GLsizei stride,  GLuint offset)

GL_ENTRY(void, glVertexPointerData, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glColorPointerData, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glNormalPointerData, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glTexCoordPointerData, GLint unit, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glPointSizePointerData, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glWeightPointerData, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)
GL_ENTRY(void, glMatrixIndexPointerData, GLint size, GLenum type, GLsizei stride,  void * data, GLuint datalen)

GL_ENTRY(void, glDrawElementsOffset, GLenum mode, GLsizei count, GLenum type, GLuint offset)
GL_ENTRY(void, glDrawElementsData, GLenum mode, GLsizei count, GLenum type, void *data, GLuint datalen)
GL_ENTRY(void, glGetCompressedTextureFormats, int count, GLint *formats);

GL_ENTRY(int, glFinishRoundTrip, void)

#opengl extensions

GL_ENTRY(void, glBlendEquationSeparateOES, GLenum modeRGB, GLenum modeAlpha)
GL_ENTRY(void, glBlendFuncSeparateOES, GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha)
GL_ENTRY(void, glBlendEquationOES, GLenum mode)
GL_ENTRY(void, glDrawTexsOES, GLshort x, GLshort y, GLshort z, GLshort width, GLshort height)
GL_ENTRY(void, glDrawTexiOES, GLint x, GLint y, GLint z, GLint width, GLint height)
GL_ENTRY(void, glDrawTexxOES, GLfixed x, GLfixed y, GLfixed z, GLfixed width, GLfixed height)
GL_ENTRY(void, glDrawTexsvOES, const GLshort *coords)
GL_ENTRY(void, glDrawTexivOES, const GLint *coords)
GL_ENTRY(void, glDrawTexxvOES, const GLfixed *coords)
GL_ENTRY(void, glDrawTexfOES, GLfloat x, GLfloat y, GLfloat z, GLfloat width, GLfloat height)
GL_ENTRY(void, glDrawTexfvOES, const GLfloat *coords)
GL_ENTRY(void, glEGLImageTargetTexture2DOES, GLenum target, GLeglImageOES image)
GL_ENTRY(void, glEGLImageTargetRenderbufferStorageOES, GLenum target, GLeglImageOES image)
GL_ENTRY(void, glAlphaFuncxOES, GLenum func, GLclampx ref)
GL_ENTRY(void, glClearColorxOES, GLclampx red, GLclampx green, GLclampx blue, GLclampx alpha)
GL_ENTRY(void, glClearDepthxOES, GLclampx depth)
GL_ENTRY(void, glClipPlanexOES, GLenum plane, const GLfixed * equation)
GL_ENTRY(void, glClipPlanexIMG, GLenum plane, const GLfixed * equation)
GL_ENTRY(void, glColor4xOES, GLfixed red, GLfixed green, GLfixed blue, GLfixed alpha)
GL_ENTRY(void, glDepthRangexOES, GLclampx zNear, GLclampx zFar)
GL_ENTRY(void, glFogxOES, GLenum pname, GLfixed param)
GL_ENTRY(void, glFogxvOES, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glFrustumxOES, GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
GL_ENTRY(void, glGetClipPlanexOES, GLenum pname, GLfixed* eqn)
GL_ENTRY(void, glGetClipPlanex, GLenum pname, GLfixed* eqn)
GL_ENTRY(void, glGetFixedvOES, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetLightxvOES, GLenum light, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetMaterialxvOES, GLenum face, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetTexEnvxvOES, GLenum env, GLenum pname, GLfixed *params)
GL_ENTRY(void, glGetTexParameterxvOES, GLenum target, GLenum pname, GLfixed *params)
GL_ENTRY(void, glLightModelxOES, GLenum pname, GLfixed param)
GL_ENTRY(void, glLightModelxvOES, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glLightxOES, GLenum light, GLenum pname, GLfixed param)
GL_ENTRY(void, glLightxvOES, GLenum light, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glLineWidthxOES, GLfixed width)
GL_ENTRY(void, glLoadMatrixxOES, const GLfixed *m)
GL_ENTRY(void, glMaterialxOES, GLenum face, GLenum pname, GLfixed param)
GL_ENTRY(void, glMaterialxvOES, GLenum face, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glMultMatrixxOES, const GLfixed *m)
GL_ENTRY(void, glMultiTexCoord4xOES, GLenum target, GLfixed s, GLfixed t, GLfixed r, GLfixed q)
GL_ENTRY(void, glNormal3xOES, GLfixed nx, GLfixed ny, GLfixed nz)
GL_ENTRY(void, glOrthoxOES, GLfixed left, GLfixed right, GLfixed bottom, GLfixed top, GLfixed zNear, GLfixed zFar)
GL_ENTRY(void, glPointParameterxOES, GLenum pname, GLfixed param)
GL_ENTRY(void, glPointParameterxvOES, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glPointSizexOES, GLfixed size)
GL_ENTRY(void, glPolygonOffsetxOES, GLfixed factor, GLfixed units)
GL_ENTRY(void, glRotatexOES, GLfixed angle, GLfixed x, GLfixed y, GLfixed z)
GL_ENTRY(void, glSampleCoveragexOES, GLclampx value, GLboolean invert)
GL_ENTRY(void, glScalexOES, GLfixed x, GLfixed y, GLfixed z)
GL_ENTRY(void, glTexEnvxOES, GLenum target, GLenum pname, GLfixed param)
GL_ENTRY(void, glTexEnvxvOES, GLenum target, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glTexParameterxOES, GLenum target, GLenum pname, GLfixed param)
GL_ENTRY(void, glTexParameterxvOES, GLenum target, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glTranslatexOES, GLfixed x, GLfixed y, GLfixed z)
GL_ENTRY(GLboolean, glIsRenderbufferOES, GLuint renderbuffer)
GL_ENTRY(void, glBindRenderbufferOES, GLenum target, GLuint renderbuffer)
GL_ENTRY(void, glDeleteRenderbuffersOES, GLsizei n, const GLuint* renderbuffers)
GL_ENTRY(void, glGenRenderbuffersOES, GLsizei n, GLuint* renderbuffers)
GL_ENTRY(void, glRenderbufferStorageOES, GLenum target, GLenum internalformat, GLsizei width, GLsizei height)
GL_ENTRY(void, glGetRenderbufferParameterivOES, GLenum target, GLenum pname, GLint* params)
GL_ENTRY(GLboolean, glIsFramebufferOES, GLuint framebuffer)
GL_ENTRY(void, glBindFramebufferOES, GLenum target, GLuint framebuffer)
GL_ENTRY(void, glDeleteFramebuffersOES, GLsizei n, const GLuint* framebuffers)
GL_ENTRY(void, glGenFramebuffersOES, GLsizei n, GLuint* framebuffers)
GL_ENTRY(GLenum, glCheckFramebufferStatusOES, GLenum target)
GL_ENTRY(void, glFramebufferRenderbufferOES, GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer)
GL_ENTRY(void, glFramebufferTexture2DOES, GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level)
GL_ENTRY(void, glGetFramebufferAttachmentParameterivOES, GLenum target, GLenum attachment, GLenum pname, GLint* params)
GL_ENTRY(void, glGenerateMipmapOES, GLenum target)
GL_ENTRY(void*, glMapBufferOES, GLenum target, GLenum access)
GL_ENTRY(GLboolean, glUnmapBufferOES, GLenum target)
GL_ENTRY(void, glGetBufferPointervOES, GLenum target, GLenum pname, GLvoid* *params)
GL_ENTRY(void, glCurrentPaletteMatrixOES, GLuint matrixpaletteindex)
GL_ENTRY(void, glLoadPaletteFromModelViewMatrixOES, void)
GL_ENTRY(void, glMatrixIndexPointerOES, GLint size, GLenum type, GLsizei stride, const GLvoid * pointer)
GL_ENTRY(void, glWeightPointerOES, GLint size, GLenum type, GLsizei stride, const GLvoid * pointer)
GL_ENTRY(GLbitfield, glQueryMatrixxOES, GLfixed * mantissa, GLint * exponent)
GL_ENTRY(void, glDepthRangefOES, GLclampf zNear, GLclampf zFar)
GL_ENTRY(void, glFrustumfOES, GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
GL_ENTRY(void, glOrthofOES, GLfloat left, GLfloat right, GLfloat bottom, GLfloat top, GLfloat zNear, GLfloat zFar)
GL_ENTRY(void, glClipPlanefOES, GLenum plane, const GLfloat *equation)
GL_ENTRY(void, glClipPlanefIMG, GLenum plane, const GLfloat *equation)
GL_ENTRY(void, glGetClipPlanefOES, GLenum pname, GLfloat * eqn)
GL_ENTRY(void, glClearDepthfOES, GLclampf depth)
GL_ENTRY(void, glTexGenfOES, GLenum coord, GLenum pname, GLfloat param)
GL_ENTRY(void, glTexGenfvOES, GLenum coord, GLenum pname, const GLfloat *params)
GL_ENTRY(void, glTexGeniOES, GLenum coord, GLenum pname, GLint param)
GL_ENTRY(void, glTexGenivOES, GLenum coord, GLenum pname, const GLint *params)
GL_ENTRY(void, glTexGenxOES, GLenum coord, GLenum pname, GLfixed param)
GL_ENTRY(void, glTexGenxvOES, GLenum coord, GLenum pname, const GLfixed *params)
GL_ENTRY(void, glGetTexGenfvOES, GLenum coord, GLenum pname, GLfloat *params)
GL_ENTRY(void, glGetTexGenivOES, GLenum coord, GLenum pname, GLint *params)
GL_ENTRY(void, glGetTexGenxvOES, GLenum coord, GLenum pname, GLfixed *params)
GL_ENTRY(void, glBindVertexArrayOES, GLuint array)
GL_ENTRY(void, glDeleteVertexArraysOES, GLsizei n, const GLuint *arrays)
GL_ENTRY(void, glGenVertexArraysOES, GLsizei n, GLuint *arrays)
GL_ENTRY(GLboolean, glIsVertexArrayOES, GLuint array)
GL_ENTRY(void, glDiscardFramebufferEXT, GLenum target, GLsizei numAttachments, const GLenum *attachments)
GL_ENTRY(void, glMultiDrawArraysEXT, GLenum mode, const GLint *first, const GLsizei *count, GLsizei primcount)
GL_ENTRY(void, glMultiDrawElementsEXT, GLenum mode, const GLsizei *count, GLenum type, const GLvoid*const *indices, GLsizei primcount)
GL_ENTRY(void, glMultiDrawArraysSUN, GLenum mode, GLint *first, GLsizei *count, GLsizei primcount)
GL_ENTRY(void, glMultiDrawElementsSUN, GLenum mode, const GLsizei *count, GLenum type, const GLvoid **indices, GLsizei primcount)
GL_ENTRY(void, glRenderbufferStorageMultisampleIMG, GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height)
GL_ENTRY(void, glFramebufferTexture2DMultisampleIMG, GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLsizei samples)
GL_ENTRY(void, glDeleteFencesNV, GLsizei n, const GLuint *fences)
GL_ENTRY(void, glGenFencesNV, GLsizei n, GLuint *fences)
GL_ENTRY(GLboolean, glIsFenceNV, GLuint fence)
GL_ENTRY(GLboolean, glTestFenceNV, GLuint fence)
GL_ENTRY(void, glGetFenceivNV, GLuint fence, GLenum pname, GLint *params)
GL_ENTRY(void, glFinishFenceNV, GLuint fence)
GL_ENTRY(void, glSetFenceNV, GLuint fence, GLenum condition)
GL_ENTRY(void, glGetDriverControlsQCOM, GLint *num, GLsizei size, GLuint *driverControls)
GL_ENTRY(void, glGetDriverControlStringQCOM, GLuint driverControl, GLsizei bufSize, GLsizei *length, GLchar *driverControlString)
GL_ENTRY(void, glEnableDriverControlQCOM, GLuint driverControl)
GL_ENTRY(void, glDisableDriverControlQCOM, GLuint driverControl)
GL_ENTRY(void, glExtGetTexturesQCOM, GLuint *textures, GLint maxTextures, GLint *numTextures)
GL_ENTRY(void, glExtGetBuffersQCOM, GLuint *buffers, GLint maxBuffers, GLint *numBuffers)
GL_ENTRY(void, glExtGetRenderbuffersQCOM, GLuint * renderbuffers, GLint maxRenderbuffers, GLint * numRenderbuffers)
GL_ENTRY(void, glExtGetFramebuffersQCOM, GLuint *framebuffers, GLint maxFramebuffers, GLint *numFramebuffers)
GL_ENTRY(void, glExtGetTexLevelParameterivQCOM, GLuint texture, GLenum face, GLint level, GLenum pname, GLint *params)
GL_ENTRY(void, glExtTexObjectStateOverrideiQCOM, GLenum target, GLenum pname, GLint param)
GL_ENTRY(void, glExtGetTexSubImageQCOM, GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLvoid *texels)
GL_ENTRY(void, glExtGetBufferPointervQCOM, GLenum target, GLvoid* *params)
GL_ENTRY(void, glExtGetShadersQCOM, GLuint *shaders, GLint maxShaders, GLint *numShaders)
GL_ENTRY(void, glExtGetProgramsQCOM, GLuint *programs, GLint maxPrograms, GLint *numPrograms)
GL_ENTRY(GLboolean, glExtIsProgramBinaryQCOM, GLuint program)
GL_ENTRY(void, glExtGetProgramBinarySourceQCOM, GLuint program, GLenum shadertype, GLchar *source, GLint *length)
GL_ENTRY(void, glStartTilingQCOM, GLuint x, GLuint y, GLuint width, GLuint height, GLbitfield preserveMask)
GL_ENTRY(void, glEndTilingQCOM, GLbitfield preserveMask)
