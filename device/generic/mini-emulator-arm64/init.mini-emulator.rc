on init
    symlink /sdcard /storage/sdcard0

    mount debugfs debugfs /sys/kernel/debug

on post-fs-data
    # Set indication (checked by vold) that we have finished this action
    setprop vold.post_fs_data_done 1
    # to force to start sdcard
    class_start late_start

on boot
    setprop ARGH ARGH
    setprop net.eth0.gw ********
    setprop net.eth0.dns1 ********
    setprop net.dns1 ********
    setprop net.gprs.local-ip *********
    setprop ro.radio.use-ppp no
    setprop ro.build.product generic
    setprop ro.product.device generic

# fake some battery state
    setprop status.battery.state Slow
    setprop status.battery.level 5
    setprop status.battery.level_raw  50
    setprop status.battery.level_scale 9

# disable some daemons the emulator doesn't want
    stop dund
    stop akmd

# start essential services
    start qemud
    start goldfish-logcat
    start goldfish-setup

    setprop ro.setupwizard.mode EMULATOR

# enable Google-specific location features,
# like NetworkLocationProvider and LocationCollector
    setprop ro.com.google.locationfeatures 1

# For the emulator, which bypasses Setup Wizard, you can specify
# account info for the device via these two properties.  Google
# Login Service will insert these accounts into the database when
# it is created (ie, after a data wipe).
#
#   setprop ro.config.hosted_account <EMAIL>:password
#   setprop ro.config.google_account <EMAIL>:password
#
# You MUST have a Google account on the device, and you MAY
# additionally have a hosted account.  No other configuration is
# supported, and arbitrary breakage may result if you specify
# something else.

on fs
        mount_all /fstab.goldfish

service goldfish-setup /system/etc/init.goldfish.sh
    user root
    group root
    oneshot

# The qemu-props program is used to set various system
# properties on boot. It must be run early during the boot
# process to avoid race conditions with other daemons that
# might read them (e.g. surface flinger), so define it in
# class 'core'
#
service qemu-props /system/bin/qemu-props
    class core
    user root
    group root
    oneshot

service qemud /system/bin/qemud
    socket qemud    stream 666
    oneshot

# -Q is a special logcat option that forces the
# program to check wether it runs on the emulator
# if it does, it redirects its output to the device
# named by the androidboot.console kernel option
# if not, is simply exits immediately

service goldfish-logcat /system/bin/logcat -Q
    oneshot
